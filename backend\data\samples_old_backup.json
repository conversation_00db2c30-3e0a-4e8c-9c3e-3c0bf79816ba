[{"id": 1, "sample_id": "S00001", "patient_id": 29, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-04-24", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-04-24T09:47:59.970381", "updated_at": "2025-04-24T09:47:59.970381", "tenant_id": 1, "collected_by": 1}, {"id": 2, "sample_id": "S00002", "patient_id": 25, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-04", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-05-04T09:47:59.970388", "updated_at": "2025-05-04T09:47:59.970388", "tenant_id": 1, "collected_by": 1}, {"id": 3, "sample_id": "S00003", "patient_id": 32, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-01", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-05-01T09:47:59.970393", "updated_at": "2025-05-01T09:47:59.970393", "tenant_id": 2, "collected_by": 3}, {"id": 4, "sample_id": "S00004", "patient_id": 22, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-11", "collection_time": "09:47:59", "status": "Received", "created_at": "2025-05-11T09:47:59.970398", "updated_at": "2025-05-11T09:47:59.970398", "tenant_id": 3, "collected_by": 2}, {"id": 5, "sample_id": "S00005", "patient_id": 13, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-04-22", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-04-22T09:47:59.970402", "updated_at": "2025-04-22T09:47:59.970402", "tenant_id": 1, "collected_by": 3}, {"id": 6, "sample_id": "S00006", "patient_id": 41, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-06", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-05-06T09:47:59.970407", "updated_at": "2025-05-06T09:47:59.970407", "tenant_id": 3, "collected_by": 1}, {"id": 7, "sample_id": "S00007", "patient_id": 22, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-04-29", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-04-29T09:47:59.970411", "updated_at": "2025-04-29T09:47:59.970411", "tenant_id": 2, "collected_by": 3}, {"id": 8, "sample_id": "S00008", "patient_id": 9, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-10", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-10T09:47:59.970416", "updated_at": "2025-05-10T09:47:59.970416", "tenant_id": 2, "collected_by": 1}, {"id": 9, "sample_id": "S00009", "patient_id": 1, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-04-26", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-04-26T09:47:59.970421", "updated_at": "2025-04-26T09:47:59.970421", "tenant_id": 3, "collected_by": 1}, {"id": 10, "sample_id": "S00010", "patient_id": 7, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-04-28", "collection_time": "09:47:59", "status": "In Transit", "created_at": "2025-04-28T09:47:59.970426", "updated_at": "2025-04-28T09:47:59.970426", "tenant_id": 3, "collected_by": 2}, {"id": 11, "sample_id": "S00011", "patient_id": 4, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-18", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-05-18T09:47:59.970430", "updated_at": "2025-05-18T09:47:59.970430", "tenant_id": 3, "collected_by": 2}, {"id": 12, "sample_id": "S00012", "patient_id": 43, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-04-26", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-04-26T09:47:59.970435", "updated_at": "2025-04-26T09:47:59.970435", "tenant_id": 1, "collected_by": 1}, {"id": 13, "sample_id": "S00013", "patient_id": 3, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-08", "collection_time": "09:47:59", "status": "Received", "created_at": "2025-05-08T09:47:59.970440", "updated_at": "2025-05-08T09:47:59.970440", "tenant_id": 3, "collected_by": 2}, {"id": 14, "sample_id": "S00014", "patient_id": 31, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-15", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-15T09:47:59.970444", "updated_at": "2025-05-15T09:47:59.970444", "tenant_id": 1, "collected_by": 2}, {"id": 15, "sample_id": "S00015", "patient_id": 1, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-04", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-05-04T09:47:59.970448", "updated_at": "2025-05-04T09:47:59.970448", "tenant_id": 2, "collected_by": 1}, {"id": 16, "sample_id": "S00016", "patient_id": 2, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-04-28", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-04-28T09:47:59.970455", "updated_at": "2025-04-28T09:47:59.970455", "tenant_id": 2, "collected_by": 1}, {"id": 17, "sample_id": "S00017", "patient_id": 9, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-01", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-01T09:47:59.970461", "updated_at": "2025-05-01T09:47:59.970461", "tenant_id": 2, "collected_by": 2}, {"id": 18, "sample_id": "S00018", "patient_id": 27, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-07", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-07T09:47:59.970466", "updated_at": "2025-05-07T09:47:59.970466", "tenant_id": 3, "collected_by": 2}, {"id": 19, "sample_id": "S00019", "patient_id": 3, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-07", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-07T09:47:59.970470", "updated_at": "2025-05-07T09:47:59.970470", "tenant_id": 2, "collected_by": 2}, {"id": 20, "sample_id": "S00020", "patient_id": 7, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-22", "collection_time": "09:47:59", "status": "In Transit", "created_at": "2025-05-22T09:47:59.970474", "updated_at": "2025-05-22T09:47:59.970474", "tenant_id": 1, "collected_by": 1}, {"id": 21, "sample_id": "S00021", "patient_id": 1, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-04-24", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-04-24T09:47:59.970479", "updated_at": "2025-04-24T09:47:59.970479", "tenant_id": 3, "collected_by": 3}, {"id": 22, "sample_id": "S00022", "patient_id": 19, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-13", "collection_time": "09:47:59", "status": "Received", "created_at": "2025-05-13T09:47:59.970483", "updated_at": "2025-05-13T09:47:59.970483", "tenant_id": 3, "collected_by": 2}, {"id": 23, "sample_id": "S00023", "patient_id": 29, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-13", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-05-13T09:47:59.970488", "updated_at": "2025-05-13T09:47:59.970488", "tenant_id": 1, "collected_by": 3}, {"id": 24, "sample_id": "S00024", "patient_id": 23, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-01", "collection_time": "09:47:59", "status": "In Transit", "created_at": "2025-05-01T09:47:59.970492", "updated_at": "2025-05-01T09:47:59.970492", "tenant_id": 2, "collected_by": 2}, {"id": 25, "sample_id": "S00025", "patient_id": 12, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-22", "collection_time": "09:47:59", "status": "Received", "created_at": "2025-05-22T09:47:59.970497", "updated_at": "2025-05-22T09:47:59.970497", "tenant_id": 2, "collected_by": 2}, {"id": 26, "sample_id": "S00026", "patient_id": 42, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-16", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-05-16T09:47:59.970501", "updated_at": "2025-05-16T09:47:59.970501", "tenant_id": 3, "collected_by": 1}, {"id": 27, "sample_id": "S00027", "patient_id": 8, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-04", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-04T09:47:59.970505", "updated_at": "2025-05-04T09:47:59.970505", "tenant_id": 3, "collected_by": 3}, {"id": 28, "sample_id": "S00028", "patient_id": 18, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-22", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-05-22T09:47:59.970510", "updated_at": "2025-05-22T09:47:59.970510", "tenant_id": 3, "collected_by": 1}, {"id": 29, "sample_id": "S00029", "patient_id": 3, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-15", "collection_time": "09:47:59", "status": "Received", "created_at": "2025-05-15T09:47:59.970514", "updated_at": "2025-05-15T09:47:59.970514", "tenant_id": 1, "collected_by": 3}, {"id": 30, "sample_id": "S00030", "patient_id": 47, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-17", "collection_time": "09:47:59", "status": "In Transit", "created_at": "2025-05-17T09:47:59.970519", "updated_at": "2025-05-17T09:47:59.970519", "tenant_id": 2, "collected_by": 3}, {"id": 31, "sample_id": "S00031", "patient_id": 44, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-04-24", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-04-24T09:47:59.970523", "updated_at": "2025-04-24T09:47:59.970523", "tenant_id": 3, "collected_by": 3}, {"id": 32, "sample_id": "S00032", "patient_id": 44, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-05", "collection_time": "09:47:59", "status": "Received", "created_at": "2025-05-05T09:47:59.970528", "updated_at": "2025-05-05T09:47:59.970528", "tenant_id": 1, "collected_by": 1}, {"id": 33, "sample_id": "S00033", "patient_id": 19, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-18", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-05-18T09:47:59.970532", "updated_at": "2025-05-18T09:47:59.970532", "tenant_id": 2, "collected_by": 2}, {"id": 34, "sample_id": "S00034", "patient_id": 20, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-11", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-05-11T09:47:59.970537", "updated_at": "2025-05-11T09:47:59.970537", "tenant_id": 2, "collected_by": 1}, {"id": 35, "sample_id": "S00035", "patient_id": 45, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-14", "collection_time": "09:47:59", "status": "In Transit", "created_at": "2025-05-14T09:47:59.970541", "updated_at": "2025-05-14T09:47:59.970541", "tenant_id": 2, "collected_by": 2}, {"id": 36, "sample_id": "S00036", "patient_id": 48, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-18", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-18T09:47:59.970546", "updated_at": "2025-05-18T09:47:59.970546", "tenant_id": 2, "collected_by": 1}, {"id": 37, "sample_id": "S00037", "patient_id": 31, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-04-28", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-04-28T09:47:59.970550", "updated_at": "2025-04-28T09:47:59.970550", "tenant_id": 2, "collected_by": 3}, {"id": 38, "sample_id": "S00038", "patient_id": 11, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-05", "collection_time": "09:47:59", "status": "Received", "created_at": "2025-05-05T09:47:59.970555", "updated_at": "2025-05-05T09:47:59.970555", "tenant_id": 1, "collected_by": 3}, {"id": 39, "sample_id": "S00039", "patient_id": 46, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-20", "collection_time": "09:47:59", "status": "In Transit", "created_at": "2025-05-20T09:47:59.970559", "updated_at": "2025-05-20T09:47:59.970559", "tenant_id": 2, "collected_by": 3}, {"id": 40, "sample_id": "S00040", "patient_id": 31, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-03", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-05-03T09:47:59.970564", "updated_at": "2025-05-03T09:47:59.970564", "tenant_id": 1, "collected_by": 1}, {"id": 41, "sample_id": "S00041", "patient_id": 4, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-04-23", "collection_time": "09:47:59", "status": "Received", "created_at": "2025-04-23T09:47:59.970568", "updated_at": "2025-04-23T09:47:59.970568", "tenant_id": 1, "collected_by": 2}, {"id": 42, "sample_id": "S00042", "patient_id": 15, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-18", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-05-18T09:47:59.970573", "updated_at": "2025-05-18T09:47:59.970573", "tenant_id": 1, "collected_by": 3}, {"id": 43, "sample_id": "S00043", "patient_id": 35, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-17", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-17T09:47:59.970577", "updated_at": "2025-05-17T09:47:59.970577", "tenant_id": 3, "collected_by": 3}, {"id": 44, "sample_id": "S00044", "patient_id": 23, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-15", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-05-15T09:47:59.970584", "updated_at": "2025-05-15T09:47:59.970584", "tenant_id": 3, "collected_by": 3}, {"id": 45, "sample_id": "S00045", "patient_id": 26, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-04", "collection_time": "09:47:59", "status": "In Transit", "created_at": "2025-05-04T09:47:59.970588", "updated_at": "2025-05-04T09:47:59.970588", "tenant_id": 3, "collected_by": 3}, {"id": 46, "sample_id": "S00046", "patient_id": 5, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-04-27", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-04-27T09:47:59.970593", "updated_at": "2025-04-27T09:47:59.970593", "tenant_id": 1, "collected_by": 2}, {"id": 47, "sample_id": "S00047", "patient_id": 5, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-07", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-05-07T09:47:59.970597", "updated_at": "2025-05-07T09:47:59.970597", "tenant_id": 1, "collected_by": 1}, {"id": 48, "sample_id": "S00048", "patient_id": 36, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-04", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-04T09:47:59.970601", "updated_at": "2025-05-04T09:47:59.970601", "tenant_id": 1, "collected_by": 2}, {"id": 49, "sample_id": "S00049", "patient_id": 20, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-04-28", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-04-28T09:47:59.970606", "updated_at": "2025-04-28T09:47:59.970606", "tenant_id": 3, "collected_by": 3}, {"id": 50, "sample_id": "S00050", "patient_id": 15, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-04-28", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-04-28T09:47:59.970610", "updated_at": "2025-04-28T09:47:59.970610", "tenant_id": 3, "collected_by": 1}, {"id": 51, "sample_id": "S00051", "patient_id": 27, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-06", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-05-06T09:47:59.970614", "updated_at": "2025-05-06T09:47:59.970614", "tenant_id": 2, "collected_by": 1}, {"id": 52, "sample_id": "S00052", "patient_id": 26, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-14", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-05-14T09:47:59.970619", "updated_at": "2025-05-14T09:47:59.970619", "tenant_id": 1, "collected_by": 2}, {"id": 53, "sample_id": "S00053", "patient_id": 37, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-04-28", "collection_time": "09:47:59", "status": "Received", "created_at": "2025-04-28T09:47:59.970623", "updated_at": "2025-04-28T09:47:59.970623", "tenant_id": 1, "collected_by": 1}, {"id": 54, "sample_id": "S00054", "patient_id": 43, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-04-27", "collection_time": "09:47:59", "status": "In Transit", "created_at": "2025-04-27T09:47:59.970628", "updated_at": "2025-04-27T09:47:59.970628", "tenant_id": 1, "collected_by": 1}, {"id": 55, "sample_id": "S00055", "patient_id": 48, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-13", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-13T09:47:59.970632", "updated_at": "2025-05-13T09:47:59.970632", "tenant_id": 3, "collected_by": 1}, {"id": 56, "sample_id": "S00056", "patient_id": 1, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-13", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-05-13T09:47:59.970636", "updated_at": "2025-05-13T09:47:59.970636", "tenant_id": 2, "collected_by": 3}, {"id": 57, "sample_id": "S00057", "patient_id": 3, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-07", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-07T09:47:59.970640", "updated_at": "2025-05-07T09:47:59.970640", "tenant_id": 3, "collected_by": 1}, {"id": 58, "sample_id": "S00058", "patient_id": 10, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-13", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-13T09:47:59.970645", "updated_at": "2025-05-13T09:47:59.970645", "tenant_id": 3, "collected_by": 1}, {"id": 59, "sample_id": "S00059", "patient_id": 30, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-04-28", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-04-28T09:47:59.970649", "updated_at": "2025-04-28T09:47:59.970649", "tenant_id": 3, "collected_by": 2}, {"id": 60, "sample_id": "S00060", "patient_id": 47, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-06", "collection_time": "09:47:59", "status": "In Transit", "created_at": "2025-05-06T09:47:59.970653", "updated_at": "2025-05-06T09:47:59.970653", "tenant_id": 1, "collected_by": 2}, {"id": 61, "sample_id": "S00061", "patient_id": 17, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-08", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-05-08T09:47:59.970658", "updated_at": "2025-05-08T09:47:59.970658", "tenant_id": 3, "collected_by": 2}, {"id": 62, "sample_id": "S00062", "patient_id": 36, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-16", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-05-16T09:47:59.970662", "updated_at": "2025-05-16T09:47:59.970662", "tenant_id": 2, "collected_by": 3}, {"id": 63, "sample_id": "S00063", "patient_id": 46, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-14", "collection_time": "09:47:59", "status": "In Transit", "created_at": "2025-05-14T09:47:59.970666", "updated_at": "2025-05-14T09:47:59.970666", "tenant_id": 2, "collected_by": 3}, {"id": 64, "sample_id": "S00064", "patient_id": 44, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-04-27", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-04-27T09:47:59.970672", "updated_at": "2025-04-27T09:47:59.970672", "tenant_id": 2, "collected_by": 1}, {"id": 65, "sample_id": "S00065", "patient_id": 9, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-04-27", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-04-27T09:47:59.970677", "updated_at": "2025-04-27T09:47:59.970677", "tenant_id": 3, "collected_by": 3}, {"id": 66, "sample_id": "S00066", "patient_id": 39, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-04-26", "collection_time": "09:47:59", "status": "Received", "created_at": "2025-04-26T09:47:59.970681", "updated_at": "2025-04-26T09:47:59.970681", "tenant_id": 1, "collected_by": 1}, {"id": 67, "sample_id": "S00067", "patient_id": 28, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-20", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-20T09:47:59.970686", "updated_at": "2025-05-20T09:47:59.970686", "tenant_id": 2, "collected_by": 2}, {"id": 68, "sample_id": "S00068", "patient_id": 48, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-04-24", "collection_time": "09:47:59", "status": "Received", "created_at": "2025-04-24T09:47:59.970690", "updated_at": "2025-04-24T09:47:59.970690", "tenant_id": 3, "collected_by": 2}, {"id": 69, "sample_id": "S00069", "patient_id": 20, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-19", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-05-19T09:47:59.970695", "updated_at": "2025-05-19T09:47:59.970695", "tenant_id": 1, "collected_by": 3}, {"id": 70, "sample_id": "S00070", "patient_id": 38, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-12", "collection_time": "09:47:59", "status": "Received", "created_at": "2025-05-12T09:47:59.970699", "updated_at": "2025-05-12T09:47:59.970699", "tenant_id": 2, "collected_by": 3}, {"id": 71, "sample_id": "S00071", "patient_id": 17, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-04-25", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-04-25T09:47:59.970705", "updated_at": "2025-04-25T09:47:59.970705", "tenant_id": 2, "collected_by": 2}, {"id": 72, "sample_id": "S00072", "patient_id": 49, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-04-22", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-04-22T09:47:59.970710", "updated_at": "2025-04-22T09:47:59.970710", "tenant_id": 1, "collected_by": 3}, {"id": 73, "sample_id": "S00073", "patient_id": 22, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-16", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-16T09:47:59.970714", "updated_at": "2025-05-16T09:47:59.970714", "tenant_id": 3, "collected_by": 3}, {"id": 74, "sample_id": "S00074", "patient_id": 15, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-04-23", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-04-23T09:47:59.970718", "updated_at": "2025-04-23T09:47:59.970718", "tenant_id": 2, "collected_by": 2}, {"id": 75, "sample_id": "S00075", "patient_id": 6, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-04", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-05-04T09:47:59.970723", "updated_at": "2025-05-04T09:47:59.970723", "tenant_id": 3, "collected_by": 2}, {"id": 76, "sample_id": "S00076", "patient_id": 18, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-04-23", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-04-23T09:47:59.970727", "updated_at": "2025-04-23T09:47:59.970727", "tenant_id": 1, "collected_by": 3}, {"id": 77, "sample_id": "S00077", "patient_id": 15, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-04-25", "collection_time": "09:47:59", "status": "Received", "created_at": "2025-04-25T09:47:59.970732", "updated_at": "2025-04-25T09:47:59.970732", "tenant_id": 2, "collected_by": 3}, {"id": 78, "sample_id": "S00078", "patient_id": 47, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-01", "collection_time": "09:47:59", "status": "Received", "created_at": "2025-05-01T09:47:59.970736", "updated_at": "2025-05-01T09:47:59.970736", "tenant_id": 3, "collected_by": 1}, {"id": 79, "sample_id": "S00079", "patient_id": 36, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-07", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-07T09:47:59.970741", "updated_at": "2025-05-07T09:47:59.970741", "tenant_id": 3, "collected_by": 1}, {"id": 80, "sample_id": "S00080", "patient_id": 46, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-10", "collection_time": "09:47:59", "status": "In Transit", "created_at": "2025-05-10T09:47:59.970745", "updated_at": "2025-05-10T09:47:59.970745", "tenant_id": 3, "collected_by": 2}, {"id": 81, "sample_id": "S00081", "patient_id": 5, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-10", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-10T09:47:59.970750", "updated_at": "2025-05-10T09:47:59.970750", "tenant_id": 3, "collected_by": 1}, {"id": 82, "sample_id": "S00082", "patient_id": 28, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-02", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-02T09:47:59.970754", "updated_at": "2025-05-02T09:47:59.970754", "tenant_id": 3, "collected_by": 2}, {"id": 83, "sample_id": "S00083", "patient_id": 40, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-04-30", "collection_time": "09:47:59", "status": "Received", "created_at": "2025-04-30T09:47:59.970758", "updated_at": "2025-04-30T09:47:59.970758", "tenant_id": 1, "collected_by": 2}, {"id": 84, "sample_id": "S00084", "patient_id": 34, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-04-24", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-04-24T09:47:59.970763", "updated_at": "2025-04-24T09:47:59.970763", "tenant_id": 2, "collected_by": 3}, {"id": 85, "sample_id": "S00085", "patient_id": 6, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-04-26", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-04-26T09:47:59.970767", "updated_at": "2025-04-26T09:47:59.970767", "tenant_id": 2, "collected_by": 3}, {"id": 86, "sample_id": "S00086", "patient_id": 32, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-17", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-17T09:47:59.970772", "updated_at": "2025-05-17T09:47:59.970772", "tenant_id": 2, "collected_by": 3}, {"id": 87, "sample_id": "S00087", "patient_id": 30, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-16", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-05-16T09:47:59.970776", "updated_at": "2025-05-16T09:47:59.970776", "tenant_id": 3, "collected_by": 1}, {"id": 88, "sample_id": "S00088", "patient_id": 7, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-03", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-03T09:47:59.970781", "updated_at": "2025-05-03T09:47:59.970781", "tenant_id": 2, "collected_by": 3}, {"id": 89, "sample_id": "S00089", "patient_id": 3, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-13", "collection_time": "09:47:59", "status": "Completed", "created_at": "2025-05-13T09:47:59.970785", "updated_at": "2025-05-13T09:47:59.970785", "tenant_id": 3, "collected_by": 3}, {"id": 90, "sample_id": "S00090", "patient_id": 39, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-18", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-05-18T09:47:59.970790", "updated_at": "2025-05-18T09:47:59.970790", "tenant_id": 1, "collected_by": 2}, {"id": 91, "sample_id": "S00091", "patient_id": 13, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-06", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-05-06T09:47:59.970794", "updated_at": "2025-05-06T09:47:59.970794", "tenant_id": 2, "collected_by": 3}, {"id": 92, "sample_id": "S00092", "patient_id": 30, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-05", "collection_time": "09:47:59", "status": "In Transit", "created_at": "2025-05-05T09:47:59.970798", "updated_at": "2025-05-05T09:47:59.970798", "tenant_id": 1, "collected_by": 2}, {"id": 93, "sample_id": "S00093", "patient_id": 30, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-07", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-05-07T09:47:59.970803", "updated_at": "2025-05-07T09:47:59.970803", "tenant_id": 1, "collected_by": 3}, {"id": 94, "sample_id": "S00094", "patient_id": 32, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-17", "collection_time": "09:47:59", "status": "Collected", "created_at": "2025-05-17T09:47:59.970808", "updated_at": "2025-05-17T09:47:59.970808", "tenant_id": 2, "collected_by": 1}, {"id": 95, "sample_id": "S00095", "patient_id": 11, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-04-26", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-04-26T09:47:59.970812", "updated_at": "2025-04-26T09:47:59.970812", "tenant_id": 1, "collected_by": 1}, {"id": 96, "sample_id": "S00096", "patient_id": 12, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-03", "collection_time": "09:47:59", "status": "In Transit", "created_at": "2025-05-03T09:47:59.970817", "updated_at": "2025-05-03T09:47:59.970817", "tenant_id": 2, "collected_by": 1}, {"id": 97, "sample_id": "S00097", "patient_id": 38, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-04-23", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-04-23T09:47:59.970821", "updated_at": "2025-04-23T09:47:59.970821", "tenant_id": 2, "collected_by": 3}, {"id": 98, "sample_id": "S00098", "patient_id": 20, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-04-22", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-04-22T09:47:59.970826", "updated_at": "2025-04-22T09:47:59.970826", "tenant_id": 2, "collected_by": 1}, {"id": 99, "sample_id": "S00099", "patient_id": 24, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-04-23", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-04-23T09:47:59.970832", "updated_at": "2025-04-23T09:47:59.970832", "tenant_id": 3, "collected_by": 2}, {"id": 100, "sample_id": "S00100", "patient_id": 36, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-04", "collection_time": "09:47:59", "status": "Processed", "created_at": "2025-05-04T09:47:59.970836", "updated_at": "2025-05-04T09:47:59.970836", "tenant_id": 2, "collected_by": 3}]