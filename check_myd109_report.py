#!/usr/bin/env python3
"""
Check MYD109 report structure
"""
import sys
import os
import json

# Add backend directory to path
sys.path.append('backend')

def check_myd109_report():
    """Check MYD109 report structure"""
    
    try:
        from utils import read_data
        
        print("🔧 Checking MYD109 Report...")
        
        # Load billing reports
        billing_reports = read_data('billing_reports.json')
        myd109 = next((r for r in billing_reports if r.get('sid_number') == 'MYD109'), None)
        
        if myd109:
            print('MYD109 Report Structure:')
            print(f'  billing_id: {myd109.get("billing_id")}')
            print(f'  sid_number: {myd109.get("sid_number")}')
            print(f'  notes: {myd109.get("notes", "NOT_FOUND")}')
            print(f'  financial_summary: {myd109.get("financial_summary", "NOT_FOUND")}')
            print(f'  test_items count: {len(myd109.get("test_items", []))}')
            print(f'  All keys: {list(myd109.keys())}')
            
            # Check corresponding billing record
            billing_id = myd109.get('billing_id')
            if billing_id:
                billings = read_data('billings.json')
                billing = next((b for b in billings if b.get('id') == billing_id), None)
                
                if billing:
                    print(f'\nCorresponding Billing Record:')
                    print(f'  id: {billing.get("id")}')
                    print(f'  total_amount: {billing.get("total_amount")}')
                    print(f'  test_items count: {len(billing.get("test_items", []))}')
                else:
                    print(f'\nNo billing record found for ID {billing_id}')
        else:
            print('MYD109 not found')
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    check_myd109_report()
