[{"Test Name": "Allergen - <PERSON><PERSON><PERSON><PERSON> (Fungus)", "code": 1040, "Department": "\nSerology", "Referance Range": "Negative : < 0.1 Positive : >=0.1", "Result Unit": "kUA/L", "No of decimals": 2, "price": 1200, "Result Type": "Numeric", "Short Name": "AGAF", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Allergen - Gluten", "code": 1350, "Department": "\nSerology", "Referance Range": "NEGATIVE  : < 0.1 POSITIVE  : >= 0.1", "Result Unit": "kUA/L", "No of decimals": 2, "price": 1600, "Result Type": "Numeric", "Short Name": "GLU", "Method code": 171, "Method": "ImmunoCAP (Fluoroenzymeimmunoassay)- Specific IgE", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container"}, {"Test Name": "Allergen - Milk", "code": 1287, "Department": "\nSerology", "Referance Range": "Negative: < 0.1 Positive: >= 0.1", "Result Unit": "kUA/L", "price": 1000, "Result Type": "Pick List", "Method code": 182, "Method": "ImmunoCAP (Fluoroenzymeimmunoassay)- Specific IgE", "Specimen Code": 40, "Specimen": "SERUM", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Allergen EGG White", "code": 1290, "Department": "\nSerology", "Referance Range": "Less than 0.1 : Not Detectable  0.10 - 0.50   : Very Low  0.50 - 2.00   : Low  2.00 - 15.0   : Moderate  15.0 - 50.0   : High  >50.0         : Very High.", "Result Unit": "kUA/L", "price": 1000, "Result Type": "Pick List", "Method code": 65, "Method": "FEIA", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Allergen EGG Yolk", "code": 1289, "Department": "\nSerology", "Referance Range": "Negative: < 0.1 Positive: >= 0.1", "Result Unit": "kUA/L", "price": 1000, "Result Type": "Numeric", "Method code": 182, "Method": "ImmunoCAP (Fluoroenzymeimmunoassay)- Specific IgE", "Specimen Code": 39, "Specimen": "SERUM", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Allergens Food - Nuts", "code": 1288, "Department": "\nSerology", "price": 6500, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Allergy Profile (Drugs)", "code": 1285, "Department": "\nSerology", "price": 0, "Result Type": "Pick List", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 7}, {"Test Name": "ALLERGY PROFILE- DRUG ALLERGY (22 Approx. Test)", "code": 1658, "Department": "\nSerology", "price": 2600, "Result Type": "Pick List", "Primary specimen code": 9, "Primary specimen ": "BLOOD", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ALLERGY PROFILE- INHALANT WITH CONTACTS (100 Approx. Test)", "code": 1657, "Department": "\nSerology", "price": 1900, "Result Type": "Pick List", "Primary specimen code": 9, "Primary specimen ": "BLOOD", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ALLERGY PROFILE- NON-VEG FOOD ONLY (14 Approx. Test)", "code": 1656, "Department": "\nSerology", "price": 1500, "Result Type": "Pick List", "Primary specimen code": 9, "Primary specimen ": "BLOOD", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ALLERGY PROFILE- VEG FOOD ONLY (83 Approx.Test)", "code": 1655, "Department": "\nSerology", "price": 2300, "Result Type": "Pick List", "Primary specimen code": 9, "Primary specimen ": "BLOOD", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ALLERGY PROFILE- VEG FOOD+ INHALANTS + CONTACTS (183 Approx. Test)", "code": 1659, "Department": "\nSerology", "price": 2800, "Result Type": "Pick List", "Primary specimen code": 9, "Primary specimen ": "BLOOD", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ALLERGY PROFILE- VEG FOOD+ INHALANTS WITH CONTACTS+ DRUGS (205 Approx. Test)", "code": 1660, "Department": "\nSerology", "price": 4800, "Result Type": "Pick List", "Primary specimen code": 9, "Primary specimen ": "BLOOD", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ALLERGY PROFILE- VEG FOOD+ INHALANTS WITH CONTACTS+ NON-VEG (197 Approx. Test)", "code": 1661, "Department": "\nSerology", "price": 4100, "Result Type": "Pick List", "Primary specimen code": 9, "Primary specimen ": "BLOOD", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Allergy Screening - <PERSON><PERSON><PERSON>", "code": 1304, "Department": "\nSerology", "Referance Range": "<= 11.0", "Result Unit": "ug/L", "price": 2500, "Result Type": "Pick List", "Short Name": "TRYP", "Method code": 155, "Method": "Immunocap", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "Min. Process Time": 7, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "AMOEBIC ANTIBODY- IgG", "code": 1041, "Department": "\nSerology", "Referance Range": "Negative - 0  to 0.4  Positive - Above 0.4", "Result Unit": "NTU", "price": 1800, "Result Type": "Numeric", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANA", "code": 1042, "Department": "\nSerology", "Notes": "Note: Anti-nuclear antibodies (ANA) directed against a variety of nuclear and cytoplasmic antigens occur in high frequency in systemic rheumatic diseases (systemic lupus erythematosus (SLE), mixed connective tissue disease (MCTD), scleroderma, <PERSON><PERSON> syndrome, polymyositis and dermatomyositis) and thus an important tool for differential diagnosis. \r\n\r\nA definite clinical diagnosis should not be based on results of the test only, but should be made by the physician after all clinical and laboratory findings have been evaluated.", "Referance Range": "NEGATIVE : <1.0 Equivocal: 1.0 - 1.2 POSITIVE : >1.2", "Result Unit": "OD RATIO", "No of decimals": 2, "price": 800, "Short Name": "ANA", "Method code": 51, "Method": "E.L.I.S.A", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 24, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANA INDEX", "code": 1044, "Department": "\nSerology", "Referance Range": "<1 AI NEGATIVE  1 -1.2 AI BORDERLINE  >1.2 AI POSITIVE", "Result Unit": "AI", "No of decimals": 2, "price": 1, "Result Type": "Numeric", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANA PROFILE (Blot)", "code": 1227, "Department": "\nSerology", "price": 3000, "Result Type": "Pick List", "Short Name": "ANAP", "Method code": 17, "Method": "BLOT", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANCA", "code": 1035, "Department": "\nSerology", "price": 1800, "Result Type": "Pick List", "Short Name": "ANCA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI  HBe", "code": 1047, "Department": "\nSerology", "Referance Range": ">= 1.11     : NEGATIVE  1.01 - 1.10 : EQUIVOCAL  <= 1.01     : POSITIVE", "Result Unit": "S/Co", "price": 800, "Method code": 29, "Method": "CMIA", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI  LEPTOSPIRAL ANTIBODY IgM", "code": 1048, "Department": "\nSerology", "Referance Range": "NEGATIVE - <9.0 PB Units  BORDERLINE - 9.0 - 11.0 PB Units  POSITIVE - >11.0 PB Units", "Result Unit": "PB Units", "price": 700, "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI CARDIO LIPIN ANTIBODY IgA", "code": 1052, "Department": "\nSerology", "Referance Range": "<12.0 : Negative  12-18 : Equivocal  >18.0 : <PERSON>sitive", "Result Unit": "APLU/ML", "price": 900, "Result Type": "Pick List", "Short Name": "ACAA", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI CARDIOLIPIN ANTIBODY IgG", "code": 1053, "Department": "\nSerology", "Referance Range": "Less than 12", "Result Unit": "PL IgG-U/mL", "No of decimals": 2, "price": 800, "Short Name": "ACAG", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI CARDIOLIPIN ANTIBODY IgM", "code": 1054, "Department": "\nSerology", "Referance Range": "Less than 12", "Result Unit": "PL IgG-U/mL", "No of decimals": 2, "price": 800, "Short Name": "ACAM", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI CCP", "code": 1055, "Department": "\nSerology", "Notes": "Note : Anti CCP is useful for diagnosis of early Rheumatoid arthritis. Depending on the severity of Rheumatoid arthritis Positive results occur in 60 to 80% of patients. The predictive value of Anti CCP is greater than RF. Positive results may also occur in patients with seronegative Rheumatoid arthritis.", "Result Unit": "U/mL", "No of decimals": 2, "price": 1400, "Short Name": "CCP", "Method code": 52, "Method": "ECLIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI DIURETIC HORMONE-ADH", "code": 1233, "Department": "\nSerology", "Referance Range": "30 - 150", "Result Unit": "pg/ml", "price": 3500, "Result Type": "Pick List", "Short Name": "ADH", "Method code": 58, "Method": "ELISA", "Specimen Code": 18, "Specimen": "EDTA PLASMA", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 5, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Anti DNase B", "code": 1208, "Department": "\nSerology", "Referance Range": "Less than 200", "Result Unit": "U/mL", "price": 1500, "Result Type": "Pick List", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Anti dsDNA", "code": 1046, "Department": "\nSerology", "Referance Range": "Negative : <1 Positive : >=1", "Result Unit": "OD/Ratio", "price": 900, "Result Type": "Pick List", "Short Name": "DNA", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Anti dsDNA, IFA", "code": 1558, "Department": "\nSerology", "price": 2200, "Result Type": "Pick List", "Short Name": "DSDNA", "Method code": 4, "Method": "1:10 by Immunofluorescence", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI GLIADIN IgA", "code": 1214, "Department": "\nSerology", "Referance Range": "<25  : Negative >=25 : Positive", "Result Unit": "units", "price": 1500, "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 7, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI GLIADIN IgG", "code": 1215, "Department": "\nSerology", "Referance Range": "<20     : Negative 20 – 30 : <PERSON><PERSON> >30     : <PERSON>sitive", "Result Unit": "units", "price": 1500, "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 7, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HAV - IgG", "code": 1058, "Department": "\nSerology", "Referance Range": "<1.0  : Nonreactive >= 1.0: Reactive", "Result Unit": "S/Co", "price": 1500, "Result Type": "Pick List", "Short Name": "HAVG", "Method code": 29, "Method": "CMIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HAV -TOTAL", "code": 1059, "Department": "\nSerology", "Referance Range": "Negative : < 0.90 Equivocal: 0.90 - 1.10 Positive : > 1.10.", "Result Unit": "OD RATIO", "price": 800, "Result Type": "Pick List", "Short Name": "AVT", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HAV IgM", "code": 1060, "Department": "\nSerology", "Referance Range": "<1.0  : Non-Reactive >= 1.0: Reactive.", "Result Unit": "COI", "No of decimals": 3, "price": 800, "Short Name": "HAVM", "Method code": 52, "Method": "ECLIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HBc IgM", "code": 1063, "Department": "\nSerology", "Referance Range": "<5         : Negative >=5 to <10 : Equivocal >=10       : Positive", "Result Unit": "PEIU/ml", "price": 800, "Result Type": "Pick List", "Short Name": "ANTI HBc M", "Method code": 57, "Method": "ELFA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HBc Total", "code": 1062, "Department": "\nSerology", "Referance Range": "<1.0          : Positive <=1.0 to <1.4 : Equivocal >=1.4         : Negative  (Competitive Assay, Lower value indicates Positivity)", "Result Unit": "index", "price": 800, "Result Type": "Pick List", "Short Name": "HBcT", "Method code": 57, "Method": "ELFA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HBe", "code": 1209, "Department": "\nSerology", "Referance Range": "REACTIVE      : <=1.00 NON REACTIVE  : >1.00  (Competitive Assay, Lower value indicates Positivity)", "Result Unit": "S/Co", "price": 800, "Result Type": "Pick List", "Short Name": "ANTI HBE", "Method code": 29, "Method": "CMIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 1}, {"Test Name": "ANTI HBs", "code": 1064, "Department": "\nSerology", "Referance Range": "<10.0  NON IMMUNE", "Result Unit": "uIU/ml", "No of decimals": 2, "price": 800, "Short Name": "HBs", "Method code": 29, "Method": "CMIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HCV", "code": 1065, "Department": "\nSerology", "Notes": "Note : The above Anti-HCV assay is a third-generation test that uses peptides and recombinant antigens representing core, NS3 and Ns4 proteins for the determination of anti-HCV antibodies.\r\n\r\n \r\n\r\nFor diagnostic purposes, the results should always be assessed in conjunction with the patient's medical history, clinical examination and other findings. The detection of Anti-HCV antibodies indicates a present or past infection with Hepatitis C virus, but doesn’t differentiate between acute, chronic or resolved infection. If acute hepatitis C infection is suspected, measuring of HCV RNA by Reverse Transcriptase Polymerase Chain Reaction (RT-PCR) may give evidence of HCV infection.", "Referance Range": "< 1.0 : Non Reactive > 1.0 : Reactive", "Result Unit": "S/Co", "price": 900, "Result Type": "Pick List", "Short Name": "HCV", "Method code": 29, "Method": "CMIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HCV (CARD)", "code": 1211, "Department": "\nSerology", "Notes": "Note:\r\nHepatitis C Virus (HCV) tests is an immunochromatographic (rapid) test for the qualitative detection of antibodies specific to HCV in human serum, plasma and whole blood. The major immune reactive antigens of structural and non-structural regions of HCV encoded polyprotein have been reported as core NS3, NS4 and NS5 regions of HCV genome which are known to be highly immunodominant regions. For diagnosis of HCV infection these recombinant proteins were used as capture materials and coated on the membrane of an immunochromatographic (rapid) test.\r\nA negative result does not preclude the possibility of HCV infection.\r\nSpecimens repeatedly tested positive should be retested using other testing methods (mention)\r\nDefinitive clinical diagnosis should not be based on the result of a single test but should only be made by the physician after all clinical and laboratory findings have been evaluated.", "Referance Range": "NEGATIVE", "price": 200, "Result Type": "Pick List", "Short Name": "HCV CARD", "Method code": 80, "Method": "IC", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HCV (ELISA)", "code": 1303, "Department": "\nSerology", "Referance Range": "Negative : < Positive : >=", "Result Unit": "OD Ratio", "price": 1000, "Result Type": "Pick List", "Short Name": "HCV", "Method code": 51, "Method": "E.L.I.S.A", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Anti HCV IGG", "code": 1702, "Department": "\nSerology", "price": 0, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HCV IgM", "code": 1067, "Department": "\nSerology", "Notes": "A negative result does not preclude the possibility of HCV infection.", "Referance Range": "\r\n< 1 : Negative >1  : Positive\r", "Result Unit": "index", "No of decimals": 2, "price": 1700, "Min. Process Time": 7, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HDV IgM", "code": 1223, "Department": "\nSerology", "Referance Range": "< 0.9         :  Negative 0.9 - 1.1     :  Equivocal > 1.1         :  Positive", "Result Unit": "index", "price": 2500, "Result Type": "Pick List", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 7, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HEV IgG", "code": 1068, "Department": "\nSerology", "Referance Range": "NEGATIVE : <0.9 Equivocal: 0.9 - 1.1 POSITIVE : >1.1", "Result Unit": "index", "price": 1500, "Result Type": "Pick List", "Method code": 50, "Method": "E.I.A", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HEV IgM", "code": 1069, "Department": "\nSerology", "Referance Range": "Negative: <0.9 Equivocal: 0.9 - 1.1 Positive: >1.1", "Result Unit": "COI", "price": 850, "Result Type": "Pick List", "Short Name": "HEV-M", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HIV 1&2 (CARD)", "code": 1071, "Department": "\nSerology", "Notes": "Note:\r\n     HIV-1/2 is an in-vitro, visually read, qualitative immunoassay for the detection of antibodies to HIV-1 and HIV-2 in human serum, plasma or whole blood. The test is intended as an aid to detect the antibodies to HIV-1/2 from infected individuals. HIV-1/2 test in other body fluids or pooled specimens may not give accurate results.\r\n\r\n     A negative result with HIV-1/2 does not exclude the possibility of infection with HIV. A false negative results can occur in the following circumstances 1. Low level of antibody (eg. Early seroconversion specimens) are below the detection limits of the test. 2. Infection with a variant of the virus that is less detectable. 3. HIV antibodies in the patient that don’t react with specific antigens utilized in the assay configuration. 4. HIV-infected persons taking anti-retroviral medication. For these reasons, care should be taken in interpreting negative results. Other clinical data such as symptom or risk factors should be used in conjugation with test results.\r\n\r\n     Positive specimens should be retested using another method and the results should be evaluated in light of the overall clinical evaluation before a diagnosis. - Whole blood or plasma specimen containing other than EDTA may give incorrect results.\r\n\r\n     Neonates of HIV-infected mothers may carry maternal antibodies to HIV for upto around 18 months, which may not necessarily indicate the true infection status of the newborn.", "Referance Range": "Non Reactive", "price": 400, "Result Type": "Pick List", "Short Name": "HIV CARD", "Method code": 80, "Method": "IC", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HIV Ag/Ab Combo", "code": 1070, "Department": "\nSerology", "Result Unit": "COI", "price": 900, "Result Type": "Pick List", "Short Name": "HIV", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HIV Ag/Ab Combo.", "code": 1544, "Department": "\nSerology", "price": 500, "Result Type": "Pick List", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI HIV I & 2 (ELISA)", "code": 1210, "Department": "\nSerology", "Referance Range": "NEGATIVE : < POSITIVE : >=", "Result Unit": "OD RATIO", "price": 1100, "Result Type": "Pick List", "Short Name": "HIVe", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Anti MAG (Myelin associated Glycoprotein)", "code": 1283, "Department": "\nSerology", "Referance Range": "NEGATIVE", "price": 3500, "Result Type": "Pick List", "Method code": 18, "Method": "BLOT - IMMUNOASSAY IgM", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI MALARIAL ANTIBODY IgG", "code": 1075, "Department": "\nSerology", "price": 700, "Result Type": "Pick List", "Method code": 80, "Method": "IC", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Anti Nuclear Antibody (IF)", "code": 1045, "Department": "\nSerology", "Notes": "Method : Indirect Immunofluorescence (HEp-20-10/Liver) test (IIFT)\r\nScreening 1:100 dilution\r\nInterpretation\r\nNegative : No Immunofluorescence\r\n-:Borderline\r\n1+ : Weak Positive (1:100)\r\n2+ : Positive (1:320)\r\n3+ : Strong Positive (1:1000)\r\n4+ : Very strong Positive (1:3200)\r\nAntinuclear autoantibodies (ANAs) are unusual antibodies, detectable in the blood that have the capability of binding to certain structures within the nucleus of the cells. ANAs\r\nindicate the possible presence of autoimmunity and provide, therefore, an indication of autoimmune illness. The IIFT test is a sensitive screening test used to detect autoimmune\r\ndiseases.\r\n If the test is negative, Detectable level of auto antibodies is ruled out. In the case of a positive result, auto antibodies against any one or in some cases simultaneously against\r\nmore than one may be present and further mono specific tests or panel of profiles can be used to determine the specific auto antibodies present.\r\nFor verification of analysis results, monospecific test should always be accompanied by IIFT. All borderline or weak positive (+) results may be repeated after 6\r\n- 8 weeks.\r\nLOCATION PATTERN TARGET ANTIGEN CLINICAL ASSOCIATION\r\nNucleus Homogeneous (with chromosome bands\r\npositive)\r\n1.   dsDNA\r\n2.   Histones\r\n3.   Nucleosoms\r\nSLE\r\nDrug induced SLE\r\nJIA\r\nNuclear membrane positive Lamins,gp120 PBC\r\nNucleoplasm dotted 1.  Centromeres 1.  PSS (limited)1. 46 - 92 dots\r\n2.  6 -20 dots\r\n3.  2 - 6 dots\r\n  \r\n2.  SP 10,PML(6-20 dots)\r\n3.  -\r\n  \r\n2.  PBC,Other rheumatic diseases\r\n3.  SjS,SLE\r\nNucleoplasm granular with\r\ngranular chromosome bands\r\n DFS -70 Atopic dermatitis, Asthma and interstitial cystitis and\r\nin healthy blood donors\r\nNucleus Nucleoplasm fine granular with nucleoli\r\npositive or Negative\r\nSSA/SSB SjS, SLE,NLE\r\nNucleoplasm granular Ku SLE,PM,DM,Progressive systemic sclerosis(diffused)\r\nNucleoplasm coarse granular nRNP/Sm SLE,MCTD\r\nNucleoplasm granular (Mitotic cells\r\nnegative)\r\nPCNA SLE\r\nNucleoplasm granular (Mitotic cells\r\nPositive)\r\nCENP-F Various malignant tumours\r\nNucleoplasm fine granular with nucleoli\r\npositive\r\nTopoisomerase(Scl-70) Progressive systemic sclerosis(diffused)\r\nNucleoli Nucleoli homogeneous PM-Scl PM,DM, PSS(Diffuse)\r\nNucleoli fine granular Fibrillarin PSS(Diffuse)\r\nNucleoli coarse granular RNA polymerase 1 PSS(Diffuse)\r\nNucleoli granular with mitotic cells single\r\ndots\r\n NOR PSS(Diffuse)\r\nCytoplasm Cytoplasm filamentous\r\n1.  Needle shaped fibre bundles\r\n2.  Fine fibres droplets in the mitotic cells\r\n3.  Fibre slings\r\n4.  Regular short fibres\r\n 1.  Actin\r\n2.  Vimentin\r\n3.  Tropomyosin\r\n4.  Vinculin\r\n1. AIH\r\n2. Infections and inflammation\r\n3. Infections and inflammation\r\n4. Myasthenia gravis,UC,CD\r\nCytoplasm homogeneous Ribosomal P-protein,\r\nPL7/PL12\r\nSLE\r\nPM,DM\r\nCytoplasm granular Lysosomes PBC , Neurological diseases\r\nCytoplasm fine granular JO-1 PM\r\nCytoplasm Cytoplasm coarse granular AMA PBC\r\nCytoplasm peri nuclear Golgi apparatus SjS,SLE,RA\r\nCytoplasm rings & rods - Hepatitis C virus infection\r\nMitotic cells Centrosomes positive - Reynaud’s syndrome, PSS, Infections\r\nSpindle apparatus positive - SjS, SLE, Other CTDS\r\nSpindle apparatus positive\r\n(Intercells-granular)\r\n- SjS, arthritis, APS,SLE\r\nMidbody positive - Reynaud’s sundrome, malignant tumors, PSS\r\nCondense chromosomes positive - PMR,DLE,SjS,CLL\r\nSLE: Systemic Lupus Erythematosus, MCTD: Mixed Connective Tissue Disease; AIH: Autoimmune Hepatitis, PBC: Primary Biliary Cholangitis, PM:Polymyositis,\r\nDM:Dermatomyositis, PSS: Progressive Systemic sclerosis, RA:-Rheumatoid Arthritis, JIA:-Juvenile Idiopathic Arthritis, SJS:-Sjogren’s syndrome, UC:-Ulcerative Colitis, CD:\r\nCrohn’s disease, APS:-Anti-phospholipid syndrome, DLE:-Discoid Lupus Erythematosus, PMR:-Poly Myalgia Rheumatica, NLE:-Neonatal Lupus Erythematous, CLL:-ChronicLymphatic Leukaemia.", "price": 900, "Result Type": "Pick List", "Short Name": "ANAIF", "Method code": 94, "Method": "IMMUNOFLORESN ASSAY", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 3, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI PHOSPHOLIPID ANTIBODY IgA", "code": 1498, "Department": "\nSerology", "Referance Range": "NEGATIVE : Less Than 12 EQUIVOCAL: 12-18    POSITIVE : More than 18", "Result Unit": "U/mL", "No of decimals": 2, "price": 1500, "Short Name": "APA", "Method code": 53, "Method": "EIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI PHOSPHOLIPID ANTIBODY IgG", "code": 1077, "Department": "\nSerology", "Referance Range": "Negative  : <10 Positive  : >=10", "Result Unit": "GPL.U/mL", "price": 800, "Result Type": "Pick List", "Short Name": "ACAG", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI PHOSPHOLIPID ANTIBODY IgM", "code": 1078, "Department": "\nSerology", "Referance Range": "Negative  : <10 Positive  : >=10", "Result Unit": "MPL.U/ML", "price": 800, "Result Type": "Pick List", "Short Name": "APAM", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI SCL 70", "code": 1079, "Department": "\nSerology", "Referance Range": "<20.00", "Result Unit": "Units", "No of decimals": 1, "price": 1400, "Short Name": "SCL", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ANTI SPERM ANTIBODY", "code": 1083, "Department": "\nSerology", "Referance Range": "<60: NEGATIVE >60: POSITIVE", "Result Unit": "U/mL", "No of decimals": 2, "price": 750, "Short Name": "ASA", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Anti SS DNA", "code": 1057, "Department": "\nSerology", "Referance Range": "<68.6      :  Negative   68.6-229   :  Moderate Positive >229       :  Strong Positive", "Result Unit": "U/mL", "No of decimals": 2, "price": 2900, "Short Name": "DNA", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Anti-HBc Total (Ab to Hep-B Core Ag)", "code": 1126, "Department": "\nSerology", "Referance Range": "Non Reactive: < 1 Reactive: >= 1", "Result Unit": "index", "price": 1200, "Result Type": "Pick List", "Method code": 52, "Method": "ECLIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Anti-La/SSB Antibody", "code": 1050, "Department": "\nSerology", "Referance Range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "price": 1200, "Result Type": "Pick List", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Anti-SARS-CoV-2", "code": 1571, "Department": "\nSerology", "Referance Range": "<1.0  : Nonreactive >=1.0 : Reactive", "Result Unit": "COI", "price": 1000, "Result Type": "Pick List", "Short Name": "COVT", "Method code": 52, "Method": "ECLIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 2}, {"Test Name": "Antibodies to Soluble Liver Antigen (SLA)", "code": 1286, "Department": "\nSerology", "Referance Range": "Negative    :  < 12 Equivocal   :  12 - 18 Positive    :  > 18", "Result Unit": "U/mL", "price": 1500, "Result Type": "Pick List", "Short Name": "SLA", "Method code": 50, "Method": "E.I.A", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Antibody to Sm Antigen", "code": 1217, "Department": "\nSerology", "Referance Range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "price": 0, "Result Type": "Pick List", "Method code": 17, "Method": "BLOT", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ASCA - IgA", "code": 1224, "Department": "\nSerology", "Referance Range": "Negative  : 0.0 - 20.0  Borderline: 20.1 - 24.9 positive  : >= 25.0", "Result Unit": "units", "price": 1700, "Result Type": "Pick List", "Short Name": "ASCA", "Method code": 50, "Method": "E.I.A", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ASCA - IgG", "code": 1225, "Department": "\nSerology", "Referance Range": "Negative  : 0.0 - 20.0  Borderline: 20.1 - 24.9 positive  : >= 25.0", "Result Unit": "units", "price": 1700, "Result Type": "Pick List", "Short Name": "ASCA", "Method code": 50, "Method": "E.I.A", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ASO - Antistreptolysin O", "code": 1039, "Department": "\nSerology", "Notes": "Note: ASO antibodies are produced about a week to a month after an initial streptococcal infection. The amount of ASO antibody (titer) peaks at about 3 to 5 weeks after the illness and then tapers off but may remain detectable for several months after the strep infection has resolved. Over 80% of patients with acute rheumatic fever and 95% of patients with acute glomerulonephritis due to streptococci have elevated ASO. In some cases of streptococcal infections, particularly skin infections, there may be no observable increase in the ASO titer. An elevated titer of antibody (positive ASO) or an ASO titer that is rising indicates recent strep infection. ASO titers that are initially high and then decline suggest that an infection has occurred and may be resolving. Both clinical and laboratory findings should be correlated in reaching a diagnosis. In very rare cases, gammopathy, in particular type IgM (<PERSON><PERSON><PERSON>’s macroglobulinemia), may cause unreliable results.", "Referance Range": "Less than 200 \r\nUpto 5 Yrs < 100", "price": 500, "Short Name": "ASO", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 4, "      Test Done On": "Expect saturday", "Applicable to": "Both"}, {"Test Name": "Aspergillosis Antibody - IgG", "code": 1088, "Department": "\nSerology", "Referance Range": "Negative   : <8.0 Equivocal : 8.0 - 12.0 Positive  : > 12.0", "price": 2000, "Result Type": "Pick List", "Short Name": "AGAG", "Method code": 50, "Method": "E.I.A", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Aspergillosis Antibody - IgM", "code": 1089, "Department": "\nSerology", "Referance Range": "Negative  : <9.0 Equivocal : 9.0 - 11.0 Positive  : > 11.0", "Result Unit": "units", "price": 2000, "Result Type": "Pick List", "Short Name": "AGAM", "Method code": 50, "Method": "E.I.A", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Asper<PERSON><PERSON> fumigatus, Specific IgG", "code": 1618, "Department": "\nSerology", "Referance Range": "<=27.4", "Result Unit": "mgA/L", "No of decimals": 1, "price": 2000, "Short Name": "AFSG", "Method code": 70, "Method": "Fluoro enzyme immunoassay", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 2}, {"Test Name": "Bacterial Meningitis Panel", "code": 1368, "Department": "\nSerology", "price": 4500, "Result Type": "Pick List", "Short Name": "BMP", "Specimen Code": 48, "Specimen": "Serum/CSF", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Beta-2-glycoprotein I-IgA", "code": 1506, "Department": "\nSerology", "Referance Range": "Less than 5 : Negative 5 - 8       : Equivocal More than 8 : Positive", "Result Unit": "U/mL", "price": 1800, "Result Type": "Pick List", "Short Name": "GLYA", "Method code": 53, "Method": "EIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 5}, {"Test Name": "Beta-2-glycoprotein I-IgG", "code": 1281, "Department": "\nSerology", "Referance Range": "<20.0", "Result Unit": "RU/mL", "price": 1200, "Result Type": "Pick List", "Short Name": "B2GG", "Method code": 58, "Method": "ELISA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Beta-2-glycoprotein I-IgM", "code": 1282, "Department": "\nSerology", "Referance Range": "<20.0", "Result Unit": "RU/mL", "price": 1200, "Result Type": "Pick List", "Short Name": "B2GM", "Method code": 58, "Method": "ELISA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "BK Virus Quantitative, Urine", "code": 1570, "Department": "\nSerology", "Result Unit": "Copies/mL", "price": 5000, "Result Type": "Pick List", "Short Name": "BKV", "Method code": 125, "Method": "Real Time P.C.R", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 10}, {"Test Name": "<PERSON><PERSON><PERSON><PERSON> IgG", "code": 1627, "Department": "\nSerology", "Referance Range": "Negative     : < 16 Indeterminate: 16-24 Positive     : > 24", "Result Unit": "U/mL", "price": 3500, "Short Name": "BORG", "Method code": 53, "Method": "EIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Borrel<PERSON>(Lyme) - IgG Ab", "code": 1292, "Department": "\nSerology", "Referance Range": "Negative: < 10 Equivocal: 10-15 Positive: >= 15", "Result Unit": "AU/mL", "price": 2000, "Result Type": "Pick List", "Method code": 28, "Method": "CLIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Borrel<PERSON>(Lyme)- IgM Ab", "code": 1293, "Department": "\nSerology", "Referance Range": "Negative: < 18                                                                                                             Equivocal: 18-22                                                                                                                 Positive: >= 22", "Result Unit": "AU/mL", "price": 2000, "Result Type": "Pick List", "Method code": 28, "Method": "CLIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "BRUCELLA  ANTIBODY - IGM", "code": 1090, "Department": "\nSerology", "Referance Range": "Negative  : <8.0 Equivocal : 8.0 - 12.0 Positive  : >12.0", "Result Unit": "U/mL", "price": 900, "Short Name": "BRUM", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "BRUCELLA AGGLUTINATION TEST", "code": 1206, "Department": "\nSerology", "price": 500, "Result Type": "Pick List", "Short Name": "BRAT", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "BRUCELLA ANTIBODY - IGG", "code": 1092, "Department": "\nSerology", "Referance Range": "Negative  : <8.0 Borderline: 8.0 - 12.0 Positive  : >12.0", "Result Unit": "U/mL", "price": 900, "Short Name": "BRUG", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "C-ANCA (PR3)", "code": 1038, "Department": "\nSerology", "Referance Range": "Negative  : <12.0 Equivocal : 12.0 - 18.0 Positive  : >18.0", "Result Unit": "U/mL", "No of decimals": 2, "price": 900, "Short Name": "C ANCA", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Centromere Antibody IgG", "code": 1274, "Department": "\nSerology", "Referance Range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "Result Unit": "units", "No of decimals": 2, "price": 1700, "Method code": 50, "Method": "E.I.A", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CHIKUNGUNYA IgG (CARD)", "code": 1734, "Department": "\nSerology", "price": 1100, "Result Type": "Pick List", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CHIKUNGUNYA IgM (CARD)", "code": 1097, "Department": "\nSerology", "Referance Range": "Negative", "price": 800, "Result Type": "Pick List", "Short Name": "CHIK", "Method code": 80, "Method": "IC", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CHIKUNGUNYA-IGM (ELISA)", "code": 1598, "Department": "\nSerology", "Referance Range": "Negative : < 0.90 Equivocal: 0.90 - 1.10 Positive : > 1.10", "Result Unit": "index", "price": 1000, "Result Type": "Pick List", "Method code": 58, "Method": "ELISA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 2}, {"Test Name": "CHLAMYDIAL ANTIBODY  IgM", "code": 1099, "Department": "\nSerology", "Referance Range": "<0.8       : Negative 0.8 - 1.1  : Equivocal >1.1       : Positive", "Result Unit": "<PERSON><PERSON>", "price": 1500, "Result Type": "Pick List", "Short Name": "CHLM", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CHLAMYDIAL ANTIBODY IgG", "code": 1100, "Department": "\nSerology", "Referance Range": "< 9       : Negative 9 - 11    : Equivocal >11       : <PERSON><PERSON>", "Result Unit": "<PERSON><PERSON>", "price": 1500, "Result Type": "Pick List", "Short Name": "CHLG", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Clostridium Difficile GDH", "code": 1575, "Department": "\nSerology", "Referance Range": "Negative  : <=0.9 Equivocal : 0.91-1.10 Positive  : >=1.10", "Result Unit": "index", "price": 2600, "Method code": 28, "Method": "CLIA", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Clostridium Difficile Toxin (A&B)", "code": 1294, "Department": "\nSerology", "Referance Range": "Negative   :  <=0.9 Equivocal  :  0.91 - 1.10 Positive    : >= 1.10", "Result Unit": "index", "price": 2200, "Result Type": "Pick List", "Method code": 28, "Method": "CLIA", "Container Code": 47, "Container": "STOOL", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CMV - Avidity test", "code": 1272, "Department": "\nSerology", "Referance Range": "Low avidity           : <30  Grey zone mean avidity: 30 - 40 High avidity          : >40", "Result Unit": "%", "price": 1500, "Result Type": "Pick List", "Short Name": "CMVA", "Method code": 50, "Method": "E.I.A", "Specimen Code": 39, "Specimen": "SERUM", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CMV IgG", "code": 1101, "Department": "\nSerology", "Referance Range": "Nonreactive  : <0.5 Indeterminate: 0.5 - <1.0 Reactive     : >=1.0", "Result Unit": "\r\nIU/mL\r", "No of decimals": 2, "price": 600, "Short Name": "CMV G", "Method code": 52, "Method": "ECLIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CMV IgM", "code": 1102, "Department": "\nSerology", "Referance Range": "Nonreactive  : <0.70 Indeterminate: 0.70 - <1.00 Reactive     : >= 1.00", "Result Unit": "COI", "No of decimals": 2, "price": 600, "Short Name": "CMV M", "Method code": 52, "Method": "ECLIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "COMPLETE ALLERGY PROFILE- VEG FOOD+ INHALANTS WITH CONTACTS + NON-VEG + DRUGS (222 Approx. Test)", "code": 1662, "Department": "\nSerology", "price": 5750, "Result Type": "Pick List", "Primary specimen code": 9, "Primary specimen ": "BLOOD", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CRP-LATEX", "code": 1226, "Department": "\nSerology", "Notes": "C- Reactive protein is an annular, pentameric protein synthesized by the liver and found in human blood. Its rate of synthesis increases within hours of acute injury or the onset of inflammation and may reach as high as 20 times the normal levels. The CRP-latex is a slide agglutination test for the qualitative and semi-quantitative detection of CRP in human serum. -CRP is found to be present after the first trimester of pregnancy and may persist until delivery. -CRP levels increase in women who are on oral contraceptives. -CRP response is not affected by the commonly used anti-inflammatory or immunosuppressive drugs, including steroids, unless the disease activity is affected and it covers an exceptionally broad incremental range upto 3000 times. -CRP production is a non-specific response to tissue injury; it is recommended that the result of the tests should be correlated with clinical findings to arrive at the final diagnosis. -In cases where there is an increase in CRP levels is suspected, but the screening tests shows negative results, semi-quantitative tests should be done to rule out prozone effect. -Lipemic, hemolysed and contaminated serum samples could produce non-specific results.", "Referance Range": "Negative", "price": 400, "Result Type": "Pick List", "Method code": 104, "Method": "LATEX", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CRYPTOCOCCUS ANTIGEN", "code": 1104, "Department": "\nSerology", "price": 2500, "Result Type": "Pick List", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Cryptococcus Antigen, CSF", "code": 1248, "Department": "\nSerology", "price": 2500, "Result Type": "Pick List", "Method code": 177, "Method": "Latex Agglutination", "Specimen Code": 12, "Specimen": "CSF", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "DENGUE Ab/Ag - Card", "code": 1253, "Department": "\nSerology", "price": 800, "Short Name": "<PERSON><PERSON>", "Method code": 80, "Method": "IC", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "DENGUE ANTIBODY IgG", "code": 1114, "Department": "\nSerology", "Notes": "Note: Dengue IgG CLIA is a presumptive quantitative detection of IgG antibodies to dengue virus in patients with secondary infection. The diagnosis must be interpreted with clinical data and patient symptoms.    Secondary dengue infection is characterized by high IgG levels detectable as early as 3 days following the onset of infection, which may be accompanied with elevation of IgM.   The accuracy of the assay is dependent upon the timing following the infection when the sample is collected.   The peak performance of the assay is achieved when samples are taken between 6-15 days following onset of illness.  In some secondary infections detectable levels of IgG may be low. Where symptoms persist, it is recommended to retest 4-7 days after the first specimen.\r\n\r\n\r\n\r\nConsidering the similarity in some of the symptoms between dengue and COVID-19, as well as possible cross-reactivity between dengue virus and SARS-CoV-2, which can lead to false positive dengue serology among COVID-19 patients and vice versa.  Requesting health practitioners in dengue endemic areas to diagnosis both dengue and COVID-19 to avoid misdiagnosis.", "Referance Range": "Less than 1   : Non reactive\r\nAbove >=1     : Reactive", "Result Unit": "S/Co", "price": 500, "Short Name": "DENG", "Method code": 28, "Method": "CLIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 6, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "DENGUE ANTIBODY IgM", "code": 1115, "Department": "\nSerology", "Notes": "Note : Dengue IgM CLIA quantitatively detects IgM antibodies to dengue antigen in serum. The diagnosis must be interpreted with clinical signs and symptoms of the patient.   Primary Dengue infection is characterized by the presence of significant or rising IgM levels after 3-5 days on the onset of infection and persists for 3-5 months.   Secondary infection is characterized by elevation of specific IgG 1-2 days after the onset of infection and in majority of the cases is accompanied with elevation of IgM.   In early and some secondary infections, detectable levels of IgM may be low. In few cases, patients may not produce detectable levels of antibody within the first 7-10 days after infection. Where symptoms persist, it is recommended to re-test seven days after the first specimen.\r\n\r\nConsidering the similarity in some of the symptoms between dengue and COVID-19, as well as possible cross-reactivity between dengue virus and SARS-CoV-2, which can lead to false positive dengue serology among COVID-19 patients and vice versa.  Requesting health practitioners in dengue endemic areas to diagnosis both dengue and COVID-19 to avoid misdiagnosis.", "Referance Range": "Less than 1   : Non reactive\nAbove >=1     : Reactive", "Result Unit": "S/Co", "price": 500, "Short Name": "DENM", "Method code": 28, "Method": "CLIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "\nPLAIN", "Min. Process Time": 6, "      Test Done On": "Expect sunday", "Applicable to": "Both"}, {"Test Name": "DENGUE IgG (CARD)", "code": 1247, "Department": "\nSerology", "Referance Range": "NEGATIVE", "price": 600, "Result Type": "Pick List", "Method code": 80, "Method": "IC", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "DENGUE IgM (CARD)", "code": 1246, "Department": "\nSerology", "Referance Range": "NEGATIVE", "price": 600, "Result Type": "Pick List", "Method code": 80, "Method": "IC", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "DENGUE Ns1 ANTIGEN - Card", "code": 1244, "Department": "\nSerology", "Referance Range": "\nNEGATIVE", "price": 600, "Result Type": "Pick List", "Short Name": "NS1", "Method code": 80, "Method": "IC", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "DENGUE Ns1 ANTIGEN - CLIA", "code": 1279, "Department": "\nSerology", "Referance Range": "Less than 1    :  Non reactive\r\nAbove >=  1     : Reactive", "Result Unit": "S/Co", "Critical Low": 1, "   Critical High": 1, "price": 500, "Short Name": "NS1 CLIA", "Method code": 28, "Method": "CLIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 6, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "DENGUE PROFILE", "Department": "\nSerology", "price": 1700}, {"Test Name": "DIPHTHERIA IgG-Antibodies", "code": 1216, "Department": "\nSerology", "Referance Range": "< 0.01     - No Protection  0.01 - 0.1 - Minimal Protection  0.10 - 1.0 - Safe Protection  > 1.00     - Long Term Protection", "Result Unit": "IU/ml", "price": 2900, "Short Name": "DITG", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "DPT Antibodies (Diphtheria,Pertussis,Tetanus)-IgG", "code": 1517, "Department": "\nSerology", "price": 6500, "Result Type": "Pick List", "Short Name": "DPT", "Method code": 53, "Method": "EIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "EBV  - MONOSPOT", "code": 1266, "Department": "\nSerology", "price": 700, "Result Type": "Pick List", "Method code": 7, "Method": "Agglutination", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "EBV (NA) IgG", "code": 1261, "Department": "\nSerology", "Referance Range": "Negative: < 5 Equivocal: 5-20 Positive: >= 20", "Result Unit": "U/mL", "price": 1750, "Result Type": "Pick List", "Short Name": "NA-G", "Method code": 28, "Method": "CLIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "EBV (NA) IgM", "code": 1262, "Department": "\nSerology", "Referance Range": "Negative  : <8.0 Equivocal : 8.0 - 12.0 Positive  : >12.0", "Result Unit": "U/mL", "price": 1750, "Result Type": "Pick List", "Short Name": "NA-M", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "EBV (VCA) IgG", "code": 1263, "Department": "\nSerology", "Referance Range": "Negative: < 20   Positive: >= 20", "Result Unit": "U/mL", "price": 1750, "Result Type": "Pick List", "Short Name": "VCAG", "Method code": 28, "Method": "CLIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "EBV (VCA) IgM", "code": 1264, "Department": "\nSerology", "Referance Range": "NEGATIVE   :  < 20 EQUIVOCAL  :  20 - 39 POSITIVE   :  >= 40", "Result Unit": "U/mL", "price": 1750, "Result Type": "Pick List", "Short Name": "VCAM", "Method code": 28, "Method": "CLIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ECHINOCOCCUS (HYDATID CYST) DETECTION", "code": 1577, "Department": "\nSerology", "price": 1200, "Result Type": "Pick List", "Specimen Code": 21, "Specimen": "FLUID", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Echinococcus(Hydatid Cyst) IgG", "code": 1243, "Department": "\nSerology", "Referance Range": "Negative: < 9.0 Positive: > 11 Equivocal: 9.0-11", "Result Unit": "NTU", "No of decimals": 1, "price": 1500, "Result Type": "Numeric", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ENA PROFILE", "code": "P00014", "Department": "\nSerology", "price": 3600}, {"Test Name": "ENDOMYCIAL ANTIBODY IGG", "code": 1509, "Department": "\nSerology", "Referance Range": "Negative  Sample screening dilution is 1:10", "price": 1800, "Result Type": "Pick list", "Short Name": "ENDG", "Method code": 95, "Method": "Immunofluorescence", "Specimen Code": 40, "Specimen": "SERUM", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Endomysial Antibody - IgA", "code": 1245, "Department": "\nSerology", "Referance Range": "NEGATIVE  ", "price": 1800, "Result Type": "Pick List", "Short Name": "ENDA", "Method code": 87, "Method": "IFA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Febrile Agglutination Test", "code": 1221, "Department": "\nSerology", "price": 1000, "Result Type": "Numeric", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "FTA-Abs IgG", "code": 1118, "Department": "\nSerology", "Referance Range": "Negative", "price": 1500, "Result Type": "Pick List", "Method code": 4, "Method": "1:10 by Immunofluorescence", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "FTA-Abs IgM", "code": 1234, "Department": "\nSerology", "Referance Range": "Negative", "price": 2000, "Result Type": "Pick List", "Method code": 4, "Method": "1:10 by Immunofluorescence", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Ganglioside antibody IgG (Anti GM1)", "code": 1284, "Department": "\nSerology", "price": 4500, "Result Type": "Template", "Method code": 91, "Method": "Methyl Re<PERSON>ufin", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "GBM - Glomerular Basement Membrane Antibody", "code": 1238, "Department": "\nSerology", "price": 1200, "Result Type": "Pick List", "Short Name": "GBM", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "GERMAN MEASLES (RUBELLA IGG)", "code": 1120, "Department": "\nSerology", "Referance Range": "Negative: <4.9 Equivocal: 5.0 - 9.9 Positive : >=10", "price": 1300, "Result Type": "Pick List", "Method code": 29, "Method": "CMIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON>, Stool", "code": 1554, "Department": "\nSerology", "Referance Range": "Negative", "price": 1700, "Result Type": "Pick List", "Short Name": "HPAG", "Method code": 90, "Method": "Immuno Chromatography", "Specimen Code": 47, "Specimen": "STOOL", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all ", "Applicable to": "Both", "  Reporting Days": 4}, {"Test Name": "HAMS TEST", "code": 1242, "Department": "\nSerology", "Referance Range": "NEGATIVE", "price": 1100, "Result Type": "Pick List", "Method code": 169, "Method": "Hemolysis", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 5}, {"Test Name": "HAV IGM-Antibody", "code": 1125, "Department": "\nSerology", "Referance Range": "< 0.8     - Non-Reactive  0.8 - 1.2 - Gray zone  > 1.2     - Reactive", "price": 800, "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Hbe Ag", "code": 1127, "Department": "\nSerology", "Referance Range": "NON REACTIVE :< 1.00 REACTIVE     :>=1.00", "Result Unit": "S/Co", "price": 900, "Result Type": "Pick List", "Short Name": "Hbe Ag", "Method code": 29, "Method": "CMIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 1}, {"Test Name": "HBsAg", "code": 1129, "Department": "\nSerology", "Notes": "Hepatitis B surface antigen (HBsAg) is the first serologic marker appearing in the serum at 6 to 16 weeks following exposure to HBV. In acute infection, HBsAg usually disappears in 1 to 2 months after the onset of symptoms. Persistence of HBsAg for more than 6 months in duration indicates development of either a chronic carrier state or chronic HBV infection.      The detection of HBsAg is useful in diagnosis of acute, recent, or chronic hepatitis B infection, determination of chronic hepatitis B infection status, routine serological screening during pregnancy and blood transfusion and evaluate the efficacy of antiviral therapy.    \r\n\r\nNote:\r\n\r\n Not useful during the \"window period\" of acute hepatitis B virus (HBV) infection (ie, after disappearance of hepatitis B surface antigen [(HBsAg] and prior to appearance of hepatitis B surface antibody [anti-HBs]). Testing for acute HBV infection should also include hepatitis B core IgM antibody (anti-HBc IgM).     Positive screen results need for confirmation testing by neutralization assay and should be interpreted in conjunction with test results of other HBV molecular and serologic markers only (eg, anti-HBs, anti-HBc total, and anti-HBc IgM).  \r\n\r\n \r\n\r\nDiscrepant results may be observed during pregnancy, patients receiving mouse monoclonal antibodies for diagnosis or therapy & mutant forms of HBsAg Individuals, especially neonates and children, who recently received hepatitis B vaccination may have transient positive HBsAg test results because of the large dose of HBsAg used in the vaccine relative to the individual's body mass.", "Referance Range": "<1.0 - Non Reactive >=1.0- Reactive", "Result Unit": "S/Co", "price": 350, "Result Type": "Pick List", "Short Name": "HBsAg", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HBsAg (CARD)", "code": 1128, "Department": "\nSerology", "Notes": "Note\r\n\r\n HBsAg test is an in-vitro immunochromatographic, one step assay designed for qualitative determination of HBsAg in human serum or plasma.\r\n\r\n\r\nA negative result does not preclude the possibility of infection with Hepatitis B Virus.\r\n\r\nOther clinically available tests (plz mention) are required if questionable results are obtained.\r\n\r\nAs with all diagnostic tests a definitive clinical diagnosis should not be based on the result of a single test, but should only be made by the physician after all clinical and laboratory findings have been evaluated.", "Referance Range": "NEGATIVE", "price": 200, "Result Type": "Pick List", "Short Name": "HbsAg CARD", "Method code": 80, "Method": "IC", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HBsAg (ELISA)", "code": 1280, "Department": "\nSerology", "Referance Range": "Negative : < Positive : >=", "Result Unit": "OD Ratio", "price": 1200, "Result Type": "Pick List", "Short Name": "HBAg", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HBsAg Confirmation", "code": 1130, "Department": "\nSerology", "price": 800, "Result Type": "Pick List", "Short Name": "HBSCon", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 1}, {"Test Name": "HBsAg- CLIA", "code": 1649, "Department": "\nSerology", "Referance Range": "Non reactive: < 1.0\r\nReactive : >= 1.0", "Result Unit": "S/Co", "price": 600, "Method code": 28, "Method": "CLIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Helicobacter Pylori -IgA antibodies", "code": 1516, "Department": "\nSerology", "Referance Range": "Negative   : <0.8 Equivocal  : 0.8-1.1 Positive   : >1.1", "Result Unit": "<PERSON><PERSON>", "No of decimals": 2, "price": 2400, "Method code": 53, "Method": "EIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 3}, {"Test Name": "Helicobacter Pylori -IgM antibodies", "code": 1121, "Department": "\nSerology", "Referance Range": "NEGATIVE : < 8.00 EQUIVOCAL: 8 - 12 POSITIVE : > 12.00", "Result Unit": "U/mL", "No of decimals": 2, "price": 1700, "Short Name": "HPYM", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "Expect sunday", "Applicable to": "Both"}, {"Test Name": "Helicobacter Pylori-IgG antibodies", "code": 1122, "Department": "\nSerology", "Referance Range": "Negative      : < 0.9 Indeterminate : 0.9 - 1.1 Positive      : > 1.1 & Above", "Result Unit": "\r\nU/mL\r", "No of decimals": 2, "price": 1300, "Short Name": "HPYG", "Method code": 28, "Method": "CLIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HISTONE ANTIBODY.", "code": 1232, "Department": "\nSerology", "Referance Range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive(+++)", "Result Unit": "U/mL", "price": 1500, "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Histoplasma Antibody", "code": 1297, "Department": "\nSerology", "price": 5000, "Result Type": "Pick List", "Method code": 93, "Method": "IMMUNODIFFUSION", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HIV I & 2 (CLIA)", "code": 1670, "Department": "\nSerology", "Referance Range": "<1.0 Negative >1.0 Positive S/Co", "price": 1100, "Result Type": "Pick List", "Method code": 28, "Method": "CLIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HIV p24 ANTIGEN", "code": 1072, "Department": "\nSerology", "Referance Range": "\r\nNEGATIVE\r", "price": 1500, "Result Type": "Pick List", "Short Name": "P24", "Method code": 57, "Method": "ELFA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HSV  I (IgG)", "code": 1135, "Department": "\nSerology", "Referance Range": "NEGATIVE : < 0.80 EQUIVOCAL: 0.80 - 1.20 POSITIVE : > 1.20", "Result Unit": "OD Ratio", "No of decimals": 2, "price": 500, "Short Name": "HS1G", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HSV 1 & 2 IgG", "code": 1136, "Department": "\nSerology", "Referance Range": "< 0.8       : Negative > 0.8-<1.1  : Borderline > 1.1       : Positive", "Result Unit": "OD Ratio", "No of decimals": 2, "price": 500, "Short Name": "HSVG", "Method code": 51, "Method": "E.L.I.S.A", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HSV 1 & 2 IgM", "code": 1137, "Department": "\nSerology", "Referance Range": "< 0.8       : Negative > 0.8-<1.1  : Borderline > 1.1       : Positive", "Result Unit": "OD Ratio", "price": 500, "Short Name": "HSVM", "Method code": 51, "Method": "E.L.I.S.A", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HSV I (IgM)", "code": 1140, "Department": "\nSerology", "Referance Range": "NEGATIVE : < 0.80 EQUIVOCAL: 0.80 - 1.20 POSITIVE : > 1.20", "Result Unit": "OD Ratio", "No of decimals": 2, "price": 500, "Short Name": "HSIM", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HSV II (IgG)", "code": 1138, "Department": "\nSerology", "Referance Range": "NEGATIVE : < 0.80 EQUIVOCAL: 0.80 - 1.20 POSITIVE : > 1.20", "Result Unit": "OD Ratio", "No of decimals": 2, "price": 500, "Short Name": "HS2G", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HSV II (IgM)", "code": 1251, "Department": "\nSerology", "Referance Range": "NEGATIVE : < 0.80 EQUIVOCAL: 0.80 - 1.20 POSITIVE : > 1.20", "Result Unit": "OD Ratio", "No of decimals": 2, "price": 500, "Short Name": "HS2M", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HTLV (I & II) Antibody", "code": 1295, "Department": "\nSerology", "Referance Range": "<1.0 : Non Reactive >=1.0: Reactive", "Result Unit": "S/Co", "price": 5700, "Result Type": "Pick List", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Influenza A & B", "code": 1229, "Department": "\nSerology", "Referance Range": "Negative     (Screening test)", "price": 800, "Method code": 80, "Method": "IC", "Specimen Code": 29, "Specimen": "<PERSON><PERSON>", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Interferon Gamma Release Assay - TB", "code": 1191, "Department": "\nSerology", "Referance Range": "Positive : Above 0.35", "Result Unit": "IU/mL", "price": 2800, "Result Type": "Pick List", "Short Name": "TBGD", "Method code": 58, "Method": "ELISA", "Specimen Code": 9, "Specimen": "BLOOD", "Instructions": "Sample collection Container to be collected from Lab", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Intrinsic factor antibody", "code": 1291, "Department": "\nSerology", "Referance Range": "Negative (1:10 dil)", "price": 2000, "Result Type": "Pick List", "Method code": 95, "Method": "Immunofluorescence", "Specimen Code": 39, "Specimen": "SERUM", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Jo-1 Antibody", "code": 1219, "Department": "\nSerology", "Referance Range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "price": 1400, "Result Type": "Pick List", "Short Name": "JO1A", "Method code": 17, "Method": "BLOT", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Legionella Pneumophila antigen", "code": 1628, "Department": "\nSerology", "price": 3000, "Result Type": "Pick List", "Short Name": "LGAG", "Method code": 92, "Method": "Immunochromatography", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 2}, {"Test Name": "LEPTOSPIRA By (DFM)", "code": 1249, "Department": "\nSerology", "price": 0, "Result Type": "Pick List", "Method code": 48, "Method": "DFM", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "LEPTOSPIRA BY MAT TEST", "code": 1236, "Department": "\nSerology", "Referance Range": "NEGATIVE", "price": 600, "Result Type": "Pick List", "Method code": 7, "Method": "Agglutination", "Specimen Code": 40, "Specimen": "SERUM", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "LEPTOSPIRA IgG", "code": 1231, "Department": "\nSerology", "Referance Range": "Negative  : < 9  Equivocal : 9 - 11  Positive  : > 11", "Result Unit": "NTU", "price": 900, "Result Type": "Pick List", "Short Name": "LEPG", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "<PERSON>,wed,fri,sat", "Applicable to": "Both"}, {"Test Name": "LEPTOSPIRA IgM", "code": 1207, "Department": "\nSerology", "Referance Range": "< 0.9 NEGATIVE 0.9-1.1 EQUIVOCAL >1.1 POSITIVE", "Result Unit": "COI", "price": 700, "Result Type": "Pick List", "Short Name": "LEPM", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "LEPTOSPIRAL IgG & IgM (Card)", "code": 1269, "Department": "\nSerology", "price": 600, "Result Type": "Pick List", "Method code": 80, "Method": "IC", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "LKM1-Antibodies", "code": 1237, "Department": "\nSerology", "Referance Range": "NEGATIVE (1:40)", "price": 2000, "Result Type": "Pick List", "Short Name": "LKM", "Method code": 87, "Method": "IFA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Lyme Borrelia <PERSON>dorferi IgM Antibodies", "code": 1335, "Department": "\nSerology", "Referance Range": "\r\nNegative : < 18 Equivocal: 18-21 Positive : >=22\r", "Result Unit": "AU/mL", "price": 2000, "Result Type": "Pick List", "Short Name": "<PERSON><PERSON>", "Method code": 28, "Method": "CLIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "LYMES ANTIBODY", "code": 1145, "Department": "\nSerology", "price": 3000, "Result Type": "Pick List", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MEASLES (Rubeola) - ANTIBODY IgM", "code": 1147, "Department": "\nSerology", "Referance Range": "NEGATIVE   : <0.90 EQUIVOCAL  : 0.90 - 1.10 POSITIVE   : >1.10", "Result Unit": "index", "price": 1500, "Result Type": "Pick List", "Short Name": "MEAM", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MEASLES (Rubeola) IgG", "code": 1170, "Department": "\nSerology", "Referance Range": "Negative  : <13.5  Equivocal : 13.5-16.5 Positive  : >=16.5", "No of decimals": 2, "price": 1500, "Short Name": "MSG", "Method code": 28, "Method": "CLIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON><PERSON>, Serum", "code": 1146, "Department": "\nSerology", "Referance Range": "Refer Inerpretation", "Result Unit": "U/mL", "No of decimals": 1, "price": 1200, "Short Name": "MEAG", "Method code": 53, "Method": "EIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MITOCHONDRIAL ANTIBODIES (AMA)", "code": 1076, "Department": "\nSerology", "Referance Range": "NEGATIVE (1:40)", "price": 1200, "Result Type": "Pick List", "Short Name": "MITO", "Method code": 87, "Method": "IFA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Mumps IgG", "code": 1148, "Department": "\nSerology", "Referance Range": "NEGATIVE    : <9 EQUIVOCAL   : 9 - 11 POSITIVE    : >11", "No of decimals": 2, "price": 1500, "Short Name": "MPG", "Method code": 28, "Method": "CLIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MUMPS IgM", "code": 1149, "Department": "\nSerology", "Referance Range": "Negative   : <0.9 Borderline : >0.9 - <1.1 Positive   : >=1.1", "Result Unit": "index", "price": 1500, "Result Type": "Pick List", "Short Name": "MUMM", "Method code": 28, "Method": "CLIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MUSK <PERSON>, Myasthenia gravis", "code": 1296, "Department": "\nSerology", "Referance Range": "Negative : < 0.4 Positive :  > 0.4", "Result Unit": "U/mL", "price": 6000, "Result Type": "Pick List", "Short Name": "MUSK", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Mycoplasma pneumoniae - IgG", "code": 1277, "Department": "\nSerology", "Referance Range": "Negative      : < 9  Indeterminate : 9-11 Positive      : > 11", "Result Unit": "NTU", "price": 2000, "Result Type": "Pick List", "Method code": 53, "Method": "EIA", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Mycoplasma pneumoniae - IgM", "code": 1151, "Department": "\nSerology", "Referance Range": "Negative : <10.0 Positive : >10.0", "Result Unit": "index", "No of decimals": 1, "price": 2000, "Method code": 28, "Method": "CLIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 5}, {"Test Name": "Myositis Profile", "code": 1519, "Department": "\nSerology", "price": 11000, "Result Type": "Template", "Method code": 17, "Method": "BLOT", "Specimen Code": 48, "Specimen": "Serum/CSF", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 10}, {"Test Name": "Neuronal Antibody Profile", "code": 1325, "Department": "\nSerology", "price": 10000, "Result Type": "Pick List", "Method code": 91, "Method": "Methyl Re<PERSON>ufin", "Specimen Code": 40, "Specimen": "SERUM", "Min. Process Time": 10, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "NMDA Receptor Antibody (NR1)", "code": 1351, "Department": "\nSerology", "Referance Range": "Negative", "price": 7000, "Result Type": "Pick List", "Short Name": "NMDA", "Method code": 95, "Method": "Immunofluorescence", "Specimen Code": 48, "Specimen": "Serum/CSF", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 10}, {"Test Name": "Ovarian Antibody (AOA)", "code": 1255, "Department": "\nSerology", "Referance Range": "Negative", "price": 2400, "Result Type": "Pick List", "Short Name": "AOA", "Method code": 96, "Method": "Immunofluorescence (1:10)", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "P C R  - Hepatitis B", "code": 1153, "Department": "\nSerology", "price": 3800, "Result Type": "Pick List", "Method code": 115, "Method": "P.C.R", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "P-ANCA (MPO)", "code": 1037, "Department": "\nSerology", "Referance Range": "Negative  : <12.0 Equivocal : 12.0 - 18.0 Positive  : >18.0", "Result Unit": "U/mL", "No of decimals": 2, "price": 900, "Short Name": "P ANCA", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all ", "Applicable to": "Both"}, {"Test Name": "Parietal Cell Antibody (GPCA)", "code": 1275, "Department": "\nSerology", "Referance Range": "Negative (1:10 dil)", "price": 2200, "Result Type": "Pick List", "Method code": 95, "Method": "Immunofluorescence", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Parvovirus  (B19) IgG", "code": 1259, "Department": "\nSerology", "Referance Range": "Negative    : < 0.9 Borderline  : 0.9-1.1 Positive    : >= 1.1", "Result Unit": "index", "price": 2500, "Result Type": "Pick List", "Method code": 50, "Method": "E.I.A", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Parvovirus (B19) IgM", "code": 1260, "Department": "\nSerology", "Referance Range": "Negative: < 0.9 Borderline: 0.9-1.1 Positive: >= 1.1", "Result Unit": "index", "price": 2500, "Result Type": "Pick List", "Method code": 28, "Method": "CLIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PAUL BUNNEL TEST (IM - SCREENING)", "code": 1154, "Department": "\nSerology", "Referance Range": "No agglutination", "price": 500, "Result Type": "Pick List", "Short Name": "PAUL", "Method code": 7, "Method": "Agglutination", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RA (ELISA)", "code": 1158, "Department": "\nSerology", "Referance Range": "<15 IU/ml INTERPRETATION : INCREASED IN RHEUMATOID ARTHRITIS", "Result Unit": "IU/mI", "No of decimals": 2, "Critical Low": 0.01, "   Critical High": 11, "price": 1, "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RA - FACTOR", "code": 1162, "Department": "\nSerology", "Result Unit": "IU/mI", "No of decimals": 1, "price": 400, "Short Name": "RA", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RA FACTOR-LATEX", "code": 1159, "Department": "\nSerology", "Referance Range": "NEGATIVE", "price": 400, "Result Type": "Pick List", "Method code": 104, "Method": "LATEX", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Rabies antibody", "code": 1213, "Department": "\nSerology", "Referance Range": "IMMUNE     : > 0.5  NON IMMUNE : < 0.5", "Result Unit": "IU/ml", "price": 4000, "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Ricketssia by PCR", "code": 1166, "Department": "\nSerology", "price": 5500, "Result Type": "Pick List", "Method code": 117, "Method": "PCR", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 7, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RNA Polymerase III Antibodies, IgG", "code": 1631, "Department": "\nSerology", "Referance Range": "Less than 20", "Result Unit": "Units", "No of decimals": 2, "price": 4000, "Short Name": "RPIII", "Method code": 58, "Method": "ELISA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 7, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RNP-Sm Antibody", "code": 1218, "Department": "\nSerology", "Referance Range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "price": 1500, "Result Type": "Pick List", "Method code": 17, "Method": "BLOT", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RPR (Rapid Plasma Reagin)", "code": 1167, "Department": "\nSerology", "Referance Range": "Nonreactive", "price": 350, "Result Type": "Pick List", "Short Name": "RPR", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Rubella - Avidity test", "code": 1273, "Department": "\nSerology", "Referance Range": "Low Avidity : <35 Mean Avidity: 35 - 45 High Avidity: >45", "Result Unit": "%", "price": 1500, "Result Type": "Pick List", "Short Name": "RUBA", "Method code": 53, "Method": "EIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RUBELLA IgG", "code": 1168, "Department": "\nSerology", "Referance Range": "Nonreactive : <10.0 Reactive    : >10.0", "Result Unit": "IU/mL", "No of decimals": 3, "price": 800, "Short Name": "RUBELLA-G", "Method code": 52, "Method": "ECLIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RUBELLA IGM", "code": 1169, "Department": "\nSerology", "Referance Range": "Nonreactive  : <0.80 Indeterminate: >=0.80 - <1.00 Reactive     : >= 1.00", "Result Unit": "index", "No of decimals": 3, "price": 800, "Result Type": "Numeric", "Short Name": "RUBELLA-M", "Method code": 52, "Method": "ECLIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "S S A ANTI R-O ANTIBODY", "code": 1171, "Department": "\nSerology", "Referance Range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "price": 1800, "Result Type": "Pick List", "Short Name": "SSA", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "S S B-La ANTIBODY", "code": 1172, "Department": "\nSerology", "Referance Range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "price": 1800, "Result Type": "Pick List", "Short Name": "SSB", "Method code": 17, "Method": "BLOT", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Salmonella Typhi Dot - IgM", "code": 1270, "Department": "\nSerology", "Referance Range": "Negative", "price": 400, "Result Type": "Pick List", "Short Name": "DotM", "Method code": 80, "Method": "IC", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "SARS-CoV-2 IgG", "code": 1568, "Department": "\nSerology", "Notes": "The results of anti-SARS-CoV-2 IgG serologic tests may be interpreted in the following way:\r\n• In a person never vaccinated:\r\ntesting positive for antibody against either N, S, or RBD indicates prior natural infection\r\n•In a vaccinated person:\r\ntesting positive for antibody against the vaccine antigen target, such as the S protein, and negative for other antigens suggests that they have produced\r\nvaccine-induced antibody and that they were never infected with SARS-CoV-2\r\ntesting positive for any antibody other than the vaccine-induced antibody, such as the N protein, indicates resolving or resolved SARS-CoV-2 infection that\r\ncould have occurred before or after vaccination.\r\nPresence of antibodies to N protein indicates previous natural infection regardless of vaccination status, while presence of antibodies to S protein indicates either\r\nprevious natural infection or vaccination. Presence of antibodies to S protein and absence of antibodies to N protein in the same specimen indicates vaccination in a\r\nperson never naturally infected or could signal prior natural infection in a person whose antibodies to N protein have waned. Since vaccines induce antibodies to\r\nspecific viral protein targets, post-vaccination serologic test results will be negative in persons without history of previous natural infection if the test used does not\r\ndetect antibodies induced by the vaccine.\r\nAntibody testing is not currently recommended to assess for immunity to COVID-19 following COVID-19 vaccination or to assess the need for vaccination in an\r\nunvaccinated person. Since vaccines induce antibodies to specific viral protein targets, post-vaccination serologic test results will be negative in persons without\r\nhistory of previous natural infection if the test used does not detect antibodies induced by the vaccine.\r\nUnvaccinated persons who have tested antibody positive within 3 months before or immediately following an exposure to someone with suspected or confirmed\r\nCOVID-19 and who have remained asymptomatic since the current COVID-19 exposure do not need to quarantine, provided there is limited or no contact with\r\npersons at high risk for severe COVID-19 illness, including older adults and persons with certain medical conditions.\r\nRef: Interim Guidelines for COVID-19 Antibody Testing, Centre for Disease Control and Prevention (March 2021).", "Referance Range": "Negative :<0.9 Equivocal:0.9-1.1 Positive :>1.1 (ELFA)", "Result Unit": "index", "price": 1000, "Result Type": "Pick List", "Short Name": "COVG", "Method code": 57, "Method": "ELFA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 2}, {"Test Name": "SARS-COV-2 IgM", "code": 1576, "Department": "\nSerology", "Notes": "Severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) is a responsible for an international outbreak of a respiratory illness termed coronavirus\r\ndisease 19 (COVID-19), and whose manifestations range from a mild, self-limiting respiratory tract illness to severe progressive pneumonia, multiorgan\r\nfailure, and death. Most COVID-19 patients had an antibody response at ten days or later after onset of the symptoms. The antibody response is\r\ncharacterized by the early rise of type M immunoglobulins (IgM), then followed by type G immunoglobulins (IgG). IgM antibodies to SARS-CoV-2 are\r\ngenerally detectable in blood several days after initial infection, although the duration of time antibodies are present post-infection is not well\r\ncharacterized.\r\n· A negative result for an individual subject indicates absence of detectable anti-SARS-CoV-2 antibodies. Negative results do not preclude SARSCoV-2 infection and should not be used as the sole basis for patient management decisions. A negative result can occur if the quantity of the\r\nanti-SARS-CoV-2 antibodies present in the specimen is below the detection limits of the assay who have been exhibiting symptoms for less than\r\n15 days, or the antibodies that are detected are not present during the stage of disease in which a sample is collected. Patient specimens may\r\nbe nonreactive if collected during the early (preseroconversion) phase of illness or due to a decline in titer over time. In addition, the immune\r\nresponse may be depressed in elderly, immunocompromised, or immunosuppressed patients.\r\n· A positive result may not indicate previous SARS-CoV-2 infection. Consider other information including clinical history and local disease\r\nprevalence, in assessing the need for a second but different serology test to confirm an immune response.\r\n· False positive results for SARS-COV-2 IgM may occur due to cross-reactivity from pre-existing antibodies or other possible causes such as AntiNuclear Antibody, Rheumatoid Factor, Plasmodium falciparum, Trypanosoma cruzi, and Coronavirus NL63. Positive results may be due to past\r\nor present infection with non-SARS-CoV-2 coronavirus strains, such as coronavirus HKU1 or OC43.\r\n· Results obtained using samples from SARS-CoV-2 infected patients must be interpreted with caution.\r\n· The individual immune response following SARS-CoV-2 infection varies considerably and might give different results with assays from different\r\nmanufacturers. Results of assays from different manufacturers should not be used interchangeably.\r\n· This test should not be used to diagnose or exclude acute SARS-CoV-2 infection. Negative results do not rule out SARS-CoV-2 infection,\r\nparticularly in those who have been in contact with the virus. Testing with a molecular diagnostic should be performed to evaluate for active\r\ninfection in symptomatic individuals.\r\n· The magnitude of the measured result above the threshold is not indicative of the total amount of antibody present in the sample.\r\n· Results from antibody testing should not be used to diagnose or exclude acute SARS-CoV-2 infection. Results are not intended to be used as the\r\nsole basis for patient management decisions.\r\n· It is not known at this time, if the presence of antibodies to SARS-CoV-2 confers immunity to reinfection.\r\n· This test should not be used for the screening of donated blood.", "Referance Range": "Negative : <1.00 Positive : >=1.00\r\n(ELFA)", "Result Unit": "index", "price": 1000, "Result Type": "Pick List", "Short Name": "COVM", "Method code": 57, "Method": "ELFA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 10, "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 2}, {"Test Name": "Scl-70 Antibody IgG (DNA Topoisomerase-1)", "code": 1220, "Department": "\nSerology", "Referance Range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "price": 1400, "Result Type": "Pick List", "Short Name": "SCLG", "Method code": 50, "Method": "E.I.A", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "SCRUB TYPHUS IGM", "code": 1278, "Department": "\nSerology", "Referance Range": "Less than 9.0 : Negative 9 - 11        : Equivocal More than 11.0: Positive", "price": 1200, "Result Type": "Pick List", "Short Name": "SCRM", "Method code": 58, "Method": "ELISA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "SMITH ANTIBODY (SM)", "code": 1174, "Department": "\nSerology", "Referance Range": "<20       -   Negative 20 - 39   -   <PERSON><PERSON> 40 - 80   -   Moderate Positive >80       -   Strong Positive", "Result Unit": "units", "price": 1500, "Result Type": "Numeric", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "SMOOTH MUSCLE ANTIBODY (ASMA)", "code": 1081, "Department": "\nSerology", "Referance Range": "NEGATIVE", "price": 1400, "Result Type": "Pick List", "Short Name": "ASMA", "Method code": 87, "Method": "IFA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON>phi<PERSON> (VDRL)", "code": 1201, "Department": "\nSerology", "Notes": "Note:\r\nVDRL (Syphilis) test is a solid phase immunochromatographic assay for the qualitative detection of antibodies of all isotypes (IgG, IgM and IgA) against Treponema pallidum. \r\nThe syphilis test will only indicate the presence of Treponema pallidum antibodies in the specimen and should not be used as the sole criteria for the diagnosis of syphilis infection \r\n\r\nAs with all diagnostic tests, results must be considered with other clinical information available to the physician. \r\n\r\nIf the test result is negative and clinical symptoms persist additional follow-up testing using other serological tests Treponema pallidum haemagglutination (TPHA) and the immunostaining analysis by fluorescent treponemal adsorption test (FTA-ABS) must be used in order to obtain a confirmation of syphilis infection", "Referance Range": "Non Reactive", "price": 150, "Result Type": "Pick List", "Short Name": "VDRL", "Method code": 90, "Method": "Immuno Chromatography", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "SYPHILIS TOTAL ANTIBODY", "code": 1733, "Department": "\nSerology", "price": 1350, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Tissue Transglutaminase (tTG) Ab-IgA", "code": 1240, "Department": "\nSerology", "Referance Range": "NEGATIVE     : <20 WEAK POSITIVE: 20 -30 POSITIVE     : >30", "Result Unit": "units", "price": 1500, "Result Type": "Pick List", "Short Name": "TTgA", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Tissue Transglutaminase (tTG) Ab-IgG", "code": 1557, "Department": "\nSerology", "Referance Range": "Less than 4.0 : Negative 4.0-10.0      : Weak positive More than 10.0: Positive", "Result Unit": "u/mL", "No of decimals": 2, "price": 1500, "Short Name": "TTgG", "Method code": 53, "Method": "EIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "TORCH IgG", "code": 1193, "Department": "\nSerology", "price": 1500, "Result Type": "Pick List", "Short Name": "TOG", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "TORCH IgM", "code": 1194, "Department": "\nSerology", "price": 1500, "Result Type": "Pick List", "Short Name": "TOM", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "TOXOPLASMA IgG", "code": 1195, "Department": "\nSerology", "Referance Range": "Non-reactive  : <1.0 Indeterminate : >=1.0 - <3.0 Reactive      : >=3.0", "Result Unit": "IU/mI", "No of decimals": 2, "price": 600, "Short Name": "TOXG", "Method code": 52, "Method": "ECLIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "TOXOPLASMA IgM", "code": 1196, "Department": "\nSerology", "Referance Range": "Nonreactive  : <0.80 Indeterminate: >=0.80 - <1.00 Reactive     : >= 1.00", "Result Unit": "COI", "No of decimals": 3, "price": 600, "Short Name": "TOXM", "Method code": 52, "Method": "ECLIA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Toxoplasma, Avidity test", "code": 1271, "Department": "\nSerology", "Referance Range": "Low Avidity      : <30  Grayzone Avidity : 30 - 35  High Avidity     : >35", "Result Unit": "%", "price": 1500, "Result Type": "Pick List", "Short Name": "TOXA", "Method code": 53, "Method": "EIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Treponema  Antibodies", "code": 1254, "Department": "\nSerology", "Referance Range": "<=1.0: Negative >1.0 : Positive.", "Result Unit": "\r\nIndex\r", "price": 1000, "Result Type": "Pick List", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "TREPONEMA ANTIBODIES", "code": 1239, "Department": "\nSerology", "Referance Range": "<1.0 : Nonreactive  >=1.0: Reactive", "Result Unit": "Index", "price": 1000, "Method code": 50, "Method": "E.I.A", "Specimen Code": 40, "Specimen": "SERUM", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Treponema Pallidum Hemagglutination (TPHA)", "code": 1197, "Department": "\nSerology", "Notes": "NOTE:\r\nThe TPHA test cannot discriminate antibodies anti-T. pallidum from antibodies to other pathogenic treponemas. It is recommended that all  positive  results be confirmed by alternative procedures.\r\nFalse positive results have been described with samples of patients with mononucleosis, leprosy, borreliosis, autoimmune diseases and drug addiction.\r\nThe TPHA test is not useful in determining the effectiveness of the therapy since the antibodies level remains long time after the disease has been clinically cured and the test remains positive.", "Referance Range": "NON REACTIVE", "price": 350, "Result Type": "Pick List", "Short Name": "TPHA", "Method code": 7, "Method": "Agglutination", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "U1RNP Antibody", "code": 1198, "Department": "\nSerology", "Referance Range": "0  - 5  : <PERSON><PERSON><PERSON> 6  - 10 : Borderline 11 - 25 : Positive (+) 26 - 50 : Positive (++) >50     : Strong Positive (+++)", "price": 1500, "Result Type": "Pick List", "Method code": 58, "Method": "ELISA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Varicella - Zoster IgG", "code": 1200, "Department": "\nSerology", "Referance Range": "Less than 150", "No of decimals": 2, "price": 1400, "Result Type": "Numeric", "Short Name": "VZG", "Method code": 28, "Method": "CLIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Varicella Z<PERSON>er -IgM antibodies", "code": 1199, "Department": "\nSerology", "Referance Range": "Less than 1.0", "price": 900, "Result Type": "Pick List", "Short Name": "VZM", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "VGKC (Lgi1 & CASPR2) Antibody", "code": 1354, "Department": "\nSerology", "price": 8500, "Result Type": "Pick List", "Short Name": "VGKC", "Method code": 87, "Method": "IFA", "Specimen Code": 48, "Specimen": "Serum/CSF", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "WEIL FELIX TEST", "code": 1202, "Department": "\nSerology", "price": 400, "Result Type": "Pick List", "Short Name": "WFEX", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "WESTERN BLOT", "code": 1203, "Department": "\nSerology", "price": 2000, "Short Name": "WB", "Method code": 17, "Method": "BLOT", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "WESTERN BLOT (HIV-1)", "code": 1252, "Department": "\nSerology", "price": 2000, "Result Type": "Pick List", "Short Name": "WB", "Method code": 17, "Method": "BLOT", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "WIDAL - Tube Dilution", "code": 1204, "Department": "\nSerology", "Notes": "Note\r\n\r\nWidal agglutination is a serologic technique to aid in diagnosis of typhoid (enteric) fever. The test is based on demonstrating the presence of agglutinin (antibody) in the serum of an infected patient, against the H (flagellar) and O (somatic) antigens of Salmonella typhi, S.paratyphi A, B & C. Antibodies to Salmonella may be detected in patient serum from the second week after onset of infection. \r\n\r\nTAB vaccinated patients may show a high titre of antibodies to each of the antigens. Similarly, an amnestic response to other vaccines and unrelated fever in case of patients who have prior infection or immunization may give a false result. Agglutinins usually appear at the end of first week of infection, blood sample taken earlier may give a negative result.\r\n\r\nA rising titre is more significant than a single high titre. It is therefore necessary to evaluate two or more serum samples taken in 4-6 days interval after the onset of disease.  While the “O” antigen is species specific, the “H” antigen is specific to serotype.\r\n\r\nSerologic findings are not intended as substitute for culture. An appropriate attempt should be made to recover and identify the etiologic organism through various culture and biochemical tests.\r\n\r\nGenerally antibodies titres of more than 1:80 are considered clinically and diagnostically significant. However the significant titre may vary from population to population and needs to be established for each area. It is recommended that the results of the tests should be correlated with clinical findings to arrive at the final diagnosis.", "price": 150, "Short Name": "WidT", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 1}, {"Test Name": "WIDAL-SLIDE METHOD", "code": 1212, "Department": "\nSerology", "Notes": "INTERPRETATION: <=1:80 - Insignificant, >=1:160 -Significant\r\n\r\nWidal agglutination is a serologic technique to aid in diagnosis of typhoid (enteric) fever. The test is based on demonstrating the presence of agglutinin (antibody) in the serum of an infected patient, against the H (flagellar) and O (somatic) antigens of Salmonella typhi, S.paratyphi A, B & C. Antibodies to Salmonella may be detected in patient serum from the second week after onset of infection.\r\n\r\nPositive results obtained in the slide test should be confirmed with tube test to establish whether the titers are diagnostically significant TAB vaccinated patients may show a high titer of antibodies to each of the antigens. Similarly, an amnestic response to other vaccines and unrelated fever in case of patients who have prior infection or immunization may give a false result.\r\n\r\nAgglutinins usually appear at the end of first week of infection, blood sample taken earlier may give a negative result.\r\n\r\nA rising titre is more significant than a single high titer. It is therefore necessary to evaluate two or more serum samples taken in 4-6 days interval after the onset of disease.\r\n\r\nWhile the “O” antigen is species specific, the “H” antigen is specific to serotype. Serologic findings are not intended as substitute for culture. An appropriate attempt should be made to recover and identify the etiologic organism through various culture and biochemical tests.\r\n\r\nGenerally antibodies titers of more than 1:80 are considered clinically and diagnostically significant. However the significant titer may vary from population to population and needs to be established for each area. It is recommended that the results of the tests should be correlated with clinical findings to arrive at the final diagnosis.", "price": 150, "Result Type": "Pick List", "Short Name": "widal", "Method code": 176, "Method": "Slide agglutination", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "Expect sunday", "Applicable to": "Both"}]