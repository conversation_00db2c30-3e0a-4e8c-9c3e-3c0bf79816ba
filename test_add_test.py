#!/usr/bin/env python3
"""
Test script to verify the add-test API functionality
"""
import requests
import json

def get_auth_token():
    """Get authentication token by logging in"""
    login_url = "http://localhost:5002/api/auth/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }

    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            result = response.json()
            token = result.get('token')
            print(f"✅ Login successful, token obtained")
            return token
        else:
            print(f"❌ Login failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data}")
            except:
                print(f"Error text: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return None

def test_add_test_api(token):
    """Test the add-test API endpoint"""
    
    # API endpoint
    url = "http://localhost:5002/api/billing/111/add-test"
    
    # Test data
    test_data = {
        "test_items": [
            {
                "name": "Complete Blood Count (CBC)",
                "amount": 250,
                "test_id": 1,
                "selectedTestData": {
                    "id": 1,
                    "testName": "Complete Blood Count (CBC)",
                    "department": "Hematology",
                    "method": "Automated",
                    "specimen": "Blood",
                    "referenceRange": "Normal values",
                    "test_price": 250
                }
            }
        ]
    }
    
    # Headers
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    try:
        print("Testing add-test API...")
        print(f"URL: {url}")
        print(f"Data: {json.dumps(test_data, indent=2)}")
        
        # Make the request
        response = requests.post(url, json=test_data, headers=headers)
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ SUCCESS: {result.get('message', 'Test added successfully')}")
            print(f"Response Data: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"❌ ERROR: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Error Text: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ ERROR: Could not connect to the server. Is the backend running on port 5002?")
        return False
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

def test_billing_retrieval(token):
    """Test retrieving the billing record to verify the test was added"""

    url = "http://localhost:5002/api/billing/111"
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    try:
        print("\nTesting billing retrieval...")
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            billing_data = response.json()
            test_items = billing_data.get('test_items', [])
            items = billing_data.get('items', [])
            
            print(f"✅ Billing retrieved successfully")
            print(f"Test Items Count: {len(test_items)}")
            print(f"Items Count: {len(items)}")
            
            if test_items:
                print("Test Items:")
                for item in test_items:
                    print(f"  - {item.get('name', 'Unknown')} (₹{item.get('amount', 0)})")
            
            return True
        else:
            print(f"❌ ERROR: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Billing Add-Test Functionality")
    print("=" * 50)

    # Step 1: Get authentication token
    token = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        exit(1)

    # Step 2: Add test to billing
    success1 = test_add_test_api(token)

    # Step 3: Verify the test was added
    success2 = test_billing_retrieval(token)

    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED!")
    else:
        print("❌ SOME TESTS FAILED!")
