/* User Create Styles */

.user-create-container {
  padding: 1.5rem;
}

/* Summary Styles */
.summary-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item span:first-child {
  font-weight: 600;
  color: var(--dark-gray);
}

/* Form Section Styles */
.form-section {
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  background-color: #f8f9fa;
}

.form-section-title {
  margin-bottom: 1rem;
  font-weight: 600;
  color: var(--dark-gray);
}

/* Password Strength Indicator */
.password-strength {
  margin-top: 0.5rem;
  height: 5px;
  border-radius: 2px;
  background-color: #e9ecef;
  overflow: hidden;
}

.password-strength-bar {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.password-strength-text {
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.strength-weak {
  background-color: var(--danger);
  width: 25%;
}

.strength-fair {
  background-color: var(--warning);
  width: 50%;
}

.strength-good {
  background-color: var(--info);
  width: 75%;
}

.strength-strong {
  background-color: var(--success);
  width: 100%;
}

/* Responsive Styles */
@media (max-width: 767.98px) {
  .user-create-container {
    padding: 1rem;
  }
  
  .d-flex.justify-content-between {
    flex-direction: column;
    align-items: flex-start !important;
  }
  
  .d-flex.justify-content-between div {
    margin-top: 1rem;
    width: 100%;
  }
  
  .d-flex.justify-content-between div .btn {
    width: 100%;
    margin-right: 0 !important;
    margin-bottom: 0.5rem;
  }
  
  .summary-item {
    padding: 0.5rem 0;
  }
}
