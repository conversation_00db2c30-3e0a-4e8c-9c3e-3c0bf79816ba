{"referrerTypes": [{"id": "doctor", "name": "Doctor", "description": "Medical practitioners and physicians"}, {"id": "self", "name": "Self", "description": "Self-referred patients"}, {"id": "lab", "name": "Lab", "description": "Other laboratory facilities"}, {"id": "hospital", "name": "Hospital", "description": "Hospital and medical institutions"}, {"id": "corporate", "name": "Corporate", "description": "Corporate health programs"}, {"id": "insurance", "name": "Insurance", "description": "Insurance companies and health plans"}, {"id": "staff", "name": "Staff", "description": "Internal staff referrals"}, {"id": "consultant", "name": "Consultant", "description": "Medical consultants and specialists"}, {"id": "outstation", "name": "Outstation", "description": "Out-of-station referrals"}], "referrerNames": {"doctor": [{"id": "dr_001", "name": "Dr. <PERSON><PERSON>", "specialization": "Cardiologist", "hospital": "City Heart Hospital", "phone": "**********", "email": "<EMAIL>"}, {"id": "dr_002", "name": "Dr. <PERSON><PERSON>", "specialization": "Endocrinologist", "hospital": "Metro Medical Center", "phone": "**********", "email": "<EMAIL>"}, {"id": "dr_003", "name": "Dr. <PERSON><PERSON>", "specialization": "General Physician", "hospital": "Community Health Clinic", "phone": "**********", "email": "<EMAIL>"}, {"id": "dr_004", "name": "Dr. <PERSON><PERSON>", "specialization": "Gynecologist", "hospital": "Women's Care Hospital", "phone": "**********", "email": "<EMAIL>"}, {"id": "dr_005", "name": "Dr. <PERSON><PERSON><PERSON>", "specialization": "Orthopedist", "hospital": "Bone & Joint Center", "phone": "**********", "email": "<EMAIL>"}], "hospital": [{"id": "hosp_001", "name": "City General Hospital", "type": "Multi-specialty", "address": "123 Main Street, City Center", "phone": "**********", "email": "<EMAIL>"}, {"id": "hosp_002", "name": "Metro Medical Center", "type": "Super Specialty", "address": "456 Health Avenue, Metro District", "phone": "**********", "email": "<EMAIL>"}, {"id": "hosp_003", "name": "Community Health Clinic", "type": "Primary Care", "address": "789 Community Road, Suburb", "phone": "**********", "email": "<EMAIL>"}, {"id": "hosp_004", "name": "Specialty Care Institute", "type": "Specialty", "address": "321 Medical Plaza, Downtown", "phone": "**********", "email": "<EMAIL>"}], "lab": [{"id": "lab_001", "name": "PathLab Diagnostics", "type": "Full Service Lab", "address": "111 Lab Street, Medical District", "phone": "**********", "email": "<EMAIL>"}, {"id": "lab_002", "name": "QuickTest Laboratory", "type": "Express Testing", "address": "222 Quick Avenue, Business Park", "phone": "**********", "email": "<EMAIL>"}, {"id": "lab_003", "name": "Precision Diagnostics", "type": "Specialized Testing", "address": "333 Precision Road, Tech Hub", "phone": "**********", "email": "<EMAIL>"}], "corporate": [{"id": "corp_001", "name": "TechCorp Health Program", "company": "TechCorp Industries", "contact_person": "HR Manager", "phone": "**********", "email": "<EMAIL>"}, {"id": "corp_002", "name": "Manufacturing Co. Wellness", "company": "Manufacturing Co. Ltd", "contact_person": "Wellness Coordinator", "phone": "**********", "email": "<EMAIL>"}, {"id": "corp_003", "name": "Financial Services Health", "company": "Financial Services Inc", "contact_person": "Benefits Manager", "phone": "**********", "email": "<EMAIL>"}], "insurance": [{"id": "ins_001", "name": "HealthFirst Insurance", "type": "Health Insurance", "contact_person": "Claims Manager", "phone": "**********", "email": "<EMAIL>"}, {"id": "ins_002", "name": "MediCare Plus", "type": "Medical Insurance", "contact_person": "Provider Relations", "phone": "**********", "email": "<EMAIL>"}, {"id": "ins_003", "name": "Universal Health Coverage", "type": "Universal Insurance", "contact_person": "Network Manager", "phone": "**********", "email": "<EMAIL>"}], "staff": [{"id": "staff_001", "name": "Internal Medicine Department", "department": "Internal Medicine", "contact_person": "Department Head", "extension": "101"}, {"id": "staff_002", "name": "Emergency Department", "department": "Emergency", "contact_person": "ER Coordinator", "extension": "102"}, {"id": "staff_003", "name": "Outpatient Department", "department": "OPD", "contact_person": "OPD Manager", "extension": "103"}], "consultant": [{"id": "cons_001", "name": "Dr. Expert Consultant", "specialization": "Radiology Consultant", "affiliation": "Medical Imaging Center", "phone": "**********", "email": "<EMAIL>"}, {"id": "cons_002", "name": "Dr. Senior Advisor", "specialization": "Pathology Consultant", "affiliation": "Advanced Diagnostics", "phone": "**********", "email": "<EMAIL>"}], "outstation": [{"id": "out_001", "name": "Regional Medical Center", "location": "North Region", "contact_person": "Regional Coordinator", "phone": "**********", "email": "<EMAIL>"}, {"id": "out_002", "name": "District Hospital", "location": "South District", "contact_person": "District Manager", "phone": "**********", "email": "<EMAIL>"}], "self": [{"id": "self_001", "name": "Walk-in Patient", "description": "Patient came directly without referral"}, {"id": "self_002", "name": "Online Booking", "description": "Patient booked online without referral"}, {"id": "self_003", "name": "Phone Booking", "description": "Patient called directly for appointment"}]}}