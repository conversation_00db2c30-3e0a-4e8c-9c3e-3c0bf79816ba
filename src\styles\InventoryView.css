/* Inventory View Styles */

.inventory-view-container {
  padding: 1.5rem;
}

/* Item Detail Styles */
.item-detail {
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-weight: 600;
  color: var(--dark-gray);
  margin-bottom: 0.25rem;
}

.detail-value {
  color: var(--gray);
}

/* Item Description Styles */
.item-description h6 {
  margin-bottom: 0.5rem;
  color: var(--dark-gray);
}

.item-description p {
  color: var(--gray);
}

/* Stock Actions Styles */
.stock-actions {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

/* Chart Styles */
.chart-container {
  position: relative;
  margin: auto;
}

/* Responsive Styles */
@media (max-width: 767.98px) {
  .inventory-view-container {
    padding: 1rem;
  }
  
  .d-flex.justify-content-between {
    flex-direction: column;
    align-items: flex-start !important;
  }
  
  .d-flex.justify-content-between div {
    margin-top: 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}
