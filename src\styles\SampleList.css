/* Sample List Styles */

.sample-list-container {
  padding: 1rem;
}

/* Sample Mobile Cards */
.sample-mobile-card {
  margin-bottom: 1rem;
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color, #e0e0e0);
  overflow: hidden;
  transition: all 0.2s ease-in-out;
}

.sample-mobile-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--primary, #d4006e);
}

.sample-mobile-card .mobile-card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid var(--border-color, #e0e0e0);
  padding: 1rem;
}

.sample-mobile-card .mobile-card-title {
  font-weight: 600;
  font-size: 1rem;
  color: var(--dark-gray, #2c2c2c);
  margin-bottom: 0.25rem;
}

.sample-mobile-card .mobile-card-subtitle {
  font-size: 0.875rem;
  color: var(--medium-gray, #5a5a5a);
}

.sample-mobile-card .mobile-card-body {
  padding: 1rem;
}

.sample-mobile-card .mobile-card-field {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.sample-mobile-card .mobile-card-field:last-child {
  border-bottom: none;
}

.sample-mobile-card .mobile-card-label {
  font-weight: 600;
  color: var(--medium-gray, #5a5a5a);
  font-size: 0.875rem;
  flex: 0 0 45%;
}

.sample-mobile-card .mobile-card-value {
  font-size: 0.875rem;
  color: var(--dark-gray, #2c2c2c);
  text-align: right;
  flex: 1;
  font-weight: 500;
}

.sample-mobile-card .mobile-card-actions {
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-top: 1px solid var(--border-color, #e0e0e0);
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.sample-mobile-card .mobile-action-btn {
  min-height: 44px;
  min-width: 80px;
  font-size: 0.875rem;
  font-weight: 600;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.sample-mobile-card .mobile-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.sample-mobile-card .priority-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* Mobile Container Styles */
.mobile-sample-container .mobile-data-container {
  padding: 0.5rem;
}

.desktop-sample-container,
.mobile-sample-container {
  width: 100%;
  max-width: 100%;
}

/* Legacy Mobile Card Styles (for backward compatibility) */
.mobile-card {
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease;
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.mobile-card:active {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.mobile-card .card-header {
  background-color: var(--secondary);
  color: white;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  border-bottom: 2px solid var(--primary);
}

.mobile-card .card-body {
  padding: 1rem;
  font-size: 0.9rem;
}

/* Sample Info Styles */
.sample-info {
  margin-bottom: 1rem;
}

/* Responsive Styles */
@media (max-width: 767.98px) {
  .sample-list-container {
    padding: 0.5rem;
  }

  /* Sample mobile card responsive adjustments */
  .sample-mobile-card .mobile-card-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .sample-mobile-card .mobile-action-btn {
    width: 100%;
    min-width: auto;
  }

  .sample-mobile-card .mobile-card-field {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .sample-mobile-card .mobile-card-label {
    flex: none;
  }

  .sample-mobile-card .mobile-card-value {
    text-align: left;
    flex: none;
    width: 100%;
  }

  /* Legacy mobile card adjustments */
  .mobile-card .card-header {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
  }

  .mobile-card .card-body {
    padding: 0.75rem;
    font-size: 0.85rem;
  }
}

.sample-info .d-flex {
  margin-bottom: 0.5rem;
}

.sample-info strong {
  font-weight: 600;
  margin-right: 0.5rem;
  min-width: 80px;
}

/* Mobile Button Group */
.mobile-btn-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.mobile-btn-group .btn {
  flex: 1;
  min-width: 80px;
  margin-bottom: 0;
}

.mobile-btn-group form {
  flex: 1;
}

/* Record Count */
.record-count {
  text-align: center;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: var(--dark-gray);
}

.record-count .badge {
  font-size: 0.8rem;
  padding: 0.35em 0.65em;
}

/* Badge Styles */
.badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.35em 0.65em;
  border-radius: 50rem;
}

/* Pagination Styles */
.pagination {
  margin-bottom: 2rem;
}

.pagination .page-item .page-link {
  color: var(--primary);
  border-color: var(--border-color);
  transition: var(--transition);
}

.pagination .page-item.active .page-link {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

.pagination .page-item .page-link:hover {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
}

.pagination .page-item.disabled .page-link {
  color: var(--light-gray);
}

/* Responsive Styles */
@media (max-width: 767.98px) {
  .sample-list-container h1 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .d-sm-flex {
    display: block !important;
  }
  
  .d-sm-flex .btn {
    width: 100%;
    margin-top: 1rem;
  }
  
  .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .pagination .page-item {
    margin-bottom: 0.5rem;
  }
  
  .page-link {
    padding: 0.5rem 0.75rem;
    min-width: 40px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
