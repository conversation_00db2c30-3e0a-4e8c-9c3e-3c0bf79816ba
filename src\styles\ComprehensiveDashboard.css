/* Comprehensive Dashboard Styles */
.comprehensive-dashboard {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 1rem;
}

.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 10px;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dashboard-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

/* Dashboard Metrics */
.dashboard-metrics .card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border: none;
  border-radius: 10px;
}

.dashboard-metrics .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.border-left-primary {
  border-left: 4px solid #4e73df !important;
}

.border-left-success {
  border-left: 4px solid #1cc88a !important;
}

.border-left-info {
  border-left: 4px solid #36b9cc !important;
}

.border-left-warning {
  border-left: 4px solid #f6c23e !important;
}

.border-left-danger {
  border-left: 4px solid #e74a3b !important;
}

.border-left-secondary {
  border-left: 4px solid #858796 !important;
}

/* AI Insights Section */
.ai-insights-section .insight-card {
  transition: all 0.3s ease;
  border-radius: 10px;
}

.ai-insights-section .insight-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.ai-insights-section .insight-card.border-warning {
  border-left: 4px solid #f6c23e;
}

.recommendation-preview {
  background-color: #f8f9fa;
  padding: 0.5rem;
  border-radius: 5px;
  border-left: 3px solid #28a745;
}

/* Patient Management Section */
.patient-management-section .table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
}

.patient-management-section .badge {
  font-size: 0.75rem;
}

/* Reports Section */
.reports-section .card {
  transition: transform 0.2s ease;
}

.reports-section .card:hover {
  transform: translateY(-2px);
}

/* Invoice Management Section */
.invoice-management-section .table-responsive {
  border-radius: 10px;
  overflow: hidden;
}

/* Inventory Section */
.inventory-section .progress {
  height: 4px;
  border-radius: 2px;
}

.inventory-section .card {
  border-radius: 10px;
}

/* Financial Accounts Section */
.financial-accounts-section .card {
  border-radius: 10px;
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.financial-accounts-section .opacity-25 {
  opacity: 0.25;
}

/* Tab Navigation */
.card-header-tabs {
  border-bottom: none;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.card-header-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.card-header-tabs .nav-link {
  border: none;
  color: #6c757d;
  font-weight: 500;
  padding: 0.75rem 1rem;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: fit-content;
}

.card-header-tabs .nav-link:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.card-header-tabs .nav-link.active {
  background-color: #fff;
  color: #495057;
  border-bottom: 3px solid #007bff;
  font-weight: 600;
}

/* Mobile Tab Navigation */
@media (max-width: 768px) {
  .card-header-tabs {
    padding: 0 0.5rem;
  }

  .card-header-tabs .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .comprehensive-dashboard {
    padding: 0.5rem;
  }
  
  .dashboard-header {
    padding: 1rem;
    margin-bottom: 1rem;
  }
  
  .dashboard-header h1 {
    font-size: 1.5rem;
  }
  
  .card-header-tabs {
    flex-wrap: wrap;
  }
  
  .card-header-tabs .nav-link {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }
  
  .btn-group {
    flex-wrap: wrap;
  }
  
  .btn-group .btn {
    margin-bottom: 0.25rem;
  }
  
  .table-responsive {
    font-size: 0.875rem;
  }
  
  .d-flex.gap-2 {
    flex-wrap: wrap;
    gap: 0.5rem !important;
  }
}

@media (max-width: 576px) {
  .comprehensive-dashboard {
    padding: 0.25rem;
  }
  
  .dashboard-header {
    text-align: center;
  }
  
  .dashboard-header .row {
    flex-direction: column;
  }
  
  .card-header-tabs .nav-link {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
  }
  
  .btn-sm {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
  
  .h3 {
    font-size: 1.25rem;
  }
  
  .fa-2x {
    font-size: 1.5em !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .comprehensive-dashboard {
    background-color: #1a1a1a;
    color: #ffffff;
  }
  
  .card {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .table {
    color: #ffffff;
  }
  
  .table-light {
    background-color: #404040;
    color: #ffffff;
  }
  
  .text-muted {
    color: #adb5bd !important;
  }
  
  .bg-light {
    background-color: #2d2d2d !important;
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Custom scrollbar */
.table-responsive::-webkit-scrollbar {
  height: 6px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Print styles */
@media print {
  .comprehensive-dashboard {
    background-color: white !important;
    color: black !important;
  }
  
  .btn, .nav-tabs {
    display: none !important;
  }
  
  .card {
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }
}
