#!/usr/bin/env python3
"""
Check billing reports structure for notes field and financial summary issues
"""
import sys
import os
import json

# Add backend directory to path
sys.path.append('backend')

def check_billing_reports_structure():
    """Check billing reports for missing fields and calculation issues"""
    
    try:
        from utils import read_data
        
        print("🔧 Checking Billing Reports Structure...")
        
        # Load billing reports
        billing_reports = read_data('billing_reports.json')
        print(f"Total billing reports: {len(billing_reports)}")
        
        # Check first few reports for structure
        for i, report in enumerate(billing_reports[:3]):
            sid = report.get('sid_number', 'Unknown')
            print(f"\nReport {i+1} (SID: {sid}):")
            print(f"  Has notes field: {'notes' in report}")
            print(f"  Has financial_summary: {'financial_summary' in report}")
            
            if 'financial_summary' in report:
                fs = report['financial_summary']
                print(f"  Total amount: {fs.get('total_amount', 0)}")
                print(f"  Financial summary keys: {list(fs.keys())}")
            
            # Check if any test items have notes
            test_items = report.get('test_items', [])
            test_notes_count = sum(1 for t in test_items if t.get('notes'))
            print(f"  Test items with notes: {test_notes_count}/{len(test_items)}")
        
        # Look specifically for SID MYD109
        myd109_report = next((r for r in billing_reports if r.get('sid_number') == 'MYD109'), None)
        if myd109_report:
            print(f"\nMYD109 Report:")
            print(f"  Has notes field: {'notes' in myd109_report}")
            print(f"  Keys: {list(myd109_report.keys())}")
            
            # Check financial summary
            if 'financial_summary' in myd109_report:
                fs = myd109_report['financial_summary']
                print(f"  Financial summary: {fs}")
        else:
            print(f"\nMYD109 Report not found")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def check_calculation_consistency():
    """Check if billing reports totals match billing data"""
    
    try:
        from utils import read_data
        
        print(f"\n🔧 Checking Calculation Consistency...")
        
        # Load both billing and billing reports
        billings = read_data('billings.json')
        billing_reports = read_data('billing_reports.json')
        
        # Check a few billing records and their corresponding reports
        for billing in billings[:5]:
            billing_id = billing.get('id')
            billing_total = billing.get('total_amount', 0)
            
            # Find corresponding report
            report = next((r for r in billing_reports if r.get('billing_id') == billing_id), None)
            
            if report:
                report_total = report.get('financial_summary', {}).get('total_amount', 0)
                print(f"  Billing {billing_id}: Billing={billing_total}, Report={report_total}, Match={'✅' if abs(billing_total - report_total) < 0.01 else '❌'}")
            else:
                print(f"  Billing {billing_id}: No corresponding report found")
                
    except Exception as e:
        print(f"❌ Error in calculation check: {str(e)}")

if __name__ == "__main__":
    print("🔍 BILLING REPORTS STRUCTURE CHECK")
    print("=" * 50)
    
    check_billing_reports_structure()
    check_calculation_consistency()
    
    print("\n" + "=" * 50)
    print("🔍 CHECK COMPLETE")
