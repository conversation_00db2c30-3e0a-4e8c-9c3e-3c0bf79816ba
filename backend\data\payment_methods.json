[{"id": 1, "method_name": "Cash", "method_code": "CASH", "description": "Cash payment", "is_online": false, "processing_fee": 0, "is_active": true, "created_at": "2024-01-15T08:00:00", "updated_at": "2025-06-11T18:38:23.786780", "created_by": 1, "name": "test"}, {"id": 2, "method_name": "Credit Card", "method_code": "CC", "description": "Credit card payment", "is_online": true, "processing_fee": 2.5, "is_active": true, "created_at": "2024-01-15T08:05:00", "updated_at": "2024-01-15T08:05:00", "created_by": 1}, {"id": 3, "method_name": "Debit Card", "method_code": "DC", "description": "Debit card payment", "is_online": true, "processing_fee": 1.5, "is_active": true, "created_at": "2024-01-15T08:10:00", "updated_at": "2024-01-15T08:10:00", "created_by": 1}, {"id": 4, "method_name": "UPI", "method_code": "UPI", "description": "Unified Payments Interface", "is_online": true, "processing_fee": 0, "is_active": true, "created_at": "2024-01-15T08:15:00", "updated_at": "2024-01-15T08:15:00", "created_by": 1}, {"id": 5, "method_name": "Net Banking", "method_code": "NB", "description": "Online banking transfer", "is_online": true, "processing_fee": 1.0, "is_active": true, "created_at": "2024-01-15T08:20:00", "updated_at": "2024-01-15T08:20:00", "created_by": 1}, {"id": 6, "method_name": "Cheque", "method_code": "CHQ", "description": "Bank cheque payment", "is_online": false, "processing_fee": 0, "is_active": true, "created_at": "2024-01-15T08:25:00", "updated_at": "2024-01-15T08:25:00", "created_by": 1}]