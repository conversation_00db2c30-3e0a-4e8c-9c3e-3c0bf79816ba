[{"Test Name": "ACETONE (URINE)", "code": 315, "Department": "Clinical pathology", "Referance Range": "NEGATIVE", "Price": 100, "Result Type": "Pick List", "Short Name": "UACE", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "BENCE JONES PROTEIN-URINE", "code": 316, "Department": "Clinical pathology", "Referance Range": "NEGATIVE", "Price": 100, "Result Type": "Pick List", "Short Name": "BJP", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Bile Pigment", "code": 1635, "Department": "Clinical pathology", "Referance Range": "Negative", "Price": 30, "Result Type": "Pick List", "Specimen Code": 56, "Specimen": "URINE", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Bile Salt", "code": 1634, "Department": "Clinical pathology", "Referance Range": "Absent", "Price": 30, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Cell Count (Body Fluids)", "code": 317, "Department": "Clinical pathology", "Price": 200, "Result Type": "Pick List", "Short Name": "CCBF", "Method code": 110, "Method": "Microscopic", "Specimen Code": 11, "Specimen": "Body Fluids", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "both"}, {"Test Name": "Cell count (CSF)", "code": 313, "Department": "Clinical pathology", "Price": 300, "Result Type": "Pick List", "Short Name": "CCSF", "Specimen Code": 12, "Specimen": "CSF", "      Test Done On": "all", "Applicable to": "both"}, {"Test Name": "Chyluria", "code": 311, "Department": "Clinical pathology", "Referance Range": "Negative", "Price": 250, "Result Type": "Pick List", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "both"}, {"Test Name": "Color & Transparency", "code": 1636, "Department": "Clinical pathology", "Price": 0, "Result Type": "Pick List", "Specimen Code": 56, "Specimen": "URINE", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "DNA FRAGMENTATION INDEX", "code": 1648, "Department": "Clinical pathology", "Price": 5000, "Result Type": "Pick List", "Short Name": "DFI", "Primary specimen code": 38, "Primary specimen ": "SEMEN", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Male"}, {"Test Name": "FAT GLOBULES (STOOL)", "code": 318, "Department": "Clinical pathology", "Referance Range": "Not Present", "Price": 100, "Result Type": "Pick List", "Specimen Code": 47, "Specimen": "STOOL", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "both"}, {"Test Name": "INTRAUTERINE INSEMINATION -SPERM WASH", "code": 1704, "Department": "Clinical pathology", "Price": 2000, "Result Type": "Pick List", "Short Name": "IUI-SPERM", "Primary specimen code": 38, "Primary specimen ": "SEMEN", "      Test Done On": "all", "Applicable to": "Male"}, {"Test Name": "OCCULT BLOOD.", "code": 320, "Department": "Clinical pathology", "Price": 100, "Result Type": "Pick List", "Short Name": "OCC", "Method code": 78, "Method": "Guaiac", "Specimen Code": 47, "Specimen": "STOOL", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PREGNANCY TEST-URINE (CARD)", "code": 321, "Department": "Clinical pathology", "Price": 100, "Result Type": "Pick List", "Short Name": "UPC", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "REDUCING SUBSTANCE.", "code": 322, "Department": "Clinical pathology", "Price": 100, "Result Type": "Pick List", "Specimen Code": 27, "Specimen": "MOTION/URINE", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "SEMEN ANALYSIS", "code": 323, "Department": "Clinical pathology", "Price": 1000, "Result Type": "Pick List", "Short Name": "SEM", "Specimen Code": 38, "Specimen": "SEMEN", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Min. Process Time": 6, "      Test Done On": "all", "Applicable to": "Male"}, {"Test Name": "SEMEN FRUCTOSE", "code": 145, "Department": "Clinical pathology", "Price": 150, "Result Type": "Pick List", "Specimen Code": 38, "Specimen": "SEMEN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "SPERM MOTILITY ANALYSIS-SEMEN", "code": 1646, "Department": "Clinical pathology", "Price": 550, "Result Type": "Pick List", "Specimen Code": 38, "Specimen": "SEMEN", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Stool complete examination", "code": 319, "Department": "Clinical pathology", "Price": 150, "Result Type": "Pick List", "Short Name": "STC", "Specimen Code": 47, "Specimen": "STOOL", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Stool Ova & Cysts", "code": 1654, "Department": "Clinical pathology", "Price": 150, "Result Type": "Pick List", "Primary specimen code": 47, "Primary specimen ": "STOOL", "Specimen Code": 47, "Specimen": "STOOL", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE ACETONE", "code": 408, "Department": "Clinical pathology", "Referance Range": "NEGATIVE", "Price": 100, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE ALBUMIN", "code": 325, "Department": "Clinical pathology", "Referance Range": "Not present  .", "Price": 30, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Urine Chemical Analysis.", "code": 1521, "Department": "Clinical pathology", "Price": 1, "Result Type": "Pick List", "Short Name": "UR", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Urine Complete Examination", "code": 324, "Department": "Clinical pathology", "Price": 100, "Result Type": "Pick List", "Short Name": "UCE", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Urine Dysmorphic RBC", "code": 1611, "Department": "Clinical pathology", "Referance Range": "Negative", "Price": 800, "Result Type": "Pick List", "Short Name": "UDR", "Method code": 110, "Method": "Microscopic", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er"}, {"Test Name": "URINE OCCULT BLOOD", "code": 413, "Department": "Clinical pathology", "Price": 100, "Result Type": "Pick List", "Method code": 78, "Method": "Guaiac", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Urine Routine Analysis", "code": 414, "Department": "Clinical pathology", "Price": 1, "Result Type": "Pick List", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE SUGAR", "code": 303, "Department": "Clinical pathology", "Referance Range": "Not Present", "Price": 10, "Result Type": "Pick List", "Short Name": "US", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE SUGAR (120 mins)", "code": 307, "Department": "Clinical pathology", "Price": 10, "Result Type": "Pick List", "Short Name": 120, "Specimen Code": 55, "Specimen": "URINE", "Container Code": 3, "Container": "URINE Container", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE SUGAR (150 mins)", "code": 309, "Department": "Clinical pathology", "Price": 10, "Result Type": "Pick List", "Short Name": 150, "Specimen Code": 55, "Specimen": "URINE", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE SUGAR (180 mins)", "code": 308, "Department": "Clinical pathology", "Price": 10, "Result Type": "Pick List", "Short Name": 180, "Specimen Code": 55, "Specimen": "URINE", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE SUGAR (30 mins)", "code": 304, "Department": "Clinical pathology", "Price": 10, "Result Type": "Pick List", "Short Name": 30, "Specimen Code": 55, "Specimen": "URINE", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE SUGAR (60 mins)", "code": 305, "Department": "Clinical pathology", "Price": 10, "Result Type": "Pick List", "Short Name": 60, "Specimen Code": 55, "Specimen": "URINE", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE SUGAR (90 mins)", "code": 306, "Department": "Clinical pathology", "Price": 10, "Result Type": "Pick List", "Short Name": 90, "Specimen Code": 55, "Specimen": "URINE", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE SUGAR (F)", "code": 301, "Department": "Clinical pathology", "Referance Range": "Not Present.", "Price": 10, "Result Type": "Pick List", "Short Name": "USF", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE SUGAR (PP)", "code": 302, "Department": "Clinical pathology", "Referance Range": "Not Present", "Price": 10, "Result Type": "Pick List", "Short Name": "USPP", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}]