[{"id": 1, "invoice_number": "INV00001", "patient_id": 6, "items": [{"id": 1, "test_id": 4, "test_name": "Lipid Profile", "quantity": 3, "price": 600, "amount": 1800}, {"id": 2, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 1, "price": 150, "amount": 150}, {"id": 3, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 2, "price": 350, "amount": 700}], "subtotal": 2650, "discount": 10.47, "tax": 477.0, "total_amount": 3116.53, "paid_amount": 3116.53, "balance": 0, "payment_method": "UPI", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-27", "due_date": "2025-05-27", "created_at": "2025-04-27T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.340256", "tenant_id": 1, "created_by": 2, "sid_number": "MYD001"}, {"id": 2, "invoice_number": "INV00002", "patient_id": 29, "items": [{"id": 1, "test_id": 4, "test_name": "Lipid Profile", "quantity": 2, "price": 600, "amount": 1200}, {"id": 2, "test_id": 5, "test_name": "Liver Function Test", "quantity": 3, "price": 800, "amount": 2400}, {"id": 3, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}], "subtotal": 4050, "discount": 475.17, "tax": 729.0, "total_amount": 4303.83, "paid_amount": 0, "balance": 4303.83, "payment_method": "Card", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-05-08", "due_date": "2025-06-07", "created_at": "2025-05-08T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.341183", "tenant_id": 3, "created_by": 3, "sid_number": "TNJ002"}, {"id": 3, "invoice_number": "INV00003", "patient_id": 33, "items": [{"id": 1, "test_id": 3, "test_name": "HbA1c", "quantity": 3, "price": 450, "amount": 1350}], "subtotal": 1350, "discount": 94.44, "tax": 243.0, "total_amount": 1498.56, "paid_amount": 1273.46, "balance": 225.0999999999999, "payment_method": "Cash", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-04-04", "due_date": "2025-05-04", "created_at": "2025-04-04T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.341183", "tenant_id": 2, "created_by": 1, "sid_number": "SKZ003"}, {"id": 4, "invoice_number": "INV00004", "patient_id": 12, "items": [{"id": 1, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 1, "price": 350, "amount": 350}, {"id": 2, "test_id": 7, "test_name": "Thyroid Profile", "quantity": 1, "price": 850, "amount": 850}], "subtotal": 1200, "discount": 94.55, "tax": 216.0, "total_amount": 1321.45, "paid_amount": 1321.45, "balance": 0, "payment_method": "Cash", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-04", "due_date": "2025-05-04", "created_at": "2025-04-04T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.341183", "tenant_id": 3, "created_by": 1, "sid_number": "TNJ004"}, {"id": 5, "invoice_number": "INV00005", "patient_id": 31, "items": [{"id": 1, "test_id": 4, "test_name": "Lipid Profile", "quantity": 2, "price": 600, "amount": 1200}, {"id": 2, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}, {"id": 3, "test_id": 9, "test_name": "Stool Routine", "quantity": 1, "price": 250, "amount": 250}], "subtotal": 1900, "discount": 28.52, "tax": 342.0, "total_amount": 2213.48, "paid_amount": 2213.48, "balance": 0, "payment_method": "Cash", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-12", "due_date": "2025-05-12", "created_at": "2025-04-12T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.342180", "tenant_id": 1, "created_by": 1, "sid_number": "MYD005"}, {"id": 6, "invoice_number": "INV00006", "patient_id": 31, "items": [{"id": 1, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 2, "price": 350, "amount": 700}, {"id": 2, "test_id": 4, "test_name": "Lipid Profile", "quantity": 2, "price": 600, "amount": 1200}, {"id": 3, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 1, "price": 700, "amount": 700}, {"id": 4, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 3, "price": 700, "amount": 2100}], "subtotal": 4700, "discount": 441.76, "tax": 846.0, "total_amount": 5104.24, "paid_amount": 5104.24, "balance": 0, "payment_method": "Bank Transfer", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-18", "due_date": "2025-05-18", "created_at": "2025-04-18T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.342180", "tenant_id": 3, "created_by": 1, "sid_number": "TNJ006"}, {"id": 7, "invoice_number": "INV00007", "patient_id": 40, "items": [{"id": 1, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 2, "price": 350, "amount": 700}, {"id": 2, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 2, "price": 700, "amount": 1400}, {"id": 3, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}], "subtotal": 2550, "discount": 72.01, "tax": 459.0, "total_amount": 2936.99, "paid_amount": 0, "balance": 2936.99, "payment_method": "Bank Transfer", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-04-07", "due_date": "2025-05-07", "created_at": "2025-04-07T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.342180", "tenant_id": 3, "created_by": 2, "sid_number": "TNJ007"}, {"id": 8, "invoice_number": "INV00008", "patient_id": 14, "items": [{"id": 1, "test_id": 9, "test_name": "Stool Routine", "quantity": 1, "price": 250, "amount": 250}, {"id": 2, "test_id": 4, "test_name": "Lipid Profile", "quantity": 3, "price": 600, "amount": 1800}], "subtotal": 2050, "discount": 125.58, "tax": 369.0, "total_amount": 2293.42, "paid_amount": 2293.42, "balance": 0, "payment_method": "Cash", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-05-05", "due_date": "2025-06-04", "created_at": "2025-05-05T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.342180", "tenant_id": 2, "created_by": 2, "sid_number": "SKZ008"}, {"id": 9, "invoice_number": "INV00009", "patient_id": 50, "items": [{"id": 1, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 3, "price": 200, "amount": 600}, {"id": 2, "test_id": 9, "test_name": "Stool Routine", "quantity": 1, "price": 250, "amount": 250}], "subtotal": 850, "discount": 19.72, "tax": 153.0, "total_amount": 983.28, "paid_amount": 0, "balance": 983.28, "payment_method": "Card", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-03-23", "due_date": "2025-04-22", "created_at": "2025-03-23T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.343181", "tenant_id": 2, "created_by": 1, "sid_number": "SKZ009"}, {"id": 10, "invoice_number": "INV00010", "patient_id": 29, "items": [{"id": 1, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 2, "price": 150, "amount": 300}, {"id": 2, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 1, "price": 200, "amount": 200}, {"id": 3, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}, {"id": 4, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 2, "price": 350, "amount": 700}], "subtotal": 1650, "discount": 317.03, "tax": 297.0, "total_amount": 1629.97, "paid_amount": 0, "balance": 1629.97, "payment_method": "UPI", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-03-28", "due_date": "2025-04-27", "created_at": "2025-03-28T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.343181", "tenant_id": 3, "created_by": 3, "sid_number": "TNJ010"}, {"id": 11, "invoice_number": "INV00011", "patient_id": 13, "items": [{"id": 1, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}, {"id": 2, "test_id": 5, "test_name": "Liver Function Test", "quantity": 2, "price": 800, "amount": 1600}, {"id": 3, "test_id": 7, "test_name": "Thyroid Profile", "quantity": 3, "price": 850, "amount": 2550}, {"id": 4, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 1, "price": 700, "amount": 700}, {"id": 5, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 2, "price": 200, "amount": 400}], "subtotal": 5700, "discount": 1023.75, "tax": 1026.0, "total_amount": 5702.25, "paid_amount": 0, "balance": 5702.25, "payment_method": "Bank Transfer", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-05-07", "due_date": "2025-06-06", "created_at": "2025-05-07T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.343181", "tenant_id": 3, "created_by": 2, "sid_number": "TNJ011"}, {"id": 12, "invoice_number": "INV00012", "patient_id": 40, "items": [{"id": 1, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}, {"id": 2, "test_id": 7, "test_name": "Thyroid Profile", "quantity": 3, "price": 850, "amount": 2550}, {"id": 3, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 1, "price": 200, "amount": 200}], "subtotal": 3200, "discount": 514.27, "tax": 576.0, "total_amount": 3261.73, "paid_amount": 0, "balance": 3261.73, "payment_method": "Bank Transfer", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-10", "due_date": "2025-07-10", "created_at": "2025-06-10T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.343181", "tenant_id": 3, "created_by": 2, "sid_number": "TNJ012"}, {"id": 13, "invoice_number": "INV00013", "patient_id": 42, "items": [{"id": 1, "test_id": 5, "test_name": "Liver Function Test", "quantity": 3, "price": 800, "amount": 2400}], "subtotal": 2400, "discount": 398.17, "tax": 432.0, "total_amount": 2433.83, "paid_amount": 0, "balance": 2433.83, "payment_method": "Card", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-05", "due_date": "2025-07-05", "created_at": "2025-06-05T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.344182", "tenant_id": 3, "created_by": 2, "sid_number": "TNJ013"}, {"id": 14, "invoice_number": "INV00014", "patient_id": 23, "items": [{"id": 1, "test_id": 9, "test_name": "Stool Routine", "quantity": 3, "price": 250, "amount": 750}, {"id": 2, "test_id": 4, "test_name": "Lipid Profile", "quantity": 3, "price": 600, "amount": 1800}, {"id": 3, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 2, "price": 700, "amount": 1400}], "subtotal": 3950, "discount": 426.16, "tax": 711.0, "total_amount": 4234.84, "paid_amount": 0, "balance": 4234.84, "payment_method": "Cash", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-05-18", "due_date": "2025-06-17", "created_at": "2025-05-18T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.344182", "tenant_id": 2, "created_by": 2, "sid_number": "SKZ014"}, {"id": 15, "invoice_number": "INV00015", "patient_id": 31, "items": [{"id": 1, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 2, "price": 350, "amount": 700}, {"id": 2, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 3, "price": 200, "amount": 600}, {"id": 3, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}, {"id": 4, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 3, "price": 350, "amount": 1050}], "subtotal": 2800, "discount": 303.39, "tax": 504.0, "total_amount": 3000.61, "paid_amount": 0, "balance": 3000.61, "payment_method": "UPI", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-05-04", "due_date": "2025-06-03", "created_at": "2025-05-04T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.344182", "tenant_id": 1, "created_by": 3, "sid_number": "MYD015"}, {"id": 16, "invoice_number": "INV00016", "patient_id": 27, "items": [{"id": 1, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 1, "price": 700, "amount": 700}, {"id": 2, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 2, "price": 150, "amount": 300}], "subtotal": 1000, "discount": 176.53, "tax": 180.0, "total_amount": 1003.47, "paid_amount": 1003.47, "balance": 0, "payment_method": "UPI", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-09", "due_date": "2025-05-09", "created_at": "2025-04-09T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.345181", "tenant_id": 1, "created_by": 1, "sid_number": "MYD016"}, {"id": 17, "invoice_number": "INV00017", "patient_id": 16, "items": [{"id": 1, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 2, "price": 700, "amount": 1400}, {"id": 2, "test_id": 7, "test_name": "Thyroid Profile", "quantity": 2, "price": 850, "amount": 1700}, {"id": 3, "test_id": 5, "test_name": "Liver Function Test", "quantity": 1, "price": 800, "amount": 800}, {"id": 4, "test_id": 3, "test_name": "HbA1c", "quantity": 3, "price": 450, "amount": 1350}, {"id": 5, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 1, "price": 700, "amount": 700}], "subtotal": 5950, "discount": 885.62, "tax": 1071.0, "total_amount": 6135.38, "paid_amount": 6135.38, "balance": 0, "payment_method": "Card", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-03-30", "due_date": "2025-04-29", "created_at": "2025-03-30T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.345181", "tenant_id": 3, "created_by": 2, "sid_number": "TNJ017"}, {"id": 18, "invoice_number": "INV00018", "patient_id": 36, "items": [{"id": 1, "test_id": 4, "test_name": "Lipid Profile", "quantity": 1, "price": 600, "amount": 600}], "subtotal": 600, "discount": 25.32, "tax": 108.0, "total_amount": 682.68, "paid_amount": 0, "balance": 682.68, "payment_method": "Cash", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-05-06", "due_date": "2025-06-05", "created_at": "2025-05-06T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.345181", "tenant_id": 3, "created_by": 2, "sid_number": "TNJ018"}, {"id": 19, "invoice_number": "INV00019", "patient_id": 37, "items": [{"id": 1, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}, {"id": 2, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 2, "price": 700, "amount": 1400}, {"id": 3, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 2, "price": 700, "amount": 1400}], "subtotal": 3250, "discount": 398.74, "tax": 585.0, "total_amount": 3436.26, "paid_amount": 1501.97, "balance": 1934.2900000000002, "payment_method": "UPI", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-04-29", "due_date": "2025-05-29", "created_at": "2025-04-29T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.345181", "tenant_id": 1, "created_by": 3, "sid_number": "MYD019"}, {"id": 20, "invoice_number": "INV00020", "patient_id": 23, "items": [{"id": 1, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 2, "price": 200, "amount": 400}, {"id": 2, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 3, "price": 700, "amount": 2100}, {"id": 3, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 1, "price": 350, "amount": 350}, {"id": 4, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 1, "price": 200, "amount": 200}, {"id": 5, "test_id": 4, "test_name": "Lipid Profile", "quantity": 3, "price": 600, "amount": 1800}], "subtotal": 4850, "discount": 252.63, "tax": 873.0, "total_amount": 5470.37, "paid_amount": 5470.37, "balance": 0, "payment_method": "Cash", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-05", "due_date": "2025-05-05", "created_at": "2025-04-05T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.346191", "tenant_id": 1, "created_by": 1, "sid_number": "MYD020"}, {"id": 21, "invoice_number": "INV00021", "patient_id": 4, "items": [{"id": 1, "test_id": 5, "test_name": "Liver Function Test", "quantity": 3, "price": 800, "amount": 2400}, {"id": 2, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 1, "price": 200, "amount": 200}], "subtotal": 2600, "discount": 140.02, "tax": 468.0, "total_amount": 2927.98, "paid_amount": 2927.98, "balance": 0, "payment_method": "Bank Transfer", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-05", "due_date": "2025-05-05", "created_at": "2025-04-05T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.346191", "tenant_id": 2, "created_by": 1, "sid_number": "SKZ021"}, {"id": 22, "invoice_number": "INV00022", "patient_id": 22, "items": [{"id": 1, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 3, "price": 350, "amount": 1050}, {"id": 2, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 2, "price": 150, "amount": 300}, {"id": 3, "test_id": 5, "test_name": "Liver Function Test", "quantity": 2, "price": 800, "amount": 1600}, {"id": 4, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}], "subtotal": 3400, "discount": 616.45, "tax": 612.0, "total_amount": 3395.55, "paid_amount": 711.93, "balance": 2683.*************, "payment_method": "Card", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-04-03", "due_date": "2025-05-03", "created_at": "2025-04-03T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.346191", "tenant_id": 1, "created_by": 3, "sid_number": "MYD022"}, {"id": 23, "invoice_number": "INV00023", "patient_id": 28, "items": [{"id": 1, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 3, "price": 350, "amount": 1050}, {"id": 2, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 1, "price": 700, "amount": 700}, {"id": 3, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 2, "price": 200, "amount": 400}, {"id": 4, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 1, "price": 350, "amount": 350}, {"id": 5, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 2, "price": 200, "amount": 400}], "subtotal": 2900, "discount": 179.42, "tax": 522.0, "total_amount": 3242.58, "paid_amount": 598.59, "balance": 2643.99, "payment_method": "Bank Transfer", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-03-27", "due_date": "2025-04-26", "created_at": "2025-03-27T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.347183", "tenant_id": 1, "created_by": 2, "sid_number": "MYD023"}, {"id": 24, "invoice_number": "INV00024", "patient_id": 40, "items": [{"id": 1, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}, {"id": 2, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 2, "price": 150, "amount": 300}, {"id": 3, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 3, "price": 700, "amount": 2100}, {"id": 4, "test_id": 3, "test_name": "HbA1c", "quantity": 3, "price": 450, "amount": 1350}], "subtotal": 4200, "discount": 284.55, "tax": 756.0, "total_amount": 4671.45, "paid_amount": 3954.97, "balance": 716.48, "payment_method": "Bank Transfer", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-04-03", "due_date": "2025-05-03", "created_at": "2025-04-03T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.347601", "tenant_id": 1, "created_by": 2, "sid_number": "MYD024"}, {"id": 25, "invoice_number": "INV00025", "patient_id": 20, "items": [{"id": 1, "test_id": 9, "test_name": "Stool Routine", "quantity": 1, "price": 250, "amount": 250}], "subtotal": 250, "discount": 7.14, "tax": 45.0, "total_amount": 287.86, "paid_amount": 89.33, "balance": 198.**************, "payment_method": "UPI", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-06-04", "due_date": "2025-07-04", "created_at": "2025-06-04T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.347601", "tenant_id": 1, "created_by": 2, "sid_number": "MYD025"}, {"id": 26, "invoice_number": "INV00026", "patient_id": 25, "items": [{"id": 1, "test_id": 3, "test_name": "HbA1c", "quantity": 2, "price": 450, "amount": 900}, {"id": 2, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 3, "price": 350, "amount": 1050}, {"id": 3, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 3, "price": 700, "amount": 2100}, {"id": 4, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 1, "price": 350, "amount": 350}, {"id": 5, "test_id": 4, "test_name": "Lipid Profile", "quantity": 1, "price": 600, "amount": 600}], "subtotal": 5000, "discount": 275.61, "tax": 900.0, "total_amount": 5624.39, "paid_amount": 1133.07, "balance": 4491.************, "payment_method": "Bank Transfer", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-05-28", "due_date": "2025-06-27", "created_at": "2025-05-28T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.347601", "tenant_id": 2, "created_by": 1, "sid_number": "SKZ026"}, {"id": 27, "invoice_number": "INV00027", "patient_id": 31, "items": [{"id": 1, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 1, "price": 700, "amount": 700}], "subtotal": 700, "discount": 60.48, "tax": 126.0, "total_amount": 765.52, "paid_amount": 765.52, "balance": 765.52, "payment_method": "Bank Transfer", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-03-30", "due_date": "2025-04-29", "created_at": "2025-03-30T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.347601", "tenant_id": 1, "created_by": 3, "sid_number": "MYD027", "last_payment_date": "2025-07-16T13:14:16.221538", "last_payment_method": "Cash", "last_payment_reference": "79", "payment_history": [{"payment_date": "2025-07-16T13:14:16.221560", "amount": 765.52, "method": "Cash", "reference": "79", "notes": "", "processed_by": 4, "remaining_due": 0.0}]}, {"id": 28, "invoice_number": "INV00028", "patient_id": 4, "items": [{"id": 1, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 2, "price": 150, "amount": 300}, {"id": 2, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}], "subtotal": 750, "discount": 142.96, "tax": 135.0, "total_amount": 742.04, "paid_amount": 0, "balance": 742.04, "payment_method": "UPI", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-04-03", "due_date": "2025-05-03", "created_at": "2025-04-03T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.348638", "tenant_id": 1, "created_by": 2, "sid_number": "MYD028"}, {"id": 29, "invoice_number": "INV00029", "patient_id": 18, "items": [{"id": 1, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 1, "price": 700, "amount": 700}, {"id": 2, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 3, "price": 350, "amount": 1050}, {"id": 3, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 2, "price": 700, "amount": 1400}, {"id": 4, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 2, "price": 350, "amount": 700}, {"id": 5, "test_id": 4, "test_name": "Lipid Profile", "quantity": 3, "price": 600, "amount": 1800}], "subtotal": 5650, "discount": 476.17, "tax": 1017.0, "total_amount": 6190.83, "paid_amount": 0, "balance": 6190.83, "payment_method": "Bank Transfer", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-05-04", "due_date": "2025-06-03", "created_at": "2025-05-04T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.348638", "tenant_id": 3, "created_by": 1, "sid_number": "TNJ029"}, {"id": 30, "invoice_number": "INV00030", "patient_id": 4, "items": [{"id": 1, "test_id": 5, "test_name": "Liver Function Test", "quantity": 2, "price": 800, "amount": 1600}, {"id": 2, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 2, "price": 150, "amount": 300}, {"id": 3, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 2, "price": 150, "amount": 300}, {"id": 4, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 3, "price": 700, "amount": 2100}, {"id": 5, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}], "subtotal": 4750, "discount": 594.54, "tax": 855.0, "total_amount": 5010.46, "paid_amount": 542.76, "balance": 4467.7, "payment_method": "Card", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-04-02", "due_date": "2025-05-02", "created_at": "2025-04-02T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.348638", "tenant_id": 2, "created_by": 3, "sid_number": "SKZ030"}, {"id": 31, "invoice_number": "INV00031", "sid_number": "MYD003", "patient_id": 34, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1750436899987, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}], "bill_amount": 800, "other_charges": 0, "discount_percent": 0, "subtotal": 944, "discount": 0, "gst_rate": 18, "gst_amount": 144, "tax": 144, "total_amount": 944, "paid_amount": 0, "balance": 944, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-20", "due_date": "2025-07-20", "notes": "", "branch": "1", "created_at": "2025-06-20T21:58:22.614664", "updated_at": "2025-06-25T14:43:56.710105", "tenant_id": 1, "created_by": 4}, {"id": 32, "invoice_number": "INV00032", "sid_number": "SKZ013", "patient_id": 37, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1750490226713, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}], "bill_amount": 800, "other_charges": 0, "discount_percent": 0, "subtotal": 944, "discount": 0, "gst_rate": 18, "gst_amount": 144, "tax": 144, "total_amount": 944, "paid_amount": 944.0, "balance": 944, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-06-21", "due_date": "2025-07-21", "notes": "", "branch": "2", "created_at": "2025-06-21T12:47:09.145759", "updated_at": "2025-06-21T14:31:12.269097", "tenant_id": 2, "created_by": 5, "last_payment_date": "2025-07-16T13:12:36.897759", "last_payment_method": "Cash", "last_payment_reference": "76876", "payment_history": [{"payment_date": "2025-07-16T13:12:36.897775", "amount": 944.0, "method": "Cash", "reference": "76876", "notes": "", "processed_by": 4, "remaining_due": 0.0}]}, {"id": 33, "invoice_number": "INV00033", "sid_number": "MYD002", "patient_id": 34, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1750495890087, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}], "bill_amount": 800, "other_charges": 0, "discount_percent": 0, "subtotal": 944, "discount": 0, "gst_rate": 18, "gst_amount": 144, "tax": 144, "total_amount": 944, "paid_amount": 0, "balance": 944, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-21", "due_date": "2025-07-21", "notes": "", "branch": "1", "created_at": "2025-06-21T14:21:32.518940", "updated_at": "2025-06-21T14:21:32.518940", "tenant_id": 1, "created_by": 4}, {"id": 34, "invoice_number": "INV00034", "sid_number": "SKZ001", "patient_id": 37, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1750496536278, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}], "bill_amount": 800, "other_charges": 0, "discount_percent": 0, "subtotal": 944, "discount": 0, "gst_rate": 18, "gst_amount": 144, "tax": 144, "total_amount": 944, "paid_amount": 944.0, "balance": 0.0, "payment_method": "Cash", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-06-21", "due_date": "2025-07-21", "notes": "", "branch": "2", "created_at": "2025-06-21T14:32:20.748951", "updated_at": "2025-06-25T15:42:15.990422", "tenant_id": 2, "created_by": 5, "payments": [{"amount": 944.0, "payment_method": "Cash", "payment_date": "2025-06-25T15:42:15.990422", "reference": "", "notes": "", "collected_by": 5}]}, {"id": 35, "invoice_number": "INV00035", "sid_number": "SKZ015", "patient_id": 37, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1750496601733, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}], "bill_amount": 800, "other_charges": 0, "discount_percent": 0, "subtotal": 944, "discount": 0, "gst_rate": 18, "gst_amount": 144, "tax": 144, "total_amount": 944, "paid_amount": 944.0, "balance": 0.0, "payment_method": "Cash", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-06-21", "due_date": "2025-07-21", "notes": "", "branch": "2", "created_at": "2025-06-21T14:33:23.894705", "updated_at": "2025-06-25T15:41:45.508376", "tenant_id": 2, "created_by": 4, "payments": [{"amount": 944.0, "payment_method": "Cash", "payment_date": "2025-06-25T15:41:45.508376", "reference": "", "notes": "", "collected_by": 5}]}, {"id": 36, "invoice_number": "INV00036", "sid_number": "008", "patient_id": 28, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1750511358432, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}, {"testName": "Activated Protein C Resisitance", "test_id": 8, "amount": 3900, "id": 1750511363024, "test_name": "Activated Protein C Resisitance", "department": "IMMUNOLOGY", "hmsCode": "1429.0"}, {"testName": "ANDROSTENEDIONE (A4)", "test_id": 12, "amount": 900, "id": 1750511369104, "test_name": "ANDROSTENEDIONE (A4)", "department": "IMMUNOLOGY", "hmsCode": "512.0"}], "bill_amount": 5600, "other_charges": 0, "discount_percent": 0, "subtotal": 6608, "discount": 0, "gst_rate": 18, "gst_amount": 1008, "tax": 1008, "total_amount": 6608, "paid_amount": 6610, "balance": -2, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-06-21", "due_date": "2025-07-21", "notes": "", "branch": "1", "created_at": "2025-06-21T18:40:40.093362", "updated_at": "2025-06-21T18:40:40.093364", "tenant_id": 1, "created_by": 4}, {"id": 37, "invoice_number": "INV00037", "sid_number": "015", "patient_id": 51, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1751026392125, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}, {"testName": "<PERSON> Muller<PERSON> (AMH)", "test_id": 13, "amount": 1500, "id": 1751026396594, "test_name": "<PERSON> Muller<PERSON> (AMH)", "department": "IMMUNOLOGY", "hmsCode": "1305.0"}, {"testName": "IGF BP3", "test_id": 67, "amount": 2500, "id": 1751026400472, "test_name": "IGF BP3", "department": "IMMUNOLOGY", "hmsCode": "583.0"}, {"testName": "Estrone", "test_id": 45, "amount": 6500, "id": 1751*********, "test_name": "Estrone", "department": "IMMUNOLOGY", "hmsCode": "1499.0"}, {"testName": "IgE", "test_id": 66, "amount": 900, "id": 1751026417279, "test_name": "IgE", "department": "IMMUNOLOGY", "hmsCode": "637.0"}, {"testName": "BETA hCG", "test_id": 16, "amount": 600, "id": 1751026422735, "test_name": "BETA hCG", "department": "IMMUNOLOGY", "hmsCode": "522.0"}], "bill_amount": 12800, "other_charges": 0, "discount_percent": 0, "subtotal": 15104, "discount": 0, "gst_rate": 18, "gst_amount": 2304, "tax": 2304, "total_amount": 15104, "paid_amount": 16000, "balance": -896, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-06-27", "due_date": "2025-07-27", "notes": "", "branch": "1", "created_at": "2025-06-27T17:46:17.089903", "updated_at": "2025-06-27T17:46:17.089904", "tenant_id": 1, "created_by": 4}, {"id": 38, "invoice_number": "INV00038", "sid_number": "017", "patient_id": 51, "items": [{"testName": "Activated Protein C Resisitance", "test_id": 8, "amount": 3900, "id": 1751178484334, "test_name": "Activated Protein C Resisitance", "department": "IMMUNOLOGY", "hmsCode": "1429.0"}, {"testName": "ANDROSTENEDIONE (A4)", "test_id": 12, "amount": 900, "id": 1751178499717, "test_name": "ANDROSTENEDIONE (A4)", "department": "IMMUNOLOGY", "hmsCode": "512.0"}, {"testName": "ALPHA 1. ANTITRIPSIN", "test_id": 10, "amount": 1500, "id": 1751178504823, "test_name": "ALPHA 1. ANTITRIPSIN", "department": "IMMUNOLOGY", "hmsCode": "508.0"}], "bill_amount": 6300, "other_charges": 0, "discount_percent": 0, "subtotal": 7434, "discount": 0, "gst_rate": 18, "gst_amount": 1134, "tax": 1134, "total_amount": 7434, "paid_amount": 8000, "balance": -566, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-06-29", "due_date": "2025-07-29", "notes": "", "branch": "1", "created_at": "2025-06-29T11:59:27.311909", "updated_at": "2025-06-29T11:59:27.311910", "tenant_id": 1, "created_by": 4}, {"id": 39, "invoice_number": "INV00039", "sid_number": "020", "patient_id": 51, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1751783883632, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}, {"testName": "ACTH (Adreno Corticotropic Hormone)", "test_id": 7, "amount": 1200, "id": 1751783889978, "test_name": "ACTH (Adreno Corticotropic Hormone)", "department": "IMMUNOLOGY", "hmsCode": "17.0"}, {"testName": "ALPHA 1. ANTITRIPSIN", "test_id": 10, "amount": 1500, "id": 1751783895303, "test_name": "ALPHA 1. ANTITRIPSIN", "department": "IMMUNOLOGY", "hmsCode": "508.0"}, {"testName": "CA 72.4 ( TAG-72)", "test_id": 23, "amount": 1700, "id": 1751783902564, "test_name": "CA 72.4 ( TAG-72)", "department": "IMMUNOLOGY", "hmsCode": "665.0"}, {"testName": "<PERSON><PERSON><PERSON><PERSON>, Direct", "test_id": 237, "amount": 150, "id": 1751783920060, "test_name": "<PERSON><PERSON><PERSON><PERSON>, Direct", "department": "Biochemistry", "hmsCode": "217"}], "bill_amount": 5350, "other_charges": 0, "discount_percent": 0, "subtotal": 6313, "discount": 0, "gst_rate": 18, "gst_amount": 963, "tax": 963, "total_amount": 6313, "paid_amount": 4999.92, "balance": 1313.08, "payment_method": "", "payment_status": "Partial", "status": "Pending", "invoice_date": "2025-07-06", "due_date": "2025-08-05", "notes": "", "branch": "1", "created_at": "2025-07-06T12:14:39.004091", "updated_at": "2025-07-06T12:14:39.004096", "tenant_id": 1, "created_by": 4}, {"id": 40, "invoice_number": "INV00040", "sid_number": "025", "patient_id": 51, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1751872909476, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}, {"testName": "ANDROSTENEDIONE (A4)", "test_id": 12, "amount": 900, "id": 1751872913886, "test_name": "ANDROSTENEDIONE (A4)", "department": "IMMUNOLOGY", "hmsCode": "512.0"}, {"testName": "Alpha Fetoprotein (AFP)", "test_id": 11, "amount": 950, "id": 1751872918285, "test_name": "Alpha Fetoprotein (AFP)", "department": "IMMUNOLOGY", "hmsCode": "505.0"}], "bill_amount": 2650, "other_charges": 10, "discount_percent": 5, "subtotal": 2658.03, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 2658.03, "paid_amount": 2699.96, "balance": -41.929999999999836, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-07", "due_date": "2025-08-06", "notes": "", "branch": "1", "created_at": "2025-07-07T12:53:00.903498", "updated_at": "2025-07-07T12:53:00.903500", "tenant_id": 1, "created_by": 4}, {"id": 41, "invoice_number": "INV00041", "sid_number": "040", "patient_id": 1, "items": [{"test_id": 1, "test_name": "Blood Test", "quantity": 1, "price": 100, "amount": 100}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 100, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 100, "paid_amount": 0, "balance": 100, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-07-08", "due_date": "2025-08-07", "notes": "", "branch": 1, "created_at": "2025-07-08T17:22:11.439341", "updated_at": "2025-07-08T17:22:11.439342", "tenant_id": 1, "created_by": 1}, {"id": 42, "invoice_number": "INV00042", "sid_number": "049", "patient_id": 1, "items": [{"test_id": 1, "test_name": "Blood Test", "quantity": 1, "price": 100, "amount": 100}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 100, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 100, "paid_amount": 0, "balance": 100, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-07-08", "due_date": "2025-08-07", "notes": "", "branch": 1, "created_at": "2025-07-08T17:33:16.720431", "updated_at": "2025-07-08T17:33:16.720433", "tenant_id": 1, "created_by": 1}, {"id": 43, "invoice_number": "INV00043", "sid_number": "051", "patient_id": 1, "items": [{"test_id": 1, "test_name": "Blood Test", "quantity": 1, "price": 100, "amount": 100}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 100, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 100, "paid_amount": 0, "balance": 100, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-07-08", "due_date": "2025-08-07", "notes": "", "branch": 1, "created_at": "2025-07-08T17:37:39.459516", "updated_at": "2025-07-08T17:37:39.459517", "tenant_id": 1, "created_by": 1}, {"id": 44, "invoice_number": "INV00044", "sid_number": "052", "patient_id": 1, "items": [{"test_id": 1, "test_name": "Blood Test", "quantity": 1, "price": 100, "amount": 100}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 100, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 100, "paid_amount": 0, "balance": 100, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-07-08", "due_date": "2025-08-07", "notes": "", "branch": 1, "created_at": "2025-07-08T17:38:54.820842", "updated_at": "2025-07-08T17:38:54.820844", "tenant_id": 1, "created_by": 1}, {"id": 45, "invoice_number": "INV00045", "sid_number": "053", "patient_id": 1, "items": [{"test_id": 1, "test_name": "Blood Test", "quantity": 1, "price": 100, "amount": 100}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 100, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 100, "paid_amount": 0, "balance": 100, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-07-08", "due_date": "2025-08-07", "notes": "", "branch": 1, "created_at": "2025-07-08T17:39:55.610404", "updated_at": "2025-07-08T17:39:55.610405", "tenant_id": 1, "created_by": 1}, {"id": 46, "invoice_number": "INV00046", "sid_number": "002", "patient_id": 1, "items": [{"test_id": 2, "test_name": "X-Ray", "amount": 200.0, "quantity": 1}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 200.0, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 200.0, "paid_amount": 200.0, "balance": 0.0, "payment_method": "Card", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-09", "due_date": "2025-08-08", "notes": "", "branch": 2, "created_at": "2025-07-09T16:25:23.898125", "updated_at": "2025-07-09T16:25:23.898127", "tenant_id": 2, "created_by": 5}, {"id": 47, "invoice_number": "INV00047", "sid_number": "001", "patient_id": 53, "items": [{"test_id": 1, "test_name": "Blood Test", "amount": 100.0, "quantity": 1}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 100.0, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 100.0, "paid_amount": 100.0, "balance": 0.0, "payment_method": "Cash", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-09", "due_date": "2025-08-08", "notes": "", "branch": 2, "created_at": "2025-07-09T16:25:58.970022", "updated_at": "2025-07-09T16:25:58.970024", "tenant_id": 2, "created_by": 5}, {"id": 48, "invoice_number": "INV00048", "sid_number": "999", "patient_id": 1, "items": [{"test_id": 2, "test_name": "X-Ray", "amount": 200.0, "quantity": 1}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 200.0, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 200.0, "paid_amount": 200.0, "balance": 0.0, "payment_method": "Card", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-09", "due_date": "2025-08-08", "notes": "", "branch": 2, "created_at": "2025-07-09T16:26:33.102451", "updated_at": "2025-07-09T16:26:33.102452", "tenant_id": 2, "created_by": 5}, {"id": 49, "invoice_number": "INV00049", "sid_number": "1000", "patient_id": 55, "items": [{"test_id": 1, "test_name": "Blood Test", "amount": 100.0, "quantity": 1}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 100.0, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 100.0, "paid_amount": 100.0, "balance": 0.0, "payment_method": "Cash", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-09", "due_date": "2025-08-08", "notes": "", "branch": 2, "created_at": "2025-07-09T16:27:14.482406", "updated_at": "2025-07-09T16:27:14.482408", "tenant_id": 2, "created_by": 5}, {"id": 50, "invoice_number": "INV00050", "sid_number": "1001", "patient_id": 1, "items": [{"test_id": 2, "test_name": "X-Ray", "amount": 200.0, "quantity": 1}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 200.0, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 200.0, "paid_amount": 200.0, "balance": 0.0, "payment_method": "Card", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-09", "due_date": "2025-08-08", "notes": "", "branch": 2, "created_at": "2025-07-09T16:27:16.758783", "updated_at": "2025-07-09T16:27:16.758785", "tenant_id": 2, "created_by": 5}, {"id": 51, "invoice_number": "INV00051", "sid_number": "116", "patient_id": 28, "items": [{"testName": "1,25 Dihydroxyvitamin D", "test_id": 1, "amount": 3500, "id": 1752058998392, "test_name": "1,25 Dihydroxyvitamin D", "department": "IMMUNOLOGY", "hmsCode": "648.0"}, {"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1752059003401, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}], "bill_amount": 4300, "other_charges": 0, "discount_percent": 0, "subtotal": 5074, "discount": 0, "gst_rate": 18, "gst_amount": 774, "tax": 774, "total_amount": 5074, "paid_amount": 5999.96, "balance": -925.96, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-09", "due_date": "2025-08-08", "notes": "", "branch": "1", "created_at": "2025-07-09T16:34:04.566473", "updated_at": "2025-07-09T16:34:04.566474", "tenant_id": 1, "created_by": 4}, {"id": 52, "invoice_number": "INV00052", "sid_number": "132", "patient_id": 51, "items": [{"testName": "Glucose, 30 min", "test_id": 295, "amount": 20, "id": 1752060277499, "test_name": "Glucose, 30 min", "department": "Biochemistry", "hmsCode": "235"}, {"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1752060282067, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}, {"testName": "INSULIN LIKE GROWTH  FACTOR (IGF-1)", "test_id": 79, "amount": 0, "id": 1752060286835, "test_name": "INSULIN LIKE GROWTH  FACTOR (IGF-1)", "department": "IMMUNOLOGY", "hmsCode": "582.0"}], "bill_amount": 820, "other_charges": 0, "discount_percent": 0, "subtotal": 967.6, "discount": 0, "gst_rate": 18, "gst_amount": 147.6, "tax": 147.6, "total_amount": 967.6, "paid_amount": 999.98, "balance": -32.379999999999995, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-09", "due_date": "2025-08-08", "notes": "", "branch": 1, "created_at": "2025-07-09T16:55:24.627300", "updated_at": "2025-07-09T16:55:24.627302", "tenant_id": 1, "created_by": 4}, {"id": 53, "invoice_number": "INV00053", "sid_number": "149", "patient_id": 51, "items": [{"testName": "Glucose, Ascitic Fluid", "test_id": 298, "amount": 100, "id": 1752063511175, "test_name": "Glucose, Ascitic Fluid", "department": "Biochemistry", "hmsCode": "152"}, {"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1752063515720, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}], "bill_amount": 900, "other_charges": 9, "discount_percent": 0, "subtotal": 1072.62, "discount": 0, "gst_rate": 18, "gst_amount": 163.62, "tax": 163.62, "total_amount": 1072.62, "paid_amount": 1100, "balance": -27.38000000000011, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-09", "due_date": "2025-08-08", "notes": "", "branch": 1, "created_at": "2025-07-09T17:49:27.440115", "updated_at": "2025-07-09T17:49:27.440116", "tenant_id": 1, "created_by": 4}, {"id": 55, "invoice_number": "INV00055", "sid_number": "208", "patient_id": 51, "items": [{"id": 1752572629249, "test_id": 76, "test_master_id": 76, "testName": "SICKLING TEST", "test_name": "SICKLING TEST", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000402", "display_name": "SICKLING TEST", "short_name": "SICK", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 76, "testName": "SICKLING TEST", "test_profile": "SICKLING TEST", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000402", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.647734", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 100, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "SICK", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000402", "test_done_on": "all", "test_name": "SICKLING TEST", "updated_at": "2025-07-09T12:20:55.647737"}}, {"id": 1752572633900, "test_id": 32, "test_master_id": 32, "testName": "Immature Platelet Fraction (IPF)", "test_name": "Immature Platelet Fraction (IPF)", "amount": 1000, "price": 1000, "test_price": 1000, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "001537", "display_name": "Immature Platelet Fraction (IPF)", "short_name": "IPF", "international_code": "", "method": "Fluorescent Flow Cytometry", "primary_specimen": "EDTA BLOOD", "specimen": "EDTA BLOOD", "container": "EDTA Container", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 5, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 32, "testName": "Immature Platelet Fraction (IPF)", "test_profile": "Immature Platelet Fraction (IPF)", "test_price": 1000, "department": "HAEMATOLOGY", "hmsCode": "001537", "specimen": "EDTA BLOOD", "container": "EDTA Container", "serviceTime": "", "reportingDays": 5, "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "Fluorescent Flow Cytometry", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 7, "created_at": "2025-07-09T12:20:55.642586", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 157, "min_sample_qty": null, "price": 1000, "reference_range": null, "reporting_days": 5, "result_type": "Pick List", "result_unit": null, "short_name": "IPF", "source_sheet": "HAEMATOLOGY", "specimen_code": 17, "test_code": "001537", "test_done_on": "all", "test_name": "Immature Platelet Fraction (IPF)", "updated_at": "2025-07-09T12:20:55.642589"}}], "bill_amount": 1100, "other_charges": 1100, "discount_percent": 0, "subtotal": 2596, "discount": 0, "gst_rate": 18, "gst_amount": 396, "tax": 396, "total_amount": 2596, "paid_amount": 3000, "balance": -404, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-15", "due_date": "2025-08-14", "notes": "", "branch": 1, "created_at": "2025-07-15T15:14:41.074226", "updated_at": "2025-07-15T15:14:41.074229", "tenant_id": 1, "created_by": 4}, {"id": 57, "invoice_number": "INV00057", "sid_number": "217", "patient_id": 59, "items": [{"id": 1752643063039, "test_id": 17, "test_master_id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "amount": 3600, "price": 3600, "test_price": 3600, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "001599", "display_name": "DCP- Decarboxy Prothrombin PIVKA II", "short_name": "DCP", "international_code": "", "method": "CMIA", "primary_specimen": "SERUM", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "reference_range": "17.36 - 50.90", "result_unit": "mAU/ml", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 10, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_profile": "DCP- Decarboxy Prothrombin PIVKA II", "test_price": 3600, "department": "HAEMATOLOGY", "hmsCode": "001599", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "serviceTime": "", "reportingDays": 10, "cutoffTime": "", "referenceRange": "17.36 - 50.90", "resultUnit": "mAU/ml", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "CMIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 12, "created_at": "2025-07-09T12:20:55.640014", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 29, "min_sample_qty": null, "price": 3600, "reference_range": "17.36 - 50.90", "reporting_days": 10, "result_type": "-", "result_unit": "mAU/ml", "short_name": "DCP", "source_sheet": "HAEMATOLOGY", "specimen_code": 39, "test_code": "001599", "test_done_on": "all", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "updated_at": "2025-07-09T12:20:55.640017"}}, {"id": 1752643068450, "test_id": 12, "test_master_id": 12, "testName": "Chromosome Analysis - Product of Conception", "test_name": "Chromosome Analysis - Product of Conception", "amount": 6100, "price": 6100, "test_price": 6100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000922", "display_name": "Chromosome Analysis - Product of Conception", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 12, "testName": "Chromosome Analysis - Product of Conception", "test_profile": "Chromosome Analysis - Product of Conception", "test_price": 6100, "department": "HAEMATOLOGY", "hmsCode": "000922", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639476", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 6100, "reference_range": null, "reporting_days": 0, "result_type": "Template", "result_unit": null, "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000922", "test_done_on": "all", "test_name": "Chromosome Analysis - Product of Conception", "updated_at": "2025-07-09T12:20:55.639479"}}], "bill_amount": 9700, "other_charges": null, "discount_percent": 0, "subtotal": 9700, "discount": 0, "gst_rate": 18, "gst_amount": 1746, "tax": 1746, "total_amount": 9700, "paid_amount": 9700, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-16", "due_date": "2025-08-15", "notes": "", "branch": 1, "created_at": "2025-07-16T10:51:45.327764", "updated_at": "2025-07-16T10:51:45.327766", "tenant_id": 1, "created_by": 4}, {"id": 58, "invoice_number": "INV00058", "sid_number": "220", "patient_id": 59, "items": [{"id": 1752643892795, "test_id": 76, "test_master_id": 76, "testName": "SICKLING TEST", "test_name": "SICKLING TEST", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000402", "display_name": "SICKLING TEST", "short_name": "SICK", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 76, "testName": "SICKLING TEST", "test_profile": "SICKLING TEST", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000402", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.647734", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 100, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "SICK", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000402", "test_done_on": "all", "test_name": "SICKLING TEST", "updated_at": "2025-07-09T12:20:55.647737"}}], "bill_amount": 100, "other_charges": 0, "discount_percent": 0, "subtotal": 100, "discount": 0, "gst_rate": 18, "gst_amount": 18, "tax": 18, "total_amount": 100, "paid_amount": 100, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-16", "due_date": "2025-08-15", "notes": "", "branch": 1, "created_at": "2025-07-16T11:02:04.702677", "updated_at": "2025-07-16T11:02:04.702679", "tenant_id": 1, "created_by": 4}, {"id": 59, "invoice_number": "INV00059", "sid_number": "222", "patient_id": 44, "items": [{"id": 1752674235940, "test_id": 407, "test_master_id": 407, "testName": "Alkaline phosphatase", "test_name": "Alkaline phosphatase", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "BIOCHEMISTRY", "hms_code": "000027", "display_name": "Alkaline phosphatase", "short_name": "ALP", "international_code": "", "method": "PNPP-DGKC", "primary_specimen": "Serum", "specimen": "Serum", "container": "PLAIN", "reference_range": "Children : 47 - 406 (Age and gender dependent)  Adults   : 80 - 306", "result_unit": "U/L", "decimals": 1, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 407, "testName": "Alkaline phosphatase", "test_profile": "Alkaline phosphatase", "test_price": 100, "department": "BIOCHEMISTRY", "hmsCode": "000027", "specimen": "Serum", "container": "PLAIN", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Children : 47 - 406 (Age and gender dependent)  Adults   : 80 - 306", "resultUnit": "U/L", "decimals": 1, "criticalLow": null, "criticalHigh": null, "method": "PNPP-DGKC", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 15, "created_at": "2025-07-09T12:20:56.531537", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 38, "min_sample_qty": null, "price": 100, "reference_range": "Children : 47 - 406 (Age and gender dependent)  Adults   : 80 - 306", "reporting_days": 0, "result_type": "-", "result_unit": "U/L", "short_name": "ALP", "source_sheet": "BioChemistry", "specimen_code": 64, "test_code": "000027", "test_done_on": "all", "test_name": "Alkaline phosphatase", "updated_at": "2025-07-09T12:20:56.531540"}}, {"id": 1752674247500, "test_id": 119, "test_master_id": 119, "testName": "CHIKUNGUNYA , Qualitative PCR", "test_name": "CHIKUNGUNYA , Qualitative PCR", "amount": 3000, "price": 3000, "test_price": 3000, "quantity": 1, "department": "MOLECULAR_BIOLOGY", "hms_code": "001098", "display_name": "CHIKUNGUNYA , Qualitative PCR", "short_name": "CHIP", "international_code": "", "method": "PCR", "primary_specimen": "EDTA BLOOD", "specimen": "EDTA BLOOD", "container": "EDTA Container", "reference_range": "NOT DETECTED.", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 119, "testName": "CHIKUNGUNYA , Qualitative PCR", "test_profile": "CHIKUNGUNYA , Qualitative PCR", "test_price": 3000, "department": "MOLECULAR_BIOLOGY", "hmsCode": "001098", "specimen": "EDTA BLOOD", "container": "EDTA Container", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "NOT DETECTED.", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "PCR", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 7, "created_at": "2025-07-09T12:20:55.833314", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 117, "min_sample_qty": null, "price": 3000, "reference_range": "NOT DETECTED.", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "CHIP", "source_sheet": "Molecular Biology", "specimen_code": 17, "test_code": "001098", "test_done_on": "all", "test_name": "CHIKUNGUNYA , Qualitative PCR", "updated_at": "2025-07-09T12:20:55.833332"}}], "bill_amount": 3100, "other_charges": 0, "discount_percent": 0, "subtotal": 3100, "discount": 0, "gst_rate": 18, "gst_amount": 558, "tax": 558, "total_amount": 3100, "paid_amount": 3100, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-16", "due_date": "2025-08-15", "notes": "", "branch": 1, "created_at": "2025-07-16T19:28:03.623684", "updated_at": "2025-07-16T19:28:03.623685", "tenant_id": 1, "created_by": 4}, {"id": 60, "invoice_number": "INV00060", "sid_number": "234", "patient_id": 61, "items": [{"id": 1752759347094, "test_id": 48, "test_master_id": 48, "testName": "MYOGLOBIN-SERUM", "test_name": "MYOGLOBIN-SERUM", "amount": 1200, "price": 1200, "test_price": 1200, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000426", "display_name": "MYOGLOBIN-SERUM", "short_name": "MYOS", "international_code": "", "method": "ECLIA", "primary_specimen": "Serum", "specimen": "Serum", "container": "", "reference_range": "25-58", "result_unit": "ug/L", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 48, "testName": "MYOGLOBIN-SERUM", "test_profile": "MYOGLOBIN-SERUM", "test_price": 1200, "department": "HAEMATOLOGY", "hmsCode": "000426", "specimen": "Serum", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "25-58", "resultUnit": "ug/L", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "ECLIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.644672", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 52, "min_sample_qty": null, "price": 1200, "reference_range": "25-58", "reporting_days": 0, "result_type": "Numeric", "result_unit": "ug/L", "short_name": "MYOS", "source_sheet": "HAEMATOLOGY", "specimen_code": 64, "test_code": "000426", "test_done_on": "all", "test_name": "MYOGLOBIN-SERUM", "updated_at": "2025-07-09T12:20:55.644675"}}, {"id": 1752759351845, "test_id": 17, "test_master_id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "amount": 3600, "price": 3600, "test_price": 3600, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "001599", "display_name": "DCP- Decarboxy Prothrombin PIVKA II", "short_name": "DCP", "international_code": "", "method": "CMIA", "primary_specimen": "SERUM", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "reference_range": "17.36 - 50.90", "result_unit": "mAU/ml", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 10, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_profile": "DCP- Decarboxy Prothrombin PIVKA II", "test_price": 3600, "department": "HAEMATOLOGY", "hmsCode": "001599", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "serviceTime": "", "reportingDays": 10, "cutoffTime": "", "referenceRange": "17.36 - 50.90", "resultUnit": "mAU/ml", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "CMIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 12, "created_at": "2025-07-09T12:20:55.640014", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 29, "min_sample_qty": null, "price": 3600, "reference_range": "17.36 - 50.90", "reporting_days": 10, "result_type": "-", "result_unit": "mAU/ml", "short_name": "DCP", "source_sheet": "HAEMATOLOGY", "specimen_code": 39, "test_code": "001599", "test_done_on": "all", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "updated_at": "2025-07-09T12:20:55.640017"}}], "bill_amount": 4800, "other_charges": 0, "discount_percent": 0, "subtotal": 4800, "discount": 0, "gst_rate": 18, "gst_amount": 864, "tax": 864, "total_amount": 4800, "paid_amount": 5000, "balance": -200, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-17", "due_date": "2025-08-16", "notes": "", "branch": 1, "created_at": "2025-07-17T19:06:30.208055", "updated_at": "2025-07-17T19:06:30.208057", "tenant_id": 1, "created_by": 4}, {"id": 61, "invoice_number": "INV00061", "sid_number": "248", "patient_id": 51, "items": [{"id": 1752760666606, "test_id": 17, "test_master_id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "amount": 3600, "price": 3600, "test_price": 3600, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "001599", "display_name": "DCP- Decarboxy Prothrombin PIVKA II", "short_name": "DCP", "international_code": "", "method": "CMIA", "primary_specimen": "SERUM", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "reference_range": "17.36 - 50.90", "result_unit": "mAU/ml", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 10, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_profile": "DCP- Decarboxy Prothrombin PIVKA II", "test_price": 3600, "department": "HAEMATOLOGY", "hmsCode": "001599", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "serviceTime": "", "reportingDays": 10, "cutoffTime": "", "referenceRange": "17.36 - 50.90", "resultUnit": "mAU/ml", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "CMIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 12, "created_at": "2025-07-09T12:20:55.640014", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 29, "min_sample_qty": null, "price": 3600, "reference_range": "17.36 - 50.90", "reporting_days": 10, "result_type": "-", "result_unit": "mAU/ml", "short_name": "DCP", "source_sheet": "HAEMATOLOGY", "specimen_code": 39, "test_code": "001599", "test_done_on": "all", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "updated_at": "2025-07-09T12:20:55.640017"}}], "bill_amount": 3600, "other_charges": 0, "discount_percent": 0, "subtotal": 3600, "discount": 0, "gst_rate": 18, "gst_amount": 648, "tax": 648, "total_amount": 3600, "paid_amount": 4000, "balance": -400, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-17", "due_date": "2025-08-16", "notes": "", "branch": 1, "created_at": "2025-07-17T19:27:50.352678", "updated_at": "2025-07-17T19:27:50.352680", "tenant_id": 1, "created_by": 4}, {"id": 62, "invoice_number": "INV00062", "sid_number": "251", "patient_id": 62, "items": [{"id": 1752821803827, "test_id": 76, "test_master_id": 76, "testName": "SICKLING TEST", "test_name": "SICKLING TEST", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000402", "display_name": "SICKLING TEST", "short_name": "SICK", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 76, "testName": "SICKLING TEST", "test_profile": "SICKLING TEST", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000402", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.647734", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 100, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "SICK", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000402", "test_done_on": "all", "test_name": "SICKLING TEST", "updated_at": "2025-07-09T12:20:55.647737"}}, {"id": 1752821808910, "test_id": 85, "test_master_id": 85, "testName": "BENCE JONES PROTEIN-URINE", "test_name": "BENCE JONES PROTEIN-URINE", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "CLINICAL_PATHOLOGY", "hms_code": "000316", "display_name": "BENCE JONES PROTEIN-URINE", "short_name": "BJP", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "NEGATIVE", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 85, "testName": "BENCE JONES PROTEIN-URINE", "test_profile": "BENCE JONES PROTEIN-URINE", "test_price": 100, "department": "CLINICAL_PATHOLOGY", "hmsCode": "000316", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "NEGATIVE", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.740444", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 100, "reference_range": "NEGATIVE", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "BJP", "source_sheet": "Clinical Pathology", "specimen_code": null, "test_code": "000316", "test_done_on": "all", "test_name": "BENCE JONES PROTEIN-URINE", "updated_at": "2025-07-09T12:20:55.740447"}}], "bill_amount": 200, "other_charges": 0, "discount_percent": 0, "subtotal": 200, "discount": 0, "gst_rate": 18, "gst_amount": 36, "tax": 36, "total_amount": 200, "paid_amount": 200, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-18", "due_date": "2025-08-17", "notes": "", "branch": 1, "created_at": "2025-07-18T12:27:23.242000", "updated_at": "2025-07-18T12:27:23.242001", "tenant_id": 1, "created_by": 4}, {"id": 63, "invoice_number": "INV00063", "sid_number": "253", "patient_id": 51, "items": [{"id": 1752833748877, "test_id": 17, "test_master_id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "amount": 3600, "price": 3600, "test_price": 3600, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "001599", "display_name": "DCP- Decarboxy Prothrombin PIVKA II", "short_name": "DCP", "international_code": "", "method": "CMIA", "primary_specimen": "SERUM", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "reference_range": "17.36 - 50.90", "result_unit": "mAU/ml", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 10, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_profile": "DCP- Decarboxy Prothrombin PIVKA II", "test_price": 3600, "department": "HAEMATOLOGY", "hmsCode": "001599", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "serviceTime": "", "reportingDays": 10, "cutoffTime": "", "referenceRange": "17.36 - 50.90", "resultUnit": "mAU/ml", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "CMIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 12, "created_at": "2025-07-09T12:20:55.640014", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 29, "min_sample_qty": null, "price": 3600, "reference_range": "17.36 - 50.90", "reporting_days": 10, "result_type": "-", "result_unit": "mAU/ml", "short_name": "DCP", "source_sheet": "HAEMATOLOGY", "specimen_code": 39, "test_code": "001599", "test_done_on": "all", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "updated_at": "2025-07-09T12:20:55.640017"}}, {"id": 1752833755080, "test_id": 44, "test_master_id": 44, "testName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "test_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "amount": 1000, "price": 1000, "test_price": 1000, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000372", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "short_name": "Met<PERSON>-<PERSON><PERSON><PERSON>", "international_code": "", "method": "Spectrophotometry", "primary_specimen": "HEPARIN BLOOD", "specimen": "HEPARIN BLOOD", "container": "<PERSON><PERSON><PERSON>", "reference_range": "Less than 1.5", "result_unit": "%", "decimals": 1, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 44, "testName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "test_profile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "test_price": 1000, "department": "HAEMATOLOGY", "hmsCode": "000372", "specimen": "HEPARIN BLOOD", "container": "<PERSON><PERSON><PERSON>", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Less than 1.5", "resultUnit": "%", "decimals": 1, "criticalLow": null, "criticalHigh": null, "method": "Spectrophotometry", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 9, "created_at": "2025-07-09T12:20:55.644222", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 131, "min_sample_qty": null, "price": 1000, "reference_range": "Less than 1.5", "reporting_days": 0, "result_type": "Numeric", "result_unit": "%", "short_name": "Met<PERSON>-<PERSON><PERSON><PERSON>", "source_sheet": "HAEMATOLOGY", "specimen_code": 25, "test_code": "000372", "test_done_on": "all", "test_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "updated_at": "2025-07-09T12:20:55.644225"}}], "bill_amount": 4600, "other_charges": 0, "discount_percent": 0, "subtotal": 4600, "discount": 0, "gst_rate": 18, "gst_amount": 828, "tax": 828, "total_amount": 4600, "paid_amount": 5000, "balance": -400, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-18", "due_date": "2025-08-17", "notes": "", "branch": 1, "created_at": "2025-07-18T15:46:23.662750", "updated_at": "2025-07-18T15:46:23.662752", "tenant_id": 1, "created_by": 4}]