[{"Test Name": "Abs.Basophils in #.", "code": 1687, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Abs.Eosinophils in #.", "code": 1685, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Abs.Lymphocyte in #.", "code": 1684, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Abs.Monocyte in #.", "code": 1686, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Abs.Neutrophils in #.", "code": 1683, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ABSOLUTE EOSINOPHIL COUNT", "code": 330, "Department": "HAEMATOLOGY", "Price": 100, "Result Type": "Pick List", "Short Name": "AEC", "Method code": 9, "Method": "Automated", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ABSOLUTE LYMPHOCYTE COUNT", "code": 331, "Department": "HAEMATOLOGY", "Referance Range": "1000 - 3000", "Result Unit": "cells/cumm", "Price": 100, "Result Type": "-", "Short Name": "ALC", "Method code": 9, "Method": "Automated", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ABSOLUTE NEUTROPHIL COUNT", "code": 332, "Department": "HAEMATOLOGY", "Referance Range": "2000 - 7000", "Result Unit": "cells/cumm", "Price": 100, "Result Type": "-", "Short Name": "ANC", "Method code": 10, "Method": "Automated                        ", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Anti Platelet Antibody", "code": 333, "Department": "HAEMATOLOGY", "Price": 7500, "Result Type": "Pick List", "Short Name": "Anti Plate", "Method code": 83, "Method": "IF", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 5, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "BLEEDING TIME", "code": 342, "Department": "HAEMATOLOGY", "Referance Range": "1 to 6", "Result Unit": "minutes", "Price": 20, "Result Type": "Pick List", "Short Name": "BT", "Method code": 100, "Method": "IVY", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "BLOOD GROUP&RH-GEL METHOD", "code": 428, "Department": "HAEMATOLOGY", "Price": 20, "Result Type": "Pick List", "Method code": 75, "Method": "GEL METHOD", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Chromosome Analysis - Product of Conception", "code": 922, "Department": "HAEMATOLOGY", "Price": 6100, "Result Type": "Template", "Min. Process Time": 30, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CLOTTING TIME", "code": 347, "Department": "HAEMATOLOGY", "Referance Range": "5 - 10", "Result Unit": "minutes", "Price": 20, "Result Type": "Pick List", "Method code": 106, "Method": "<PERSON> and <PERSON> method", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "COLD AGGLUTININ", "code": 348, "Department": "HAEMATOLOGY", "Notes": "Titers of 1:32 or higher are considered elevated by this technique.Elevated titers are seen in primary atypical pneumonia and in certain hemolytic anemias.Primary atypical pneumonia can be caused by Mycoplasma pneumoniae, influenza A, influenza B,parainfluenza, and adenoviruses.However, a fourfold rise in the cold agglutinins usually begins to appear late in the first week or during the second week of the disease and begins to decrease between the fourth and sixth weeksLow titers of cold agglutinins have been demonstrated in malaria, peripheral vascular disease, and common respiratory disease.", "Referance Range": "NEGATIVE UPTO 1:32", "Price": 600, "Result Type": "Pick List", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Calcium, Urine 24Hr", "code": 40, "Department": "HAEMATOLOGY", "Result Type": "-"}, {"Test Name": "17 HYDROXY CORTICO STEROID 24 HR URINE", "code": 3, "Department": "HAEMATOLOGY", "Referance Range": "< 1 Years     :  <=  1.0 (Both) 1 - 10 Years  :  3 - 6 (Both) > 10 Years    :  3 - 10 (Male)", "Result Unit": "mg/24hrs", "Short Name": "17HY", "Method code": 45, "Method": "Column Chromatography", "Specimen Code": 3, "Specimen": "24 H<PERSON> <PERSON><PERSON>", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Instructions": "10 ml of 50% HCL as a preservative, Total volume to be mentioned", "Cut-off Time": 0.5416666666666666, "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 10}, {"Test Name": "DCP- Decarboxy Prothrombin PIVKA II", "code": 1599, "Department": "HAEMATOLOGY", "Referance Range": "17.36 - 50.90", "Result Unit": "mAU/ml", "No of decimals": 2, "Price": 3600, "Result Type": "-", "Short Name": "DCP", "Method code": 29, "Method": "CMIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 10}, {"Test Name": "DIFFERENTIAL COUNT-5 Part", "code": 350, "Department": "HAEMATOLOGY", "Price": 100, "Result Type": "No Unit / Ref. Value", "Short Name": "DC", "Method code": 9, "Method": "Automated", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Differential Count", "code": 1638, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "Method code": 160, "Method": "Electrical Impedance", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "DIRECT COOMBS TEST", "code": 351, "Department": "HAEMATOLOGY", "Referance Range": "Negative", "Price": 350, "Result Type": "Pick List", "Short Name": "DCT", "Method code": 44, "Method": "Column Agglutination", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ESR", "code": 353, "Department": "HAEMATOLOGY", "Price": 80, "Result Type": "-", "Short Name": "ESR", "Method code": 143, "Method": "<PERSON><PERSON><PERSON><PERSON> Method", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Factor X Functional", "code": 1348, "Department": "HAEMATOLOGY", "Referance Range": "70.00 - 120.00", "Result Unit": "%", "Price": 5000, "Result Type": "Pick List", "Short Name": "FAX", "Method code": 30, "Method": "COAGULATION", "Specimen Code": 14, "Specimen": "Citrate Plasma", "Container Code": 8, "Container": "Citrate Container", "Min. Process Time": 12, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "FDP", "code": 76, "Department": "HAEMATOLOGY", "Referance Range": "Negative", "Price": 1000, "Result Type": "Pick List", "Short Name": "FDP", "Method code": 7, "Method": "Agglutination", "Specimen Code": 14, "Specimen": "Citrate Plasma", "Container Code": 8, "Container": "Citrate Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "FIBRINOGEN", "code": 327, "Department": "HAEMATOLOGY", "Referance Range": "200 - 400", "Result Unit": "mg/dL", "Price": 700, "Result Type": "Pick List", "Short Name": "FIB", "Method code": 30, "Method": "COAGULATION", "Specimen Code": 14, "Specimen": "Citrate Plasma", "Container Code": 8, "Container": "Citrate Container", "Min. Process Time": 24, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "FILARIAL ANTIBODY", "code": 1276, "Department": "HAEMATOLOGY", "Price": 1500, "Result Type": "Pick List", "Short Name": "MFAB", "Method code": 90, "Method": "Immuno Chromatography", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "FILARIAL ANTIGEN", "code": 375, "Department": "HAEMATOLOGY", "Referance Range": "NEGATIVE  The Test is structured to indicate the presence or absence of W.ban<PERSON> antigen in the sample.  The absence of antigen does not exclude Filariasis caused by other nematode species.", "Price": 700, "Result Type": "Pick List", "Short Name": "MFAG", "Method code": 80, "Method": "IC", "Primary specimen code": 17, "Primary specimen ": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 24, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "GIEMSA STAINING", "code": 423, "Department": "HAEMATOLOGY", "Price": 100, "Result Type": "No Unit / Ref. Value", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Haemoglobin", "code": 361, "Department": "HAEMATOLOGY", "Referance Range": "12.0 - 16.0\n14.0 - 18.0", "Result Unit": "g/dL", "No of decimals": 1, "Critical Low": 7, "   Critical High": 19, "Price": 100, "Result Type": "-", "Short Name": "HB", "Method code": 12, "Method": "Non-Cyanide Haemoglobin Analysis", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Haemoglobin, Urine", "code": 280, "Department": "HAEMATOLOGY", "Price": 300, "Result Type": "Pick List", "Short Name": 15, "Method code": "Biochemical", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HCT", "code": 1639, "Department": "HAEMATOLOGY", "Referance Range": "37.000 - 54.000", "Result Unit": "%", "No of decimals": 2, "Price": 0, "Primary specimen code": 17, "Primary specimen ": "EDTA BLOOD", "Specimen Code": 9, "Specimen": "BLOOD", "Container Code": 7, "Container": "EDTA Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HCT (P.C.V).", "code": 1689, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "Method code": 160, "Method": "Electrical Impedance", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Immature Platelet Fraction (IPF)", "code": 1537, "Department": "HAEMATOLOGY", "Price": 1000, "Result Type": "Pick List", "Short Name": "IPF", "Method code": 157, "Method": "Fluorescent Flow Cytometry", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 5}, {"Test Name": "INDIRECT COOMBS TEST", "code": 364, "Department": "HAEMATOLOGY", "Referance Range": "Negative", "Price": 350, "Result Type": "Pick List\t", "Short Name": "ICT", "Method code": 44, "Method": "Column Agglutination", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Large Immature Cells#.", "code": 1682, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Large Immature Cells.", "code": 1681, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Lupus Anticoagulant (dRVVT)", "code": 326, "Department": "HAEMATOLOGY", "Price": 1500, "Result Type": "Pick List", "Short Name": "LAC", "Method code": 30, "Method": "COAGULATION", "Specimen Code": 14, "Specimen": "Citrate Plasma", "Container Code": 8, "Container": "Citrate Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MALARIAL  PARASITE (CARD)", "code": 424, "Department": "HAEMATOLOGY", "Referance Range": "NEGATIVE", "Price": 300, "Result Type": "Pick List", "Short Name": "MP <PERSON>", "Method code": 82, "Method": "ICT", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Min. Process Time": 6, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MALARIAL  PARASITE (SLIDE)", "code": 366, "Department": "HAEMATOLOGY", "Price": 300, "Result Type": "Pick List", "Short Name": "MP", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MALARIAL ANTIGEN (Vivax & Falciparum)", "code": 430, "Department": "HAEMATOLOGY", "Referance Range": "NEGATIVE", "Price": 600, "Result Type": "Pick List", "Method code": 80, "Method": "IC", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Malarial Parasite (QBC)", "code": 427, "Department": "HAEMATOLOGY", "Referance Range": "< 1    Parasite   : + 1-10   Parasites  : ++ 11-100 Parasites  : +++ > 100  Parasites  : +++", "Result Unit": "per QBC Field", "Price": 400, "Result Type": "Pick List", "Method code": 122, "Method": "QBC", "Min. Process Time": 1, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MCH", "code": 369, "Department": "HAEMATOLOGY", "Result Unit": "pg", "No of decimals": 1, "Price": 100, "Result Type": "Numeric", "Short Name": "MCH", "Method code": 20, "Method": "Calculated", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MCHC", "code": 370, "Department": "HAEMATOLOGY", "Result Unit": "g/L", "No of decimals": 1, "Price": 100, "Result Type": "Numeric", "Short Name": "MCHC", "Method code": 20, "Method": "Calculated", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MCV", "code": 371, "Department": "HAEMATOLOGY", "Result Unit": "fl", "No of decimals": 1, "Price": 100, "Result Type": "Numeric", "Short Name": "MCV", "Method code": 20, "Method": "Calculated", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "code": 372, "Department": "HAEMATOLOGY", "Referance Range": "Less than 1.5", "Result Unit": "%", "No of decimals": 1, "Price": 1000, "Result Type": "Numeric", "Short Name": "Met<PERSON>-<PERSON><PERSON><PERSON>", "Method code": 131, "Method": "Spectrophotometry", "Specimen Code": 25, "Specimen": "HEPARIN BLOOD", "Container Code": 9, "Container": "<PERSON><PERSON><PERSON>", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MICROFILARIA (MF)", "code": 374, "Department": "HAEMATOLOGY", "Referance Range": "Negative", "Price": 300, "Result Type": "Pick List", "Short Name": "MF", "Method code": 110, "Method": "Microscopic", "Specimen Code": 61, "Specimen": "WHOLE BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MICROFILARIA (MF)  by QBC", "code": 431, "Department": "HAEMATOLOGY", "Referance Range": "( QBC )", "Price": 400, "Result Type": "Pick List", "Short Name": "QBC", "Method code": 122, "Method": "QBC", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Min. Process Time": 1, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MPV", "code": 1640, "Department": "HAEMATOLOGY", "Result Unit": "fl", "Price": 0, "Result Type": "Numeric", "Primary specimen code": 17, "Primary specimen ": "EDTA BLOOD", "Specimen Code": 9, "Specimen": "BLOOD", "Container Code": 7, "Container": "EDTA Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MYOGLOBIN-SERUM", "code": 426, "Department": "HAEMATOLOGY", "Referance Range": "25-58", "Result Unit": "ug/L", "No of decimals": 2, "Price": 1200, "Result Type": "Numeric", "Short Name": "MYOS", "Method code": 52, "Method": "ECLIA", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 3, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "NASAL SMEAR FOR EOSINOPHILS", "code": 425, "Department": "HAEMATOLOGY", "Price": 100, "Result Type": "Pick List", "Specimen Code": 28, "Specimen": "NASAL SMEAR", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "P-LCR", "code": 1643, "Department": "HAEMATOLOGY", "Result Unit": "%", "Price": 0, "Result Type": "Numeric", "Primary specimen code": 17, "Primary specimen ": "EDTA BLOOD", "Specimen Code": 9, "Specimen": "BLOOD", "Container Code": 7, "Container": "EDTA Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PARTIAL THROMBOPLASTIN TIME (APTT)", "code": 328, "Department": "HAEMATOLOGY", "Price": 300, "Result Type": "-", "Short Name": "APTT", "Method code": 30, "Method": "COAGULATION", "Specimen Code": 14, "Specimen": "Citrate Plasma", "Container Code": 8, "Container": "Citrate Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PCT", "code": 1642, "Department": "HAEMATOLOGY", "Result Unit": "mL/L", "Price": 0, "Result Type": "-", "Primary specimen code": 17, "Primary specimen ": "EDTA BLOOD", "Specimen Code": 9, "Specimen": "BLOOD", "Container Code": 7, "Container": "EDTA Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PCV", "code": 385, "Department": "HAEMATOLOGY", "Referance Range": "Male  : 40 - 54 Female: 37 - 47", "Result Unit": "%", "No of decimals": 1, "Price": 100, "Result Type": "-", "Short Name": "PCV", "Method code": 160, "Method": "Electrical Impedance", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PDW", "code": 1641, "Department": "HAEMATOLOGY", "Result Unit": "%", "Price": 0, "Result Type": "-", "Primary specimen code": 17, "Primary specimen ": "EDTA BLOOD", "Specimen Code": 9, "Specimen": "BLOOD", "Container Code": 7, "Container": "EDTA Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Platelet count", "code": 391, "Department": "HAEMATOLOGY", "Result Unit": "cells/cumm", "Critical Low": 80000, "   Critical High": 700000, "Price": 150, "Short Name": "PLT", "Method code": 160, "Method": "Electrical Impedance", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PDW-CV.", "code": 1697, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PDW-SD.", "code": 1696, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PERIPHERAL BLOOD SMEAR", "code": 442, "Department": "HAEMATOLOGY", "Price": 400, "Result Type": "No Unit / Ref. Value", "Short Name": "PS", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 10, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Peripheral Smear", "code": 1298, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Template", "Short Name": "Ps", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 1, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PH ( BLOOD)", "code": 387, "Department": "HAEMATOLOGY", "Price": 100, "Result Type": "Pick List", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PLATELET COUNT-5P", "code": 1701, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PLATELET COUNT.", "code": 1265, "Department": "HAEMATOLOGY", "Referance Range": "150000 - 400000.", "Price": 150, "Result Type": "-", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Platelets Count.", "code": 1695, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "Method code": 160, "Method": "Electrical Impedance", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PORPHOBILINOGEN -QUANTITAITVE", "code": 390, "Department": "HAEMATOLOGY", "Referance Range": "Spot sample  :  0 to 0.2 mg/dl    24 hrs sample :  0 - 3.4 mg/24hrs", "Result Unit": "mg/dL", "Price": 4000, "Result Type": "-", "Method code": 45, "Method": "Column Chromatography", "Specimen Code": 45, "Specimen": "SPOT URINE", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PROTHROMBIN TIME", "code": 329, "Department": "HAEMATOLOGY", "Price": 250, "Result Type": "-", "Short Name": "PT", "Specimen Code": 14, "Specimen": "Citrate Plasma", "Container Code": 8, "Container": "Citrate Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RDW - CV", "code": 432, "Department": "HAEMATOLOGY", "Referance Range": 45945, "Result Unit": "%", "No of decimals": 1, "Price": 100, "Result Type": "-", "Method code": 9, "Method": "Automated", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RDW - SD", "code": 1637, "Department": "HAEMATOLOGY", "Result Unit": "fl", "No of decimals": 2, "Price": 0, "Result Type": "-", "Method code": 9, "Method": "Automated", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RDW-CV.", "code": 1693, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "Method code": 9, "Method": "Automated", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RDW-SD.", "code": 1694, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "Method code": 9, "Method": "Automated", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Red Blood Cell (RBC) Count", "code": 396, "Department": "HAEMATOLOGY", "Referance Range": "Female : 4.00 - 5.50\nMale : 4.50 - 6.20", "Result Unit": "million/cumm", "No of decimals": 2, "Price": 100, "Result Type": "Pick List", "Short Name": "RBC", "Method code": 160, "Method": "Electrical Impedance", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RED BLOOD CELL COUNT-5P", "code": 1700, "Department": "HAEMATOLOGY", "Price": 0, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RETICULOCYTE COUNT", "code": 398, "Department": "HAEMATOLOGY", "Referance Range": "At Birth        : 1.1 - 4.5  Neonates        : 0.1 - 1.5  Infants         : 2.0 - 6.0  child(>6 months): 0.5 - 4.0  Adult           : 0.5 - 2.5", "Result Unit": "%", "No of decimals": 1, "Price": 200, "Result Type": "-", "Short Name": "RET", "Method code": 110, "Method": "Microscopic", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Min. Process Time": 10, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RH ANTIBODY TITRE", "code": 400, "Department": "HAEMATOLOGY", "Price": 170, "Result Type": "Pick List", "Short Name": "RHAB", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "SPECIFIC GRAVITY URINE", "code": 405, "Department": "HAEMATOLOGY", "No of decimals": 3, "Price": 50, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "RVVT - Russell viper venom time", "code": 429, "Department": "HAEMATOLOGY", "Price": 700, "Result Type": "Pick List\t", "Short Name": "RVVT", "Method code": 6, "Method": "ACM", "Specimen Code": 14, "Specimen": "Citrate Plasma", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "SICKLING TEST", "code": 402, "Department": "HAEMATOLOGY", "Price": 100, "Result Type": "Pick List", "Short Name": "SICK", "Min. Process Time": 5, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Thrombin Time", "code": 436, "Department": "HAEMATOLOGY", "Price": 650, "Result Type": "Pick List", "Short Name": "TT", "Method code": 56, "Method": "Electromechanical Clot Detection", "Specimen Code": 14, "Specimen": "Citrate Plasma", "Container Code": 8, "Container": "Citrate Container", "Min. Process Time": 1, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Total WBC count", "code": 407, "Department": "HAEMATOLOGY", "Referance Range": "4000.000 - 10000.000", "Result Unit": "cells/cumm", "Critical Low": 2000, "   Critical High": 50000, "Price": 100, "Result Type": "-", "Short Name": "TC", "Method code": 157, "Method": "Fluorescent Flow Cytometry", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Tuberculin skin (Mantoux) Test", "code": 368, "Department": "HAEMATOLOGY", "Price": 150, "Result Type": "Pick List", "Short Name": "Mx", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE CHYLURIA", "code": 181, "Department": "HAEMATOLOGY", "Price": 250, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE FAT GLOBULIN", "code": 410, "Department": "HAEMATOLOGY", "Price": 100, "Result Type": "No Unit / Ref. Value", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE MYOGLOBIN", "code": 412, "Department": "HAEMATOLOGY", "Referance Range": "0 - 1000", "Result Unit": "ug/L", "Price": 600, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "vW (<PERSON>) Factor, Plasma", "code": 677, "Department": "HAEMATOLOGY", "Referance Range": "49.50 - 187.00", "Result Unit": "%", "Price": 8500, "Result Type": "Pick List", "Short Name": "<PERSON><PERSON><PERSON>", "Method code": 98, "Method": "Immunoturbidimetry", "Specimen Code": 14, "Specimen": "Citrate Plasma", "Container Code": 8, "Container": "Citrate Container", "      Test Done On": "all", "Applicable to": "Both"}]