#!/usr/bin/env python3
"""
Migrate existing billing reports to use corrected totals (base amounts without GST)
"""
import sys
import os
import json
from datetime import datetime

# Add backend directory to path
sys.path.append('backend')

def safe_float(value, default=0):
    """Safely convert value to float"""
    try:
        if value is None or value == '':
            return default
        return float(value)
    except (ValueError, TypeError):
        return default

def migrate_billing_reports_totals():
    """Migrate existing billing reports to use corrected totals"""
    
    try:
        from utils import read_data, write_data
        
        print("🔧 Migrating Billing Reports Totals...")
        
        # Load both billing and billing reports
        billings = read_data('billings.json')
        billing_reports = read_data('billing_reports.json')
        
        print(f"Found {len(billings)} billing records and {len(billing_reports)} reports")
        
        # Create a lookup for billing records
        billing_lookup = {b.get('id'): b for b in billings}
        
        updated_count = 0
        
        # Update each report
        for i, report in enumerate(billing_reports):
            billing_id = report.get('billing_id')
            
            if billing_id in billing_lookup:
                billing = billing_lookup[billing_id]
                billing_total = safe_float(billing.get('total_amount', 0))
                
                # Get current report total
                current_total = safe_float(report.get('financial_summary', {}).get('total_amount', 0))
                
                # Update financial summary to match billing (base amount, no GST)
                if 'financial_summary' not in report:
                    report['financial_summary'] = {}
                
                # Update with corrected values
                report['financial_summary'].update({
                    'bill_amount': billing_total,
                    'subtotal': billing_total,
                    'total_amount': billing_total,
                    'paid_amount': safe_float(billing.get('paid_amount', 0)),
                    'balance': billing_total - safe_float(billing.get('paid_amount', 0)),
                    'updated_at': datetime.now().isoformat()
                })
                
                # Add notes field if missing
                if 'notes' not in report:
                    report['notes'] = billing.get('notes', '')
                
                # Update the report
                billing_reports[i] = report
                updated_count += 1
                
                print(f"  Updated report {report.get('sid_number', 'Unknown')}: {current_total} → {billing_total}")
            else:
                print(f"  Warning: No billing record found for report {report.get('sid_number', 'Unknown')} (billing_id: {billing_id})")
        
        # Save updated reports
        if updated_count > 0:
            # Create backup first
            backup_filename = f"billing_reports_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            print(f"Creating backup: {backup_filename}")
            
            import shutil
            shutil.copy('backend/data/billing_reports.json', f'backend/data/{backup_filename}')
            
            # Save updated data
            write_data('billing_reports.json', billing_reports)
            print(f"✅ Migration complete: {updated_count} reports updated")
        else:
            print("ℹ️  No reports needed updating")
            
        return True
        
    except Exception as e:
        print(f"❌ Migration error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_migration():
    """Verify that the migration worked correctly"""
    
    try:
        from utils import read_data
        
        print(f"\n🔧 Verifying Migration...")
        
        # Load both billing and billing reports
        billings = read_data('billings.json')
        billing_reports = read_data('billing_reports.json')
        
        # Check consistency
        mismatches = 0
        
        for report in billing_reports[:10]:  # Check first 10
            billing_id = report.get('billing_id')
            billing = next((b for b in billings if b.get('id') == billing_id), None)
            
            if billing:
                billing_total = safe_float(billing.get('total_amount', 0))
                report_total = safe_float(report.get('financial_summary', {}).get('total_amount', 0))
                
                if abs(billing_total - report_total) > 0.01:
                    mismatches += 1
                    print(f"  ❌ Mismatch: SID {report.get('sid_number')} - Billing: {billing_total}, Report: {report_total}")
                else:
                    print(f"  ✅ Match: SID {report.get('sid_number')} - {billing_total}")
        
        if mismatches == 0:
            print(f"✅ All checked reports have matching totals")
        else:
            print(f"⚠️  {mismatches} reports still have mismatched totals")
            
        # Check notes field
        reports_with_notes = sum(1 for r in billing_reports if 'notes' in r)
        print(f"✅ Reports with notes field: {reports_with_notes}/{len(billing_reports)}")
        
    except Exception as e:
        print(f"❌ Verification error: {str(e)}")

if __name__ == "__main__":
    print("🔄 BILLING REPORTS MIGRATION")
    print("=" * 50)
    
    # Run migration
    success = migrate_billing_reports_totals()
    
    if success:
        # Verify migration
        verify_migration()
    
    print("\n" + "=" * 50)
    print("🔄 MIGRATION COMPLETE")
    
    if success:
        print("✅ Billing reports have been updated with corrected totals")
        print("✅ Notes field has been added to all reports")
        print("✅ Financial summaries now show base amounts (no GST)")
    else:
        print("❌ Migration failed - please check the errors above")
