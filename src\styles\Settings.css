/* Settings Styles */

.settings-container {
  padding: 1.5rem;
}

/* Tab Styles */
.nav-tabs .nav-link {
  color: var(--dark-gray);
  font-weight: 500;
  padding: 0.75rem 1rem;
}

.nav-tabs .nav-link.active {
  color: var(--primary);
  font-weight: 600;
}

/* Settings Section Styles */
.settings-section {
  padding: 1rem 0;
}

/* Form Styles */
.form-label {
  font-weight: 500;
  color: var(--dark-gray);
}

.form-text {
  color: var(--gray);
}

/* Switch Styles */
.form-switch .form-check-input {
  width: 3em;
  height: 1.5em;
}

.form-switch .form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

/* Card Footer Styles */
.card-footer {
  background-color: rgba(var(--primary-rgb), 0.05);
}

/* Responsive Styles */
@media (max-width: 767.98px) {
  .settings-container {
    padding: 1rem;
  }

  .d-flex.justify-content-between {
    flex-direction: column;
    align-items: flex-start !important;
  }

  .d-flex.justify-content-between button {
    margin-top: 1rem;
    width: 100%;
  }

  /* Enhanced Mobile Tab Navigation */
  .nav-tabs {
    border-bottom: 2px solid var(--border-color);
    margin-bottom: 0;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .nav-tabs::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  .nav-tabs .nav-item {
    flex-shrink: 0;
  }

  .nav-tabs .nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    min-height: 44px; /* Minimum touch target */
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    border: 1px solid transparent;
    border-radius: 0.5rem 0.5rem 0 0;
    margin-right: 0.25rem;
    transition: all 0.2s ease;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    position: relative;
  }

  .nav-tabs .nav-link:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
    border-color: rgba(var(--primary-rgb), 0.2);
    transform: translateY(-1px);
  }

  .nav-tabs .nav-link:active {
    transform: translateY(0);
    background-color: rgba(var(--primary-rgb), 0.15);
  }

  .nav-tabs .nav-link.active {
    background-color: var(--primary);
    color: white !important;
    border-color: var(--primary);
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.3);
  }

  .nav-tabs .nav-link.active:hover {
    background-color: var(--primary);
    transform: none;
  }

  /* Add visual feedback for touch interactions */
  .nav-tabs .nav-link::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.1s ease;
    border-radius: inherit;
  }

  .nav-tabs .nav-link:active::after {
    opacity: 1;
  }

  /* Icon spacing in tabs */
  .nav-tabs .nav-link .fa-icon,
  .nav-tabs .nav-link svg {
    margin-right: 0.5rem;
    font-size: 0.875rem;
  }

  .card-footer {
    text-align: center;
  }

  .card-footer button {
    width: 100%;
  }
}

/* Extra small screens - stack tabs vertically */
@media (max-width: 575.98px) {
  .nav-tabs {
    flex-direction: column;
    border-bottom: none;
    border-right: 2px solid var(--border-color);
    overflow: visible;
    white-space: normal;
  }

  .nav-tabs .nav-link {
    margin-right: 0;
    margin-bottom: 0.25rem;
    border-radius: 0.5rem;
    text-align: left;
    justify-content: flex-start;
    width: 100%;
  }

  .nav-tabs .nav-link.active {
    margin-left: 0.25rem;
  }
}
