/* MUI Integration with Bootstrap Styles */

/* Ensure MUI Autocomplete integrates well with Bootstrap forms */
.MuiAutocomplete-root {
  margin-bottom: 1rem !important;
}

.MuiOutlinedInput-root {
  font-size: 0.875rem !important;
  border-radius: 0.375rem !important;
}

.MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline {
  border-color: #ced4da !important;
}

.MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: #80bdff !important;
}

.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #80bdff !important;
  border-width: 1px !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

.MuiInputLabel-root {
  font-size: 0.875rem !important;
  color: #495057 !important;
}

.MuiInputLabel-root.Mui-focused {
  color: #80bdff !important;
}

.MuiAutocomplete-listbox {
  font-size: 0.875rem !important;
}

.MuiAutocomplete-option {
  font-size: 0.875rem !important;
  padding: 8px 16px !important;
}

.MuiAutocomplete-option:hover {
  background-color: #f8f9fa !important;
}

.MuiAutocomplete-option[aria-selected="true"] {
  background-color: #007bff !important;
  color: white !important;
}

.MuiAutocomplete-noOptions {
  font-size: 0.875rem !important;
  color: #6c757d !important;
  font-style: italic !important;
}

.MuiAutocomplete-loading {
  font-size: 0.875rem !important;
  color: #6c757d !important;
}

/* Ensure proper spacing in form groups */
.form-group .MuiAutocomplete-root {
  margin-bottom: 0 !important;
}

/* React-Select integration styles */
.react-select-container {
  font-size: 0.875rem;
}

.react-select__control {
  border-color: #ced4da !important;
  border-radius: 0.375rem !important;
  min-height: 38px !important;
}

.react-select__control:hover {
  border-color: #80bdff !important;
}

.react-select__control--is-focused {
  border-color: #80bdff !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

.react-select__value-container {
  padding: 2px 8px !important;
}

.react-select__input-container {
  margin: 0 !important;
  padding: 0 !important;
}

.react-select__placeholder {
  color: #6c757d !important;
}

.react-select__single-value {
  color: #495057 !important;
}

.react-select__menu {
  border-radius: 0.375rem !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.react-select__option {
  font-size: 0.875rem !important;
  padding: 8px 12px !important;
}

.react-select__option:hover {
  background-color: #f8f9fa !important;
  color: #212529 !important;
}

.react-select__option--is-selected {
  background-color: #007bff !important;
  color: white !important;
}

.react-select__option--is-focused {
  background-color: #f8f9fa !important;
  color: #212529 !important;
}

/* Loading and no options styles */
.react-select__loading-indicator {
  color: #6c757d !important;
}

.react-select__no-options-message {
  color: #6c757d !important;
  font-style: italic !important;
}

/* Ensure dropdowns work well in modals */
.modal .MuiAutocomplete-root {
  z-index: 1060 !important;
}

.modal .react-select__menu {
  z-index: 1060 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .MuiOutlinedInput-root {
    font-size: 1rem !important;
  }
  
  .MuiInputLabel-root {
    font-size: 1rem !important;
  }
  
  .MuiAutocomplete-listbox {
    font-size: 1rem !important;
  }
  
  .MuiAutocomplete-option {
    font-size: 1rem !important;
    padding: 12px 16px !important;
  }
}

/* Error states */
.MuiOutlinedInput-root.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #dc3545 !important;
}

.MuiInputLabel-root.Mui-error {
  color: #dc3545 !important;
}

/* Disabled states */
.MuiOutlinedInput-root.Mui-disabled {
  background-color: #e9ecef !important;
  color: #6c757d !important;
}

.MuiOutlinedInput-root.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: #ced4da !important;
}

/* Clear button styling */
.MuiAutocomplete-clearIndicator {
  color: #6c757d !important;
}

.MuiAutocomplete-clearIndicator:hover {
  color: #495057 !important;
}

/* Dropdown arrow styling */
.MuiAutocomplete-popupIndicator {
  color: #6c757d !important;
}

.MuiAutocomplete-popupIndicator:hover {
  color: #495057 !important;
}
