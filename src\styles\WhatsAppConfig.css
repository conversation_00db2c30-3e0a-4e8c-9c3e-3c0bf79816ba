.whatsapp-config {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 20px;
}

.page-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 2rem;
  font-weight: 600;
}

.page-header p {
  color: #6c757d;
  margin-bottom: 0;
  font-size: 1.1rem;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #6c757d;
  font-size: 1.1rem;
}

.config-form-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.config-form {
  padding: 30px;
}

.form-section {
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid #e9ecef;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.form-section h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.form-section h3::before {
  content: '';
  width: 4px;
  height: 20px;
  background: #25d366;
  margin-right: 10px;
  border-radius: 2px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c3e50;
}

.form-control {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #25d366;
  box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
}

.form-control[type="password"] {
  font-family: 'Courier New', monospace;
}

.form-check {
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-check-input {
  width: 18px;
  height: 18px;
  margin: 0;
}

.form-check-label {
  margin: 0;
  cursor: pointer;
  user-select: none;
}

.form-text {
  font-size: 12px;
  color: #6c757d;
  margin-top: 5px;
}

.form-actions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: #25d366;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #20b358;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.alert {
  padding: 15px 20px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid transparent;
}

.alert-success {
  background-color: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.help-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 30px;
  border-left: 4px solid #25d366;
}

.help-section h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.2rem;
  font-weight: 600;
}

.help-section ol {
  margin: 0;
  padding-left: 20px;
}

.help-section li {
  margin-bottom: 10px;
  color: #495057;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .whatsapp-config {
    padding: 15px;
  }
  
  .config-form {
    padding: 20px;
  }
  
  .page-header h1 {
    font-size: 1.5rem;
  }
  
  .form-section h3 {
    font-size: 1.1rem;
  }
  
  .help-section {
    padding: 20px;
  }
}

/* Animation for form submission */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.btn-primary:active {
  animation: pulse 0.3s ease;
}
