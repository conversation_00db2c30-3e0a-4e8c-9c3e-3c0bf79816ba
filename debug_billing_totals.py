#!/usr/bin/env python3
"""
Debug billing totals calculation
"""
import sys
import os
import json

# Add backend directory to path
sys.path.append('backend')

def safe_float(value, default=0):
    """Safely convert value to float"""
    try:
        if value is None or value == '':
            return default
        return float(value)
    except (ValueError, TypeError):
        return default

def debug_billing_totals():
    """Debug billing totals for specific billing records"""
    
    try:
        from utils import read_data
        
        print("🔧 Debugging Billing Totals...")
        
        # Load billing data
        billings = read_data('billings.json')
        
        # Check billing 108 specifically
        billing = next((b for b in billings if b['id'] == 108), None)
        
        if not billing:
            print("❌ Billing 108 not found")
            return
            
        test_items = billing.get('test_items', [])
        stored_total = billing.get('total_amount', 0)
        
        print(f"\n📊 Billing ID 108 Analysis:")
        print(f"   Test items count: {len(test_items)}")
        print(f"   Stored total_amount: {stored_total}")
        
        # Calculate actual total
        actual_total = 0
        print(f"\n📋 Test Items Breakdown:")
        
        for i, item in enumerate(test_items):
            amount = item.get('amount', 0)
            name = item.get('test_name', 'Unknown')
            safe_amount = safe_float(amount)
            actual_total += safe_amount
            
            print(f"   {i+1:2d}. {name[:30]:<30} | Amount: {amount} ({type(amount).__name__}) | Safe: {safe_amount}")
            
        print(f"\n💰 Total Calculation:")
        print(f"   Calculated total: {actual_total}")
        print(f"   Stored total: {stored_total}")
        print(f"   Difference: {stored_total - actual_total}")
        
        if abs(stored_total - actual_total) > 0.01:
            print("❌ TOTAL MISMATCH DETECTED!")
        else:
            print("✅ Totals match correctly")
            
        # Check a few other billing records
        print(f"\n🔍 Checking other billing records...")

        for billing_id in [107, 109, 110]:
            other_billing = next((b for b in billings if b['id'] == billing_id), None)
            if other_billing:
                other_test_items = other_billing.get('test_items', [])
                other_items = other_billing.get('items', [])
                other_stored = other_billing.get('total_amount', 0)

                # Calculate from test_items
                test_items_total = sum(safe_float(item.get('amount', 0)) for item in other_test_items)

                # Calculate from items
                items_total = sum(safe_float(item.get('amount', 0)) for item in other_items)

                print(f"   ID {billing_id}:")
                print(f"     test_items count: {len(other_test_items)}, total: {test_items_total}")
                print(f"     items count: {len(other_items)}, total: {items_total}")
                print(f"     stored total: {other_stored}")
                print(f"     difference (test_items): {other_stored - test_items_total}")
                print(f"     difference (items): {other_stored - items_total}")
                print()
                
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

def test_calculation_function():
    """Test the exact calculation logic from the API function"""
    
    try:
        from utils import read_data
        
        print(f"\n🧪 Testing API Calculation Logic...")
        
        billings = read_data('billings.json')
        billing = next((b for b in billings if b['id'] == 108), None)
        
        if billing:
            # This is the exact logic from the API function
            subtotal = sum(safe_float(item.get('amount', 0)) for item in billing['test_items'])
            
            print(f"   API calculation result: {subtotal}")
            print(f"   Current stored total: {billing.get('total_amount', 0)}")
            
            # Test with different approaches
            total_v1 = sum(float(item.get('amount', 0)) if item.get('amount') else 0 for item in billing['test_items'])
            print(f"   Alternative calc v1: {total_v1}")
            
            total_v2 = 0
            for item in billing['test_items']:
                amount = item.get('amount', 0)
                if isinstance(amount, str):
                    try:
                        total_v2 += float(amount)
                    except:
                        pass
                else:
                    total_v2 += float(amount) if amount else 0
            print(f"   Alternative calc v2: {total_v2}")
            
    except Exception as e:
        print(f"❌ Error in calculation test: {str(e)}")

if __name__ == "__main__":
    debug_billing_totals()
    test_calculation_function()
