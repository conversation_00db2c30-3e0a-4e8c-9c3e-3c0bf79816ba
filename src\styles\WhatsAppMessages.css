.whatsapp-messages {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 20px;
}

.page-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 2rem;
  font-weight: 600;
}

.page-header p {
  color: #6c757d;
  margin-bottom: 0;
  font-size: 1.1rem;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #6c757d;
  font-size: 1.1rem;
}

.messages-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 25px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #25d366;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-card h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
}

.stat-card p {
  color: #6c757d;
  margin: 0;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.messages-table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 30px;
}

.table {
  margin: 0;
  width: 100%;
  border-collapse: collapse;
}

.table thead {
  background: #f8f9fa;
}

.table th {
  padding: 15px 20px;
  font-weight: 600;
  color: #ffffff;
  border-bottom: 2px solid #e9ecef;
  text-align: left;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table td {
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.table tbody tr:hover {
  background-color: #f8f9fa;
}

.table tbody tr:last-child td {
  border-bottom: none;
}

.badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.badge-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.badge-info {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.badge-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.badge-primary {
  background-color: #cce7ff;
  color: #004085;
  border: 1px solid #b3d7ff;
}

.badge-secondary {
  background-color: #e2e3e5;
  color: #383d41;
  border: 1px solid #d6d8db;
}

.message-preview {
  max-width: 300px;
  font-size: 0.9rem;
  color: #495057;
  line-height: 1.4;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.8rem;
  border-radius: 4px;
}

.btn-outline-primary {
  color: #25d366;
  border: 1px solid #25d366;
  background: transparent;
}

.btn-outline-primary:hover {
  background: #25d366;
  color: white;
}

.text-center {
  text-align: center;
}

.text-muted {
  color: #6c757d;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.pagination {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 5px;
}

.page-item {
  display: flex;
}

.page-link {
  padding: 10px 15px;
  border: 1px solid #ddd;
  background: #fff;
  color: #495057;
  text-decoration: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-link:hover:not(:disabled) {
  background: #25d366;
  color: white;
  border-color: #25d366;
}

.page-item.active .page-link {
  background: #25d366;
  color: white;
  border-color: #25d366;
}

.page-item.disabled .page-link {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .messages-table-container {
    overflow-x: auto;
  }
  
  .table {
    min-width: 800px;
  }
}

@media (max-width: 768px) {
  .whatsapp-messages {
    padding: 15px;
  }
  
  .messages-stats {
    grid-template-columns: 1fr;
  }
  
  .page-header h1 {
    font-size: 1.5rem;
  }
  
  .table th,
  .table td {
    padding: 10px 15px;
    font-size: 0.8rem;
  }
  
  .message-preview {
    max-width: 200px;
  }
  
  .pagination {
    flex-wrap: wrap;
  }
  
  .page-link {
    padding: 8px 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .stat-card {
    padding: 20px;
  }
  
  .stat-card h3 {
    font-size: 1.5rem;
  }
  
  .table th,
  .table td {
    padding: 8px 10px;
  }
  
  .message-preview {
    max-width: 150px;
  }
}
