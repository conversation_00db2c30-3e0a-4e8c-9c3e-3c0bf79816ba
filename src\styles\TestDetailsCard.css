/* Test Details Card Styles */

/* CSS Variables for Test Cards */
:root {
  --test-card-border-radius: 0.75rem;
  --test-card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --test-card-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.15);
  --test-card-transition: all 0.3s ease;
  --test-card-header-bg: #f8f9fa;
  --test-card-border: #e9ecef;
}

/* Test Cards Section */
.test-cards-section {
  margin-bottom: 2rem;
}

/* Test Cards Grid */
.test-cards-grid {
  margin-bottom: 1.5rem;
}

.test-card-column {
  display: flex;
  align-items: stretch;
}

/* Test Details Card */
.test-details-card {
  border: 1px solid var(--test-card-border);
  border-radius: var(--test-card-border-radius);
  box-shadow: var(--test-card-shadow);
  transition: var(--test-card-transition);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.test-details-card:hover {
  box-shadow: var(--test-card-shadow-hover);
  transform: translateY(-2px);
}

/* Card Header */
.test-card-header {
  background-color: var(--test-card-header-bg);
  border-bottom: 2px solid var(--bs-primary);
  padding: 1rem;
  flex-shrink: 0;
}

.test-card-title h6 {
  color: var(--bs-dark);
  font-weight: 600;
  margin: 0;
  line-height: 1.3;
}

.test-id-badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.5rem;
  border-radius: 0.375rem;
  white-space: nowrap;
}

/* Card Body */
.test-card-body {
  padding: 1rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.test-detail-item {
  margin-bottom: 0.75rem;
}

.test-detail-item:last-child {
  margin-bottom: 0;
}

.test-detail-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.test-detail-value {
  font-size: 0.875rem;
  color: var(--bs-dark);
  line-height: 1.4;
}

/* Instructions Styling */
.test-instructions {
  font-size: 0.8rem;
  color: var(--bs-dark);
  line-height: 1.4;
  min-height: 2.5rem;
  border: 1px solid #e9ecef;
  font-style: italic;
}

/* Card Footer */
.test-card-footer {
  padding: 0.75rem 1rem;
  border-top: 1px solid var(--test-card-border);
  flex-shrink: 0;
}

.financial-item {
  text-align: center;
}

.financial-label {
  font-size: 0.7rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;
}

.financial-value {
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.2;
}

/* Pagination Styles */
.test-cards-pagination {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

/* Responsive Design */

/* Mobile Styles (up to 767px) */
@media (max-width: 767.98px) {
  .test-cards-grid {
    margin-bottom: 1rem;
  }

  .test-details-card {
    margin-bottom: 1rem;
    border-radius: 0.5rem;
  }

  .test-card-header {
    padding: 0.75rem;
  }

  .test-card-title h6 {
    font-size: 0.9rem;
  }

  .test-id-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.375rem;
  }

  .test-card-body {
    padding: 0.75rem;
  }

  .test-detail-label {
    font-size: 0.7rem;
  }

  .test-detail-value {
    font-size: 0.8rem;
  }

  .test-instructions {
    font-size: 0.75rem;
    min-height: 2rem;
    padding: 0.5rem;
  }

  .test-card-footer {
    padding: 0.5rem 0.75rem;
  }

  .financial-label {
    font-size: 0.65rem;
  }

  .financial-value {
    font-size: 0.8rem;
  }

  /* Single column layout on mobile */
  .test-cards-grid .test-card-column {
    margin-bottom: 0.75rem;
  }
}

/* Tablet Styles (768px to 1023px) */
@media (min-width: 768px) and (max-width: 1023.98px) {
  .test-cards-grid {
    gap: 1rem;
  }

  .test-details-card {
    height: 100%;
  }

  /* Two columns on tablet */
  .test-cards-grid .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

/* Desktop Styles (1024px and up) */
@media (min-width: 1024px) {
  .test-cards-grid {
    gap: 1.25rem;
  }

  .test-details-card {
    height: 100%;
  }

  /* Three columns on desktop */
  .test-cards-grid .col-lg-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .test-card-header {
    padding: 1.25rem;
  }

  .test-card-body {
    padding: 1.25rem;
  }

  .test-card-footer {
    padding: 1rem 1.25rem;
  }
}

/* Large Desktop Styles (1200px and up) */
@media (min-width: 1200px) {
  .test-cards-section {
    max-width: 1140px;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Accessibility Improvements */
.test-details-card:focus-within {
  outline: 2px solid var(--bs-primary);
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .test-details-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }

  .test-cards-pagination {
    display: none;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .test-details-card {
    border: 2px solid var(--bs-dark);
  }

  .test-card-header {
    background-color: var(--bs-light);
    border-bottom: 3px solid var(--bs-primary);
  }

  .test-detail-label {
    color: var(--bs-dark);
    font-weight: 700;
  }
}
