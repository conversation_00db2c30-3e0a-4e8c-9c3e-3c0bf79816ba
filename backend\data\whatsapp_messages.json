[{"id": 1, "tenant_id": 1, "user_id": 2, "recipient_number": "**********", "message_content": "From: Mayiladuth<PERSON> Hub Admin (AVINI Labs Mayiladuthurai)\nPatient: <PERSON>\nDate: 2025-05-22 12:43\n\nYour blood test results are ready. All parameters are within normal range.", "message_type": "report", "order_id": 1, "billing_id": null, "status": "sent", "created_at": "2025-05-22T07:13:14.802678", "sent_at": "2025-05-22T07:13:14.802946", "delivered_at": null, "message_id": "simulated-message-id-1", "error_message": null}, {"id": 2, "tenant_id": 1, "user_id": 2, "recipient_number": "**********", "message_content": "From: Mayiladuth<PERSON> Hub Admin (AVINI Labs Mayiladuthurai)\nPatient: <PERSON>\nDate: 2025-05-22 12:43\n\nYour invoice is ready. Invoice #INV001. Total amount: ₹1500. Thank you for choosing AVINI LABS.", "message_type": "invoice", "order_id": null, "billing_id": 1, "status": "sent", "created_at": "2025-05-22T07:13:20.603230", "sent_at": "2025-05-22T07:13:20.603521", "delivered_at": null, "message_id": "simulated-message-id-2", "error_message": null}, {"id": 3, "tenant_id": 1, "user_id": 2, "recipient_number": "**********", "message_content": "From: Mayiladuth<PERSON> Hub Admin (AVINI Labs Mayiladuthurai)\nPatient: <PERSON><PERSON>\nDate: 2025-05-22 12:47\n\nYour Blood Glucose test result is ready. Result: 85 mg/dL (Normal range: 70-100). All parameters are within normal range.", "message_type": "report", "order_id": 1, "billing_id": null, "status": "sent", "created_at": "2025-05-22T07:17:06.232474", "sent_at": "2025-05-22T07:17:06.232830", "delivered_at": null, "message_id": "simulated-message-id-3", "error_message": null}, {"id": 4, "tenant_id": 1, "user_id": 2, "recipient_number": "**********", "message_content": "From: Mayiladuth<PERSON> Hub Admin (AVINI Labs Mayiladuthurai)\nPatient: <PERSON><PERSON>\nDate: 2025-05-22 12:47\n\nYour invoice is ready. Invoice #INV-MYL-002. Total amount: ₹2000. Status: Pending. Please contact us for payment.", "message_type": "invoice", "order_id": null, "billing_id": 2, "status": "sent", "created_at": "2025-05-22T07:17:13.539038", "sent_at": "2025-05-22T07:17:13.539308", "delivered_at": null, "message_id": "simulated-message-id-4", "error_message": null}, {"id": 5, "tenant_id": 1, "user_id": 2, "recipient_number": "**********", "message_content": "From: Mayiladuth<PERSON> Hub Admin (AVINI Labs Mayiladuthurai)\nPatient: <PERSON><PERSON><PERSON>\nDate: 2025-05-22 13:26\n\nYour test results are ready. All parameters are within normal range. Please contact us if you have any questions.", "message_type": "report", "order_id": 10, "billing_id": null, "status": "sent", "created_at": "2025-05-22T07:56:19.809919", "sent_at": "2025-05-22T07:56:19.810602", "delivered_at": null, "message_id": "simulated-message-id-5", "error_message": null}]