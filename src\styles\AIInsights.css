/* AI Insights Dashboard Styles */
.ai-insights-dashboard {
  margin-bottom: 2rem;
}

.ai-insights-dashboard .card {
  border: none;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.ai-insights-dashboard .border-left-primary {
  border-left: 0.25rem solid #4e73df !important;
}

.ai-insights-dashboard .border-left-success {
  border-left: 0.25rem solid #1cc88a !important;
}

.ai-insights-dashboard .border-left-info {
  border-left: 0.25rem solid #36b9cc !important;
}

.ai-insights-dashboard .border-left-warning {
  border-left: 0.25rem solid #f6c23e !important;
}

.ai-insights-dashboard .border-left-danger {
  border-left: 0.25rem solid #e74a3b !important;
}

/* Recommendation Items */
.recommendation-item {
  border-radius: 0.35rem;
  transition: all 0.3s ease;
}

.recommendation-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
}

.recommendation-item .border-left-danger {
  border-left: 0.25rem solid #e74a3b !important;
}

.recommendation-item .border-left-warning {
  border-left: 0.25rem solid #f6c23e !important;
}

.recommendation-item .border-left-info {
  border-left: 0.25rem solid #36b9cc !important;
}

/* Prediction Items */
.prediction-item {
  padding: 1rem;
  border-radius: 0.35rem;
  background: linear-gradient(135deg, #f8f9fc 0%, #e3e6f0 100%);
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.prediction-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 0.15rem 0.75rem rgba(0, 0, 0, 0.1);
}

/* Status Summary */
.status-summary-item {
  padding: 1rem;
  transition: all 0.3s ease;
}

.status-summary-item:hover {
  transform: scale(1.05);
}

.status-badge-large {
  font-size: 1.5rem;
  padding: 0.75rem 1rem;
  border-radius: 50%;
  width: 4rem;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Metric Items */
.metric-item {
  padding: 1rem;
  border-radius: 0.35rem;
  transition: all 0.3s ease;
}

.metric-item:hover {
  background-color: #f8f9fc;
  transform: translateY(-2px);
}

/* AI Analytics Page Styles */
.ai-analytics .nav-tabs {
  border-bottom: 2px solid #e3e6f0;
}

.ai-analytics .nav-tabs .nav-link {
  border: none;
  color: #6c757d;
  font-weight: 600;
  padding: 1rem 1.5rem;
  transition: all 0.3s ease;
}

.ai-analytics .nav-tabs .nav-link:hover {
  border-color: transparent;
  color: #4e73df;
  background-color: #f8f9fc;
}

.ai-analytics .nav-tabs .nav-link.active {
  color: #4e73df;
  background-color: #fff;
  border-color: #e3e6f0 #e3e6f0 #fff;
  border-bottom: 2px solid #4e73df;
}

/* Invoice Management Styles */
.invoice-management .card {
  border: none;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.invoice-management .status-badge-large {
  font-size: 1.25rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.35rem;
  min-width: 3rem;
  text-align: center;
}

/* Access Level Indicator */
.billing-dashboard .alert {
  border: none;
  border-radius: 0.35rem;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-insights-dashboard .recommendation-item {
    margin-bottom: 1rem;
  }
  
  .ai-insights-dashboard .prediction-item {
    margin-bottom: 1rem;
  }
  
  .status-summary-item {
    margin-bottom: 1rem;
    text-align: center;
  }
  
  .metric-item {
    margin-bottom: 1rem;
    text-align: center;
  }
  
  .ai-analytics .nav-tabs .nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
}

@media (max-width: 576px) {
  .status-badge-large {
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
  }
  
  .ai-insights-dashboard .h5 {
    font-size: 1.1rem;
  }
  
  .ai-insights-dashboard .h4 {
    font-size: 1.3rem;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading States */
.ai-insights-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Enhanced Card Styles */
.enhanced-card {
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease;
}

.enhanced-card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.enhanced-card .card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom: none;
  border-radius: 0.5rem 0.5rem 0 0;
}

/* Progress Indicators */
.progress-enhanced {
  height: 0.5rem;
  border-radius: 0.25rem;
  background-color: #e9ecef;
  overflow: hidden;
}

.progress-enhanced .progress-bar {
  border-radius: 0.25rem;
  transition: width 0.6s ease;
}

/* Button Enhancements */
.btn-ai {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.btn-ai:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(102, 126, 234, 0.4);
  color: white;
}

/* Insight Cards */
.insight-card {
  background: linear-gradient(135deg, #f8f9fc 0%, #e3e6f0 100%);
  border: 1px solid #e3e6f0;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.insight-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.1);
}

.insight-card .insight-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.insight-card .insight-icon.primary {
  background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
  color: white;
}

.insight-card .insight-icon.success {
  background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
  color: white;
}

.insight-card .insight-icon.warning {
  background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
  color: white;
}

.insight-card .insight-icon.danger {
  background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
  color: white;
}

/* SID Badge Enhancement */
.sid-badge {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  letter-spacing: 0.5px;
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  transition: all 0.3s ease;
}

.sid-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 0.25rem 0.5rem rgba(78, 115, 223, 0.3);
}
