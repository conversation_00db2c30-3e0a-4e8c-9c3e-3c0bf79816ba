/* User Management Styles - Mobile First */

.user-management-container {
  padding: 0.75rem;
  max-width: 100vw;
  overflow-x: hidden;
}

/* Ensure proper box-sizing */
.user-management-container *,
.user-management-container *::before,
.user-management-container *::after {
  box-sizing: border-box;
}

/* Table Styles */
.table th {
  font-weight: 600;
  color: var(--dark-gray);
}

.table-hover tbody tr:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
}

/* Sort Icons */
.sort-icon {
  margin-left: 0.5rem;
}

/* Pagination Styles */
.pagination {
  margin-bottom: 0;
}

.page-link {
  color: var(--primary);
}

.page-item.active .page-link {
  background-color: var(--primary);
  border-color: var(--primary);
}

/* Role Summary Styles - Enhanced for Mobile */
.role-summary-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 0.75rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  height: 100%;
  min-height: 80px;
  transition: all 0.3s ease-in-out;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.role-summary-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  border-color: var(--primary);
}

.role-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  color: white;
  margin-right: 0.75rem;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-size: 0.875rem;
}

.role-info {
  display: flex;
  flex-direction: column;
  min-width: 0;
  flex: 1;
}

.role-name {
  font-weight: 600;
  color: var(--dark-gray);
  font-size: 0.8rem;
  line-height: 1.2;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.role-count {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary);
  line-height: 1;
}

/* Enhanced Mobile Responsive Styles */
@media (max-width: 575.98px) {
  .user-management-container {
    padding: 0.5rem;
  }

  /* Header adjustments for mobile */
  .user-management-container h1 {
    font-size: 1.25rem !important;
  }

  /* Card header improvements */
  .card-header {
    padding: 0.75rem !important;
  }

  .card-header .d-flex {
    gap: 0.5rem !important;
  }

  .card-header .form-select,
  .card-header .input-group {
    font-size: 0.875rem;
  }

  /* Role summary mobile optimizations */
  .role-summary-item {
    padding: 0.5rem;
    min-height: 70px;
  }

  .role-badge {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
    margin-right: 0.5rem;
  }

  .role-name {
    font-size: 0.75rem;
  }

  .role-count {
    font-size: 1.1rem;
  }
}

/* Tablet responsive styles */
@media (min-width: 576px) and (max-width: 767.98px) {
  .user-management-container {
    padding: 1rem;
  }

  .role-summary-item {
    padding: 0.75rem;
  }

  .role-badge {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }

  .role-name {
    font-size: 0.85rem;
  }

  .role-count {
    font-size: 1.3rem;
  }
}

/* Desktop styles */
@media (min-width: 768px) {
  .user-management-container {
    padding: 1.5rem;
  }
}

/* Touch target improvements for mobile */
@media (max-width: 767.98px) {
  .btn {
    min-height: 44px;
    min-width: 44px;
  }

  .form-control,
  .form-select {
    min-height: 44px;
  }

  /* Improve spacing for touch interfaces */
  .gap-3 {
    gap: 1rem !important;
  }
}
