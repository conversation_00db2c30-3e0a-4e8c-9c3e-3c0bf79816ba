/* Main Layout Styles */

/* Content Wrapper */
.content-wrapper {
  display: flex;
  min-height: 100vh;
}

/* User Info Header - Enhanced for Mobile */
.user-info-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 40px;
  background-color: var(--secondary);
  color: var(--white);
  z-index: 1030;
  display: flex;
  align-items: center;
  padding: 0 0.75rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.user-info-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  font-size: 0.8rem;
}

.user-info-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.user-info-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.user-info-header .btn {
  min-height: 32px;
  min-width: 32px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* Sidebar */
.sidebar {
  width: 250px;
  background-color: var(--secondary);
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1020;
  padding-top: 40px; /* Account for user info header */
  padding-bottom: 20px; /* Add bottom padding for better scrolling */
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  transition: all 0.3s ease;
  overflow-y: auto;
  overflow-x: hidden;
  border-right: 3px solid var(--primary);
  /* Ensure smooth scrolling */
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: var(--primary) transparent;
}

/* Desktop Auto-Hide Hover Trigger Zone */
.sidebar-hover-trigger {
  position: fixed;
  top: 0;
  left: 0;
  width: 15px;
  height: 100vh;
  z-index: 1019; /* Just below sidebar */
  background: transparent;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.sidebar-hover-trigger:hover {
  background: linear-gradient(to right, rgba(212, 0, 110, 0.1) 0%, transparent 100%);
}

/* Desktop Auto-Hide States */
.sidebar.desktop-hidden {
  transform: translateX(-100%);
  box-shadow: none;
  pointer-events: none; /* Prevent interaction when hidden */
}

.sidebar.desktop-visible {
  transform: translateX(0);
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  pointer-events: auto; /* Re-enable interaction when visible */
}

/* Ensure smooth transitions for all sidebar states */
.sidebar.desktop-hidden,
.sidebar.desktop-visible {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Custom scrollbar for webkit browsers */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--light-pink);
}

.sidebar-brand {
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  text-decoration: none;
  color: var(--white);
  background-color: rgba(0, 0, 0, 0.2);
}

.sidebar-divider {
  margin: 0.5rem 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
}

.sidebar-heading {
  padding: 0.5rem 1rem;
  font-size: 0.7rem;
  font-weight: 800;
  text-transform: uppercase;
  color: var(--light-pink);
}

.sidebar .nav-item {
  position: relative;
}

.sidebar .nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--white);
  font-weight: 600;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.sidebar .nav-link:hover {
  color: var(--light-pink);
  background-color: rgba(212, 0, 110, 0.1);
  border-left: 3px solid var(--primary);
  transform: translateX(5px);
}

.sidebar .nav-link.active {
  color: var(--white);
  font-weight: 700;
  background-color: rgba(212, 0, 110, 0.2);
  border-left: 3px solid var(--primary);
}

.sidebar .nav-link span {
  margin-left: 0.75rem;
}

/* Content Area */
.content {
  flex: 1;
  padding: 1.5rem;
  margin-top: 40px; /* Add margin to account for the fixed header */
  margin-left: 250px; /* Add margin to account for the sidebar */
  transition: margin-left 0.3s ease;
}

/* Content Area Auto-Hide States */
.content.content-sidebar-hidden {
  margin-left: 0; /* Full width when sidebar is hidden */
}

.content.content-sidebar-visible {
  margin-left: 250px; /* Normal margin when sidebar is visible */
}

/* Mobile Menu Button */
.mobile-menu-button {
  display: none;
  position: fixed;
  bottom: 75px;
  right: 15px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary);
  color: white;
  text-align: center;
  line-height: 50px;
  font-size: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  z-index: 1060;
  cursor: pointer;
  transition: var(--transition);
  animation: pulse 2s infinite;
}

.mobile-menu-button:hover,
.mobile-menu-button:active,
.mobile-menu-button:focus {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
  color: white;
}

/* Sidebar Overlay */
.sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1015;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sidebar-overlay.visible {
  display: block;
  opacity: 1;
}

/* Responsive Styles */
@media (max-width: 768px) {
  body, html {
    overflow-x: hidden;
    width: 100%;
    position: relative;
    -webkit-overflow-scrolling: touch;
  }

  /* Sidebar adjustments */
  .sidebar {
    width: 0;
    position: fixed;
    top: 40px; /* Position below the user info header */
    left: 0;
    height: calc(100% - 40px); /* Adjust height for header */
    z-index: 1050;
    overflow-y: auto;
    transition: all 0.3s ease;
    transform: translateX(-100%);
    margin-top: 0; /* Reset margin for mobile */
    pointer-events: auto; /* Override desktop auto-hide pointer-events */
  }

  /* Override desktop auto-hide classes on mobile */
  .sidebar.desktop-hidden,
  .sidebar.desktop-visible {
    transform: translateX(-100%); /* Use mobile transform */
    pointer-events: auto;
  }

  /* Hide hover trigger zone on mobile */
  .sidebar-hover-trigger {
    display: none;
  }

  .sidebar.mobile-visible {
    width: 85%;
    max-width: 280px;
    transform: translateX(0);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
  }

  /* Content wrapper adjustments */
  .content-wrapper {
    flex-direction: column;
    padding-top: 0;
    margin-top: 0;
  }

  /* Content adjustments */
  .content {
    margin-left: 0;
    margin-top: 40px; /* Space for header */
    padding: 1rem;
    padding-bottom: 80px; /* Increased space for mobile nav */
    min-height: calc(100vh - 40px); /* Ensure full height minus header */
  }

  /* Show mobile menu button */
  .mobile-menu-button {
    display: block;
  }

  /* Show mobile navigation */
  .mobile-nav {
    display: block !important;
  }
}

/* Additional mobile styles for very small screens */
@media (max-width: 480px) {
  .mobile-nav-item {
    font-size: 0.6rem;
    padding: 0.2rem 0.3rem;
  }

  .mobile-nav-item svg,
  .mobile-nav-item i {
    font-size: 1rem;
  }

  .mobile-nav-item span {
    font-size: 0.6rem;
  }
}

/* Mobile Bottom Navigation - Enhanced */
.mobile-nav {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 60px;
  background-color: var(--secondary);
  border-top: 2px solid var(--primary);
  z-index: 1050; /* Higher z-index to ensure it's above other elements */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
  /* Ensure it stays at bottom on mobile */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  /* Support for iPhone notch */
  padding-bottom: env(safe-area-inset-bottom, 0);
}

.mobile-nav-content {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100%;
  padding: 0 0.5rem;
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--white);
  text-decoration: none;
  font-size: 0.7rem;
  font-weight: 600;
  transition: var(--transition);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  min-width: 50px;
  flex: 1;
  max-width: 80px;
  /* Ensure proper touch targets */
  min-height: 44px;
  cursor: pointer;
}

.mobile-nav-item:hover,
.mobile-nav-item.active {
  color: var(--light-pink);
  background-color: rgba(212, 0, 110, 0.2);
  text-decoration: none;
}

.mobile-nav-item svg,
.mobile-nav-item i {
  font-size: 1.2rem;
  margin-bottom: 0.2rem;
  display: block;
}

.mobile-nav-item span {
  font-size: 0.65rem;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* Animations */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(212, 0, 110, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(212, 0, 110, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(212, 0, 110, 0);
  }
}
