[{"id": 1, "sample_id": "S00001", "patient_id": 21, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-12", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-06-12T17:15:25.316838", "updated_at": "2025-06-12T17:15:25.316838", "tenant_id": 3, "collected_by": 2}, {"id": 2, "sample_id": "S00002", "patient_id": 10, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-13", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-06-13T17:15:25.316838", "updated_at": "2025-06-13T17:15:25.316838", "tenant_id": 3, "collected_by": 2}, {"id": 3, "sample_id": "S00003", "patient_id": 28, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-04", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-06-04T17:15:25.316838", "updated_at": "2025-06-04T17:15:25.316838", "tenant_id": 2, "collected_by": 1}, {"id": 4, "sample_id": "S00004", "patient_id": 5, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-06-14", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-06-14T17:15:25.316838", "updated_at": "2025-06-14T17:15:25.316838", "tenant_id": 3, "collected_by": 1}, {"id": 5, "sample_id": "S00005", "patient_id": 38, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-26", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-05-26T17:15:25.316838", "updated_at": "2025-05-26T17:15:25.316838", "tenant_id": 2, "collected_by": 3}, {"id": 6, "sample_id": "S00006", "patient_id": 46, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-06-04", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-06-04T17:15:25.316838", "updated_at": "2025-06-04T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 7, "sample_id": "S00007", "patient_id": 41, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-06-01", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-06-01T17:15:25.316838", "updated_at": "2025-06-01T17:15:25.316838", "tenant_id": 3, "collected_by": 1}, {"id": 8, "sample_id": "S00008", "patient_id": 17, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-14", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-06-14T17:15:25.316838", "updated_at": "2025-06-14T17:15:25.316838", "tenant_id": 1, "collected_by": 1}, {"id": 9, "sample_id": "S00009", "patient_id": 50, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-26", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-05-26T17:15:25.316838", "updated_at": "2025-05-26T17:15:25.316838", "tenant_id": 2, "collected_by": 1}, {"id": 10, "sample_id": "S00010", "patient_id": 9, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-06-14", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-06-14T17:15:25.316838", "updated_at": "2025-06-14T17:15:25.316838", "tenant_id": 2, "collected_by": 3}, {"id": 11, "sample_id": "S00011", "patient_id": 18, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-06", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-06-06T17:15:25.316838", "updated_at": "2025-06-06T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 12, "sample_id": "S00012", "patient_id": 24, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-25", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-05-25T17:15:25.316838", "updated_at": "2025-05-25T17:15:25.316838", "tenant_id": 2, "collected_by": 1}, {"id": 13, "sample_id": "S00013", "patient_id": 22, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-06-11", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-06-11T17:15:25.316838", "updated_at": "2025-06-11T17:15:25.316838", "tenant_id": 1, "collected_by": 1}, {"id": 14, "sample_id": "S00014", "patient_id": 21, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-25", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-05-25T17:15:25.316838", "updated_at": "2025-05-25T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 15, "sample_id": "S00015", "patient_id": 11, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-06-08", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-06-08T17:15:25.316838", "updated_at": "2025-06-08T17:15:25.316838", "tenant_id": 3, "collected_by": 3}, {"id": 16, "sample_id": "S00016", "patient_id": 17, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-11", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-06-11T17:15:25.316838", "updated_at": "2025-06-11T17:15:25.316838", "tenant_id": 3, "collected_by": 2}, {"id": 17, "sample_id": "S00017", "patient_id": 12, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-06-09", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-06-09T17:15:25.316838", "updated_at": "2025-06-09T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 18, "sample_id": "S00018", "patient_id": 19, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-02", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-06-02T17:15:25.316838", "updated_at": "2025-06-02T17:15:25.316838", "tenant_id": 3, "collected_by": 2}, {"id": 19, "sample_id": "S00019", "patient_id": 34, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-06-18", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-06-18T17:15:25.316838", "updated_at": "2025-06-18T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 20, "sample_id": "S00020", "patient_id": 33, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-23", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-05-23T17:15:25.316838", "updated_at": "2025-05-23T17:15:25.316838", "tenant_id": 3, "collected_by": 2}, {"id": 21, "sample_id": "S00021", "patient_id": 32, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-26", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-05-26T17:15:25.316838", "updated_at": "2025-05-26T17:15:25.316838", "tenant_id": 3, "collected_by": 2}, {"id": 22, "sample_id": "S00022", "patient_id": 11, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-28", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-05-28T17:15:25.316838", "updated_at": "2025-05-28T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 23, "sample_id": "S00023", "patient_id": 35, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-21", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-05-21T17:15:25.316838", "updated_at": "2025-05-21T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 24, "sample_id": "S00024", "patient_id": 41, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-06-12", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-06-12T17:15:25.316838", "updated_at": "2025-06-12T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 25, "sample_id": "S00025", "patient_id": 42, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-06-11", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-06-11T17:15:25.316838", "updated_at": "2025-06-11T17:15:25.316838", "tenant_id": 3, "collected_by": 3}, {"id": 26, "sample_id": "S00026", "patient_id": 7, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-30", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-05-30T17:15:25.316838", "updated_at": "2025-05-30T17:15:25.316838", "tenant_id": 3, "collected_by": 1}, {"id": 27, "sample_id": "S00027", "patient_id": 11, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-23", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-05-23T17:15:25.316838", "updated_at": "2025-05-23T17:15:25.316838", "tenant_id": 2, "collected_by": 1}, {"id": 28, "sample_id": "S00028", "patient_id": 24, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-06-06", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-06-06T17:15:25.316838", "updated_at": "2025-06-06T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 29, "sample_id": "S00029", "patient_id": 4, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-26", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-05-26T17:15:25.316838", "updated_at": "2025-05-26T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 30, "sample_id": "S00030", "patient_id": 49, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-21", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-05-21T17:15:25.316838", "updated_at": "2025-05-21T17:15:25.316838", "tenant_id": 2, "collected_by": 3}, {"id": 31, "sample_id": "S00031", "patient_id": 34, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-06-18", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-06-18T17:15:25.316838", "updated_at": "2025-06-18T17:15:25.316838", "tenant_id": 1, "collected_by": 1}, {"id": 32, "sample_id": "S00032", "patient_id": 35, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-06-10", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-06-10T17:15:25.316838", "updated_at": "2025-06-10T17:15:25.316838", "tenant_id": 2, "collected_by": 1}, {"id": 33, "sample_id": "S00033", "patient_id": 33, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-06-15", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-06-15T17:15:25.316838", "updated_at": "2025-06-15T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 34, "sample_id": "S00034", "patient_id": 34, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-06-02", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-06-02T17:15:25.316838", "updated_at": "2025-06-02T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 35, "sample_id": "S00035", "patient_id": 43, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-28", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-05-28T17:15:25.316838", "updated_at": "2025-05-28T17:15:25.316838", "tenant_id": 2, "collected_by": 2}, {"id": 36, "sample_id": "S00036", "patient_id": 41, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-29", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-05-29T17:15:25.316838", "updated_at": "2025-05-29T17:15:25.316838", "tenant_id": 2, "collected_by": 1}, {"id": 37, "sample_id": "S00037", "patient_id": 20, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-21", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-05-21T17:15:25.316838", "updated_at": "2025-05-21T17:15:25.316838", "tenant_id": 3, "collected_by": 1}, {"id": 38, "sample_id": "S00038", "patient_id": 24, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-06-16", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-06-16T17:15:25.316838", "updated_at": "2025-06-16T17:15:25.316838", "tenant_id": 3, "collected_by": 3}, {"id": 39, "sample_id": "S00039", "patient_id": 34, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-06-15", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-06-15T17:15:25.316838", "updated_at": "2025-06-15T17:15:25.316838", "tenant_id": 2, "collected_by": 2}, {"id": 40, "sample_id": "S00040", "patient_id": 41, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-21", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-05-21T17:15:25.316838", "updated_at": "2025-05-21T17:15:25.316838", "tenant_id": 2, "collected_by": 1}, {"id": 41, "sample_id": "S00041", "patient_id": 22, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-06-01", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-06-01T17:15:25.316838", "updated_at": "2025-06-01T17:15:25.316838", "tenant_id": 2, "collected_by": 2}, {"id": 42, "sample_id": "S00042", "patient_id": 27, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-06-17", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-06-17T17:15:25.316838", "updated_at": "2025-06-17T17:15:25.316838", "tenant_id": 3, "collected_by": 3}, {"id": 43, "sample_id": "S00043", "patient_id": 13, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-11", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-06-11T17:15:25.316838", "updated_at": "2025-06-11T17:15:25.316838", "tenant_id": 3, "collected_by": 2}, {"id": 44, "sample_id": "S00044", "patient_id": 44, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-28", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-05-28T17:15:25.316838", "updated_at": "2025-05-28T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 45, "sample_id": "S00045", "patient_id": 7, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-06-04", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-06-04T17:15:25.316838", "updated_at": "2025-06-04T17:15:25.316838", "tenant_id": 3, "collected_by": 2}, {"id": 46, "sample_id": "S00046", "patient_id": 18, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-06-15", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-06-15T17:15:25.316838", "updated_at": "2025-06-15T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 47, "sample_id": "S00047", "patient_id": 39, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-26", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-05-26T17:15:25.316838", "updated_at": "2025-05-26T17:15:25.316838", "tenant_id": 2, "collected_by": 3}, {"id": 48, "sample_id": "S00048", "patient_id": 2, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-12", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-06-12T17:15:25.316838", "updated_at": "2025-06-12T17:15:25.316838", "tenant_id": 3, "collected_by": 1}, {"id": 49, "sample_id": "S00049", "patient_id": 28, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-06-17", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-06-17T17:15:25.316838", "updated_at": "2025-06-17T17:15:25.316838", "tenant_id": 3, "collected_by": 3}, {"id": 50, "sample_id": "S00050", "patient_id": 25, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-06-03", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-06-03T17:15:25.316838", "updated_at": "2025-06-03T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 51, "sample_id": "S00051", "patient_id": 29, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-03", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-06-03T17:15:25.316838", "updated_at": "2025-06-03T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 52, "sample_id": "S00052", "patient_id": 27, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-16", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-06-16T17:15:25.316838", "updated_at": "2025-06-16T17:15:25.316838", "tenant_id": 1, "collected_by": 1}, {"id": 53, "sample_id": "S00053", "patient_id": 35, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-06-15", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-06-15T17:15:25.316838", "updated_at": "2025-06-15T17:15:25.316838", "tenant_id": 1, "collected_by": 1}, {"id": 54, "sample_id": "S00054", "patient_id": 19, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-06-13", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-06-13T17:15:25.316838", "updated_at": "2025-06-13T17:15:25.316838", "tenant_id": 1, "collected_by": 1}, {"id": 55, "sample_id": "S00055", "patient_id": 24, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-24", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-05-24T17:15:25.316838", "updated_at": "2025-05-24T17:15:25.316838", "tenant_id": 3, "collected_by": 3}, {"id": 56, "sample_id": "S00056", "patient_id": 22, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-27", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-05-27T17:15:25.316838", "updated_at": "2025-05-27T17:15:25.316838", "tenant_id": 3, "collected_by": 2}, {"id": 57, "sample_id": "S00057", "patient_id": 15, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-21", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-05-21T17:15:25.316838", "updated_at": "2025-05-21T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 58, "sample_id": "S00058", "patient_id": 3, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-06-15", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-06-15T17:15:25.316838", "updated_at": "2025-06-15T17:15:25.316838", "tenant_id": 3, "collected_by": 3}, {"id": 59, "sample_id": "S00059", "patient_id": 13, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-06-05", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-06-05T17:15:25.316838", "updated_at": "2025-06-05T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 60, "sample_id": "S00060", "patient_id": 36, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-06-01", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-06-01T17:15:25.316838", "updated_at": "2025-06-01T17:15:25.316838", "tenant_id": 1, "collected_by": 1}, {"id": 61, "sample_id": "S00061", "patient_id": 39, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-21", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-05-21T17:15:25.316838", "updated_at": "2025-05-21T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 62, "sample_id": "S00062", "patient_id": 2, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-29", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-05-29T17:15:25.316838", "updated_at": "2025-05-29T17:15:25.316838", "tenant_id": 2, "collected_by": 1}, {"id": 63, "sample_id": "S00063", "patient_id": 47, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-15", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-06-15T17:15:25.316838", "updated_at": "2025-06-15T17:15:25.316838", "tenant_id": 3, "collected_by": 2}, {"id": 64, "sample_id": "S00064", "patient_id": 21, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-06-19", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-06-19T17:15:25.316838", "updated_at": "2025-06-19T17:15:25.316838", "tenant_id": 2, "collected_by": 2}, {"id": 65, "sample_id": "S00065", "patient_id": 48, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-28", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-05-28T17:15:25.316838", "updated_at": "2025-05-28T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 66, "sample_id": "S00066", "patient_id": 6, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-06-03", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-06-03T17:15:25.316838", "updated_at": "2025-06-03T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 67, "sample_id": "S00067", "patient_id": 46, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-31", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-05-31T17:15:25.316838", "updated_at": "2025-05-31T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 68, "sample_id": "S00068", "patient_id": 24, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-06", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-06-06T17:15:25.316838", "updated_at": "2025-06-06T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 69, "sample_id": "S00069", "patient_id": 28, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-06-14", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-06-14T17:15:25.316838", "updated_at": "2025-06-14T17:15:25.316838", "tenant_id": 3, "collected_by": 1}, {"id": 70, "sample_id": "S00070", "patient_id": 47, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-09", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-06-09T17:15:25.316838", "updated_at": "2025-06-09T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 71, "sample_id": "S00071", "patient_id": 29, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-06-13", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-06-13T17:15:25.316838", "updated_at": "2025-06-13T17:15:25.316838", "tenant_id": 2, "collected_by": 3}, {"id": 72, "sample_id": "S00072", "patient_id": 33, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-02", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-06-02T17:15:25.316838", "updated_at": "2025-06-02T17:15:25.316838", "tenant_id": 3, "collected_by": 1}, {"id": 73, "sample_id": "S00073", "patient_id": 5, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-25", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-05-25T17:15:25.316838", "updated_at": "2025-05-25T17:15:25.316838", "tenant_id": 3, "collected_by": 2}, {"id": 74, "sample_id": "S00074", "patient_id": 20, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-24", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-05-24T17:15:25.316838", "updated_at": "2025-05-24T17:15:25.316838", "tenant_id": 2, "collected_by": 1}, {"id": 75, "sample_id": "S00075", "patient_id": 27, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-25", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-05-25T17:15:25.316838", "updated_at": "2025-05-25T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 76, "sample_id": "S00076", "patient_id": 21, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-05-25", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-05-25T17:15:25.316838", "updated_at": "2025-05-25T17:15:25.316838", "tenant_id": 3, "collected_by": 1}, {"id": 77, "sample_id": "S00077", "patient_id": 44, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-06-02", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-06-02T17:15:25.316838", "updated_at": "2025-06-02T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 78, "sample_id": "S00078", "patient_id": 36, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-05-22", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-05-22T17:15:25.316838", "updated_at": "2025-05-22T17:15:25.316838", "tenant_id": 2, "collected_by": 2}, {"id": 79, "sample_id": "S00079", "patient_id": 40, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-06-16", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-06-16T17:15:25.316838", "updated_at": "2025-06-16T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 80, "sample_id": "S00080", "patient_id": 22, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-03", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-06-03T17:15:25.316838", "updated_at": "2025-06-03T17:15:25.316838", "tenant_id": 3, "collected_by": 1}, {"id": 81, "sample_id": "S00081", "patient_id": 49, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-06-13", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-06-13T17:15:25.316838", "updated_at": "2025-06-13T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 82, "sample_id": "S00082", "patient_id": 10, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-06-06", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-06-06T17:15:25.316838", "updated_at": "2025-06-06T17:15:25.316838", "tenant_id": 1, "collected_by": 1}, {"id": 83, "sample_id": "S00083", "patient_id": 44, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-06-13", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-06-13T17:15:25.316838", "updated_at": "2025-06-13T17:15:25.316838", "tenant_id": 3, "collected_by": 2}, {"id": 84, "sample_id": "S00084", "patient_id": 4, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-06-12", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-06-12T17:15:25.316838", "updated_at": "2025-06-12T17:15:25.316838", "tenant_id": 2, "collected_by": 2}, {"id": 85, "sample_id": "S00085", "patient_id": 50, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-06-13", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-06-13T17:15:25.316838", "updated_at": "2025-06-13T17:15:25.316838", "tenant_id": 1, "collected_by": 1}, {"id": 86, "sample_id": "S00086", "patient_id": 29, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-06-09", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-06-09T17:15:25.316838", "updated_at": "2025-06-09T17:15:25.316838", "tenant_id": 3, "collected_by": 2}, {"id": 87, "sample_id": "S00087", "patient_id": 1, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-06-07", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-06-07T17:15:25.316838", "updated_at": "2025-06-07T17:15:25.316838", "tenant_id": 3, "collected_by": 1}, {"id": 88, "sample_id": "S00088", "patient_id": 16, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-06-16", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-06-16T17:15:25.316838", "updated_at": "2025-06-16T17:15:25.316838", "tenant_id": 2, "collected_by": 2}, {"id": 89, "sample_id": "S00089", "patient_id": 40, "sample_type_id": 5, "sample_type": "Stool", "container_id": 7, "collection_date": "2025-05-21", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-05-21T17:15:25.316838", "updated_at": "2025-05-21T17:15:25.316838", "tenant_id": 2, "collected_by": 3}, {"id": 90, "sample_id": "S00090", "patient_id": 10, "sample_type_id": 1, "sample_type": "Blood", "container_id": 2, "collection_date": "2025-05-23", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-05-23T17:15:25.316838", "updated_at": "2025-05-23T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 91, "sample_id": "S00091", "patient_id": 48, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-26", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-05-26T17:15:25.316838", "updated_at": "2025-05-26T17:15:25.316838", "tenant_id": 1, "collected_by": 1}, {"id": 92, "sample_id": "S00092", "patient_id": 10, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-06-07", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-06-07T17:15:25.316838", "updated_at": "2025-06-07T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 93, "sample_id": "S00093", "patient_id": 5, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-06-13", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-06-13T17:15:25.316838", "updated_at": "2025-06-13T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 94, "sample_id": "S00094", "patient_id": 27, "sample_type_id": 3, "sample_type": "Plasma", "container_id": 3, "collection_date": "2025-06-20", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-06-20T17:15:25.316838", "updated_at": "2025-06-20T17:15:25.316838", "tenant_id": 2, "collected_by": 3}, {"id": 95, "sample_id": "S00095", "patient_id": 25, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-06-10", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-06-10T17:15:25.316838", "updated_at": "2025-06-10T17:15:25.316838", "tenant_id": 1, "collected_by": 3}, {"id": 96, "sample_id": "S00096", "patient_id": 12, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-06-14", "collection_time": "17:15:25", "status": "Processed", "created_at": "2025-06-14T17:15:25.316838", "updated_at": "2025-06-14T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 97, "sample_id": "S00097", "patient_id": 10, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-25", "collection_time": "17:15:25", "status": "Received", "created_at": "2025-05-25T17:15:25.316838", "updated_at": "2025-05-25T17:15:25.316838", "tenant_id": 1, "collected_by": 2}, {"id": 98, "sample_id": "S00098", "patient_id": 4, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-06-09", "collection_time": "17:15:25", "status": "Collected", "created_at": "2025-06-09T17:15:25.316838", "updated_at": "2025-06-09T17:15:25.316838", "tenant_id": 3, "collected_by": 2}, {"id": 99, "sample_id": "S00099", "patient_id": 26, "sample_type_id": 4, "sample_type": "<PERSON><PERSON>", "container_id": 6, "collection_date": "2025-06-10", "collection_time": "17:15:25", "status": "Completed", "created_at": "2025-06-10T17:15:25.316838", "updated_at": "2025-06-10T17:15:25.316838", "tenant_id": 3, "collected_by": 3}, {"id": 100, "sample_id": "S00100", "patient_id": 23, "sample_type_id": 2, "sample_type": "Serum", "container_id": 1, "collection_date": "2025-05-29", "collection_time": "17:15:25", "status": "In Transit", "created_at": "2025-05-29T17:15:25.316838", "updated_at": "2025-05-29T17:15:25.316838", "tenant_id": 1, "collected_by": 2}]