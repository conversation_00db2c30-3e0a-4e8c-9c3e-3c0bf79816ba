[{"id": 1, "main": "LAB", "code": "HA", "sub_name": "HAEMATOLOGY", "service_time": 0, "room": "0", "order": 1, "dept_amt": 0, "short": "HAEM", "collect": "", "process_receive": "", "receive": "", "no": "", "pending": "", "dept": "", "barcode": "", "appt": "", "is_active": true, "created_at": "2024-01-15T08:00:00", "updated_at": "2024-01-15T08:00:00", "created_by": 1}, {"id": 2, "main": "LAB", "code": "BC", "sub_name": "BIOCHEMISTRY", "service_time": 0, "room": "0", "order": 2, "dept_amt": 0, "short": "BIOC", "collect": "", "process_receive": "", "receive": "", "no": "", "pending": "", "dept": "", "barcode": "", "appt": "", "is_active": true, "created_at": "2024-01-15T08:05:00", "updated_at": "2024-01-15T08:05:00", "created_by": 1}, {"id": 3, "main": "LAB", "code": "MI", "sub_name": "MICROBIOLOGY", "service_time": 0, "room": "0", "order": 3, "dept_amt": 0, "short": "MICRO", "collect": "", "process_receive": "", "receive": "", "no": "", "pending": "", "dept": "", "barcode": "", "appt": "", "is_active": true, "created_at": "2024-01-15T08:10:00", "updated_at": "2024-01-15T08:10:00", "created_by": 1}]