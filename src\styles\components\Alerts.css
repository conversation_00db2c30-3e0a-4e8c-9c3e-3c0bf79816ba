/* Alerts Component Styles */

.custom-alert {
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.custom-alert.alert-primary {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
}

.custom-alert.alert-success {
  background-color: rgba(var(--success-rgb), 0.1);
  color: var(--success);
}

.custom-alert.alert-info {
  background-color: rgba(var(--info-rgb), 0.1);
  color: var(--info);
}

.custom-alert.alert-warning {
  background-color: rgba(var(--warning-rgb), 0.1);
  color: var(--warning);
}

.custom-alert.alert-danger {
  background-color: rgba(var(--danger-rgb), 0.1);
  color: var(--danger);
}

.custom-alert.alert-secondary {
  background-color: rgba(var(--secondary-rgb), 0.1);
  color: var(--secondary);
}

.custom-alert .btn-close {
  padding: 0.5rem;
  margin: -0.5rem -0.5rem -0.5rem auto;
}

/* Toast Notification Styles */
.toast-notification {
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  min-width: 300px;
}

.toast-notification .toast-header {
  border-bottom: none;
  padding: 0.75rem 1rem;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.toast-notification .toast-body {
  padding: 0.75rem 1rem;
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

.toast-notification.bg-primary .toast-header {
  background-color: var(--primary);
  color: white;
}

.toast-notification.bg-success .toast-header {
  background-color: var(--success);
  color: white;
}

.toast-notification.bg-info .toast-header {
  background-color: var(--info);
  color: white;
}

.toast-notification.bg-warning .toast-header {
  background-color: var(--warning);
  color: white;
}

.toast-notification.bg-danger .toast-header {
  background-color: var(--danger);
  color: white;
}

.toast-notification.bg-secondary .toast-header {
  background-color: var(--secondary);
  color: white;
}

.toast-notification .btn-close {
  filter: brightness(0) invert(1);
}

/* Toast Container Styles */
.toast-container {
  z-index: 1060;
}

/* Snackbar Alert Styles */
.snackbar-alert {
  position: fixed;
  z-index: 1070;
  min-width: 300px;
  max-width: 500px;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  animation: snackbar-fade-in 0.3s ease-in-out;
}

.snackbar-content {
  display: flex;
  align-items: center;
}

.snackbar-icon {
  margin-right: 0.75rem;
}

.snackbar-message {
  flex: 1;
}

.snackbar-action {
  margin-left: 0.75rem;
  padding: 0;
  color: inherit;
  text-decoration: underline;
  font-weight: 600;
}

.snackbar-close {
  margin-left: 0.5rem;
  padding: 0;
  color: inherit;
  opacity: 0.7;
}

.snackbar-close:hover {
  opacity: 1;
  color: inherit;
}

/* Snackbar Positions */
.snackbar-top-left {
  top: 1rem;
  left: 1rem;
}

.snackbar-top-center {
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
}

.snackbar-top-right {
  top: 1rem;
  right: 1rem;
}

.snackbar-bottom-left {
  bottom: 1rem;
  left: 1rem;
}

.snackbar-bottom-center {
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
}

.snackbar-bottom-right {
  bottom: 1rem;
  right: 1rem;
}

/* Snackbar Variants */
.snackbar-primary {
  background-color: var(--primary);
  color: white;
}

.snackbar-success {
  background-color: var(--success);
  color: white;
}

.snackbar-info {
  background-color: var(--info);
  color: white;
}

.snackbar-warning {
  background-color: var(--warning);
  color: white;
}

.snackbar-danger {
  background-color: var(--danger);
  color: white;
}

.snackbar-secondary {
  background-color: var(--secondary);
  color: white;
}

/* Snackbar Animation */
@keyframes snackbar-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Banner Alert Styles */
.banner-alert {
  width: 100%;
  padding: 1rem;
  color: white;
  z-index: 1050;
}

.banner-sticky {
  position: sticky;
  top: 0;
}

.banner-content {
  display: flex;
  align-items: center;
}

.banner-icon {
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-text {
  flex: 1;
}

.banner-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.banner-actions {
  display: flex;
  align-items: center;
  margin-left: 1rem;
}

.banner-close {
  color: white;
  opacity: 0.7;
  padding: 0;
  margin-left: 0.5rem;
}

.banner-close:hover {
  opacity: 1;
  color: white;
}

/* Banner Variants */
.banner-primary {
  background-color: var(--primary);
}

.banner-success {
  background-color: var(--success);
}

.banner-info {
  background-color: var(--info);
}

.banner-warning {
  background-color: var(--warning);
}

.banner-danger {
  background-color: var(--danger);
}

.banner-secondary {
  background-color: var(--secondary);
}

/* Responsive Styles */
@media (max-width: 767.98px) {
  .custom-alert {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .toast-notification {
    min-width: 250px;
  }

  .toast-notification .toast-header {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .toast-notification .toast-body {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .snackbar-alert {
    min-width: 250px;
    max-width: 90%;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .banner-alert {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .banner-content {
    flex-wrap: wrap;
  }

  .banner-actions {
    margin-left: 0;
    margin-top: 0.5rem;
    width: 100%;
    justify-content: flex-end;
  }
}
