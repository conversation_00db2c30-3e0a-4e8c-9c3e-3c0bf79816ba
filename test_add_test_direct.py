#!/usr/bin/env python3
"""
Direct test of the add_test_to_billing functionality without Flask server
"""
import sys
import os
import json
from datetime import datetime

# Add backend directory to path
sys.path.append('backend')

def test_add_test_direct():
    """Test the add-test functionality directly"""
    
    try:
        # Import required modules
        from utils import read_data, write_data
        
        print("🔧 Testing add-test functionality directly...")
        
        # Test data
        billing_id = 108
        new_tests = [
            {
                'name': 'Complete Blood Count (CBC)',
                'amount': 250,
                'test_id': 1
            }
        ]
        
        print(f"🔧 Loading billings data for ID {billing_id}...")
        billings = read_data('billings.json')
        print(f"🔧 Loaded {len(billings)} billing records")
        
        # Find billing by ID
        billing_index = next((i for i, b in enumerate(billings) if b['id'] == billing_id), None)
        if billing_index is None:
            print(f"❌ Billing ID {billing_id} not found")
            return False
            
        print(f"🔧 Found billing at index {billing_index}")
        billing = billings[billing_index]
        
        # Show current state
        current_test_count = len(billing.get('test_items', []))
        print(f"🔧 Current test items: {current_test_count}")
        
        # Add test items
        billing.setdefault('test_items', [])
        billing.setdefault('items', [])
        
        existing_ids = [t.get('id', 0) for t in billing['test_items']]
        next_test_id = max(existing_ids) + 1 if existing_ids else 1
        
        for test in new_tests:
            print(f"🔧 Adding test: {test.get('name', 'Unknown')}")
            
            # Simple test item
            new_test_item = {
                "id": next_test_id,
                "test_name": test.get("name", ""),
                "amount": test.get("amount", 0),
                "status": "Pending",
                "added_at": datetime.now().isoformat()
            }
            billing['test_items'].append(new_test_item)
            
            # Simple item
            billing['items'].append({
                "id": int(datetime.now().timestamp() * 1000) + next_test_id,
                "test_name": test.get("name", ""),
                "amount": test.get("amount", 0),
                "quantity": 1,
                "status": "Pending"
            })
            
            next_test_id += 1
        
        # Update totals with safe conversion
        def safe_float(value, default=0):
            try:
                if value is None or value == '':
                    return default
                return float(value)
            except (ValueError, TypeError):
                return default

        subtotal = sum(safe_float(item.get('amount', 0)) for item in billing['test_items'])
        billing['total_amount'] = subtotal
        billing['updated_at'] = datetime.now().isoformat()
        
        # Update the billing record
        billings[billing_index] = billing
        
        print(f"🔧 New test items count: {len(billing['test_items'])}")
        print(f"🔧 New total amount: {billing['total_amount']}")
        
        # Save to file (create backup first)
        backup_filename = f"billings_backup_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        print(f"🔧 Creating backup: {backup_filename}")
        
        # Create backup
        import shutil
        shutil.copy('backend/data/billings.json', f'backend/data/{backup_filename}')
        
        # Save updated data
        print("🔧 Saving updated billing data...")
        write_data('billings.json', billings)
        
        print("✅ Test addition completed successfully!")
        
        # Verify the change
        print("🔧 Verifying changes...")
        updated_billings = read_data('billings.json')
        updated_billing = next((b for b in updated_billings if b['id'] == billing_id), None)
        
        if updated_billing:
            final_test_count = len(updated_billing.get('test_items', []))
            print(f"✅ Verification successful: {final_test_count} test items")
            return True
        else:
            print("❌ Verification failed: billing not found")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_billing_reports_integration():
    """Test if billing reports are properly updated"""
    
    try:
        from utils import read_data, write_data
        
        print("\n🔧 Testing billing reports integration...")
        
        billing_id = 108
        billing_reports = read_data('billing_reports.json')
        
        # Find corresponding billing report
        report_index = next((i for i, r in enumerate(billing_reports) if r.get('billing_id') == billing_id), None)
        
        if report_index is not None:
            print(f"✅ Found billing report for ID {billing_id}")
            report = billing_reports[report_index]
            test_items_count = len(report.get('test_items', []))
            print(f"🔧 Current report test items: {test_items_count}")
            return True
        else:
            print(f"❌ No billing report found for ID {billing_id}")
            return False
            
    except Exception as e:
        print(f"❌ Error in billing reports test: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Direct Testing of Add Test Functionality")
    print("=" * 50)
    
    # Test 1: Direct add test
    success1 = test_add_test_direct()
    
    # Test 2: Check billing reports integration
    success2 = test_billing_reports_integration()
    
    print("\n" + "=" * 50)
    if success1:
        print("🎉 ADD TEST FUNCTIONALITY WORKING!")
        if success2:
            print("🎉 BILLING REPORTS INTEGRATION VERIFIED!")
        else:
            print("⚠️  Billing reports integration needs attention")
    else:
        print("❌ ADD TEST FUNCTIONALITY FAILED!")
