#!/usr/bin/env python3
"""
Test the fixed billing calculation logic
"""
import sys
import os
import json
from datetime import datetime

# Add backend directory to path
sys.path.append('backend')

def safe_float(value, default=0):
    """Safely convert value to float"""
    try:
        if value is None or value == '':
            return default
        return float(value)
    except (ValueError, TypeError):
        return default

def test_fixed_calculation_logic():
    """Test the new calculation logic on various billing records"""
    
    try:
        from utils import read_data
        
        print("🧪 Testing Fixed Calculation Logic...")
        
        billings = read_data('billings.json')
        
        # Test on multiple billing records
        test_billing_ids = [107, 108, 109, 110]
        
        for billing_id in test_billing_ids:
            billing = next((b for b in billings if b['id'] == billing_id), None)
            if not billing:
                print(f"❌ Billing {billing_id} not found")
                continue
                
            print(f"\n📊 Billing ID {billing_id}:")
            
            # Current stored total
            stored_total = billing.get('total_amount', 0)
            print(f"   Current stored total: {stored_total}")
            
            # Calculate using new logic
            test_items_total = sum(safe_float(item.get('amount', 0)) for item in billing.get('test_items', []))
            items_total = sum(safe_float(item.get('amount', 0)) for item in billing.get('items', []))
            
            # Apply the new logic
            if billing.get('test_items'):
                new_calculated_total = test_items_total
                source = "test_items"
            else:
                new_calculated_total = items_total
                source = "items"
                
            print(f"   test_items count: {len(billing.get('test_items', []))}, total: {test_items_total}")
            print(f"   items count: {len(billing.get('items', []))}, total: {items_total}")
            print(f"   New calculation (from {source}): {new_calculated_total}")
            print(f"   Difference: {stored_total - new_calculated_total}")
            
            if abs(stored_total - new_calculated_total) > 0.01:
                print(f"   ⚠️  Total needs correction")
            else:
                print(f"   ✅ Total is correct")
                
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

def simulate_add_test_with_fixed_logic():
    """Simulate adding a test with the complete fixed logic including migration"""

    try:
        from utils import read_data, write_data

        print(f"\n🔧 Simulating Add Test with Complete Fixed Logic...")

        billing_id = 107  # Test on a billing that uses 'items' array

        billings = read_data('billings.json')
        billing_index = next((i for i, b in enumerate(billings) if b['id'] == billing_id), None)

        if billing_index is None:
            print(f"❌ Billing {billing_id} not found")
            return

        billing = billings[billing_index]

        print(f"   Before migration:")
        print(f"     total_amount: {billing.get('total_amount', 0)}")
        print(f"     test_items count: {len(billing.get('test_items', []))}")
        print(f"     items count: {len(billing.get('items', []))}")

        # Simulate the migration logic
        billing.setdefault('test_items', [])
        billing.setdefault('items', [])

        # Migration logic: if test_items is empty but items exist, migrate items to test_items
        if not billing['test_items'] and billing['items']:
            print(f"   🔧 Migrating {len(billing['items'])} items to test_items structure...")
            for item in billing['items']:
                migrated_item = {
                    "id": item.get('id', 0),
                    "test_name": item.get('test_name', item.get('description', '')),
                    "amount": safe_float(item.get('amount', 0)),
                    "status": item.get('status', 'Pending'),
                    "migrated_from_items": True
                }
                billing['test_items'].append(migrated_item)
            print(f"   ✅ Migration complete: {len(billing['test_items'])} test items")

        # Add new test
        existing_ids = [t.get('id', 0) for t in billing['test_items']]
        next_test_id = max(existing_ids) + 1 if existing_ids else 1

        new_test = {
            "id": next_test_id,
            "test_name": "Test Calculation Fix",
            "amount": 500.0,
            "status": "Pending"
        }
        billing['test_items'].append(new_test)

        # Also add to items for consistency
        new_item = {
            "id": int(datetime.now().timestamp() * 1000) + next_test_id,
            "test_name": "Test Calculation Fix",
            "amount": 500.0,
            "quantity": 1,
            "status": "Pending"
        }
        billing['items'].append(new_item)

        # Apply the new calculation logic
        test_items_total = sum(safe_float(item.get('amount', 0)) for item in billing.get('test_items', []))
        items_total = sum(safe_float(item.get('amount', 0)) for item in billing.get('items', []))

        # Use the new logic
        test_items_count = len(billing.get('test_items', []))
        items_count = len(billing.get('items', []))

        if test_items_count > 0:
            subtotal = test_items_total
            source = "test_items (authoritative)"
        else:
            subtotal = items_total
            source = "items (legacy)"

        billing['total_amount'] = subtotal

        print(f"   After adding test:")
        print(f"     test_items count: {test_items_count}, total: {test_items_total}")
        print(f"     items count: {items_count}, total: {items_total}")
        print(f"     Used total from {source}: {subtotal}")
        print(f"     New total_amount: {billing['total_amount']}")

        # Don't actually save, just simulate
        print(f"   ✅ Complete simulation successful (not saved)")

    except Exception as e:
        print(f"❌ Error in simulation: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_calculation_logic()
    simulate_add_test_with_fixed_logic()
