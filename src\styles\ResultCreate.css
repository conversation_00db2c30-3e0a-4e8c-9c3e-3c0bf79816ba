/* Result Create Styles */

.result-create-container {
  padding: 1.5rem;
}

.test-info-item {
  margin-bottom: 0.75rem;
  display: flex;
  flex-direction: column;
}

.test-info-item strong {
  margin-bottom: 0.25rem;
  color: var(--dark-gray);
}

.test-info-item span {
  color: var(--primary);
  font-weight: 500;
}

/* Responsive Styles */
@media (max-width: 767.98px) {
  .result-create-container {
    padding: 1rem;
  }
}
