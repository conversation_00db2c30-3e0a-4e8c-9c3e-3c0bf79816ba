[{"id": 1, "department": "Immunology", "testName": "1,25 Dihydroxyvitamin D", "emrClassification": "test", "shortName": "1,25 Dihydroxyvitami", "displayName": "1,25 Dihydroxyvitamin D", "hmsCode": "648.0", "internationalCode": "648.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "1,25 Dihydroxyvitamin D", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 3500.0, "test_profile": "1,25 Dihydroxyvitamin D", "reference_range": "19.9-79.3", "result_unit": "pg/ml", "decimals": 1}, {"id": 2, "department": "Immunology", "testName": "17 - HYDROXY PROGESTERONE", "emrClassification": "test", "shortName": "17 - HYDROXY PROGEST", "displayName": "17 - HYDROXY PROGESTERONE", "hmsCode": "2.0", "internationalCode": "2.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "17 - HYDROXY PROGESTERONE", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 800.0, "test_profile": "17 - HYDROXY PROGESTERONE", "reference_range": "New Born (Boys & Girls) 1 month after birth :  0.0 - 17.3  2 month after birth :  0.32 - 13.7 3 month after birth :  0.06 - 4.2 4 month after birth : 0.2 - 4.6  Children & Adolescent 01 to 10  yrs : 0.03 - 2.85 11 to 14 yrs : 0.06 - 1.38 15 to 18 yrs : 0.41 - 2.35  ADULT MALE                :  0.50 - 2.1   FEMALE Follicular phase         : 0.1  - 0.8 Luteal phase             : 0.6  - 2.3  Ovulation                : 0.3  - 1.4 Post ACTH                : < 3.2  3rd Trimester            : 2.0 - 12.0   Postmenopausal women     : 0.13-0.51", "result_unit": "ng/ml", "decimals": 1}, {"id": 3, "department": "Immunology", "testName": "25 Hydroxy Vitamin D3", "emrClassification": "test", "shortName": "25 Hydroxy Vitamin D", "displayName": "25 Hydroxy Vitamin D3", "hmsCode": "630.0", "internationalCode": "630.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "Comments : Vitamin D is a fat-soluble steroid hormone precursor that is mainly produced in the skin by exposure to sunlight. Vitamin D is biologically inert and must undergo two successive hydroxylations in the liver and kidney to become the biologically active  1,25 - dihydroxyvitamin D.  It is commonly agreed that 25-hydroxyvitamin D is the metabolite to determine the overall vitamin D status as it is the major storage form of vitamin D in the human body. This primary circulating form of vitamin D is present human body with levels approximately 1000 fold greater than the circulating 1,25-dihydroxyvitamin D. The half-life of circulating 25-hydroxyvitamin D is 2-3 weeks. \n\nVitamin D is essential for : Bone health. In children, severe deficiency leads to bone-malformation, known as rickets. Milder degrees of insufficiency are believed to cause reduced efficiency in the utilization of dietary calcium. \n\nVitamin D deficiency causes : Muscle weakness in elderly, the risk of falling has been attributed to the effect of vitamin D on muscle function. Vitamin D deficiency is a common cause of secondary hyperparathyroidism. Elevations of PTH levels, especially in elderly vitamin D deficient adults can result in osteomalacia, increased bone turnover, reduced bone mass and risk of bone fractures. Low vitamin D (25-OH) concentrations are also associated with lower bone mineral density.\n\nThe results should always be assessed in conjunction with the patient’s medical history, clinical examination and other findings.", "instructions": "Comments : Vitamin D is a fat-soluble steroid hormone precursor that is mainly produced in the skin by exposure to sunlight. Vitamin D is biologically inert and must undergo two successive hydroxylations in the liver and kidney to become the biologically active  1,25 - dihydroxyvitamin D.  It is commonly agreed that 25-hydroxyvitamin D is the metabolite to determine the overall vitamin D status as it is the major storage form of vitamin D in the human body. This primary circulating form of vitamin D is present human body with levels approximately 1000 fold greater than the circulating 1,25-dihydroxyvitamin D. The half-life of circulating 25-hydroxyvitamin D is 2-3 weeks. \n\nVitamin D is essential for : Bone health. In children, severe deficiency leads to bone-malformation, known as rickets. Milder degrees of insufficiency are believed to cause reduced efficiency in the utilization of dietary calcium. \n\nVitamin D deficiency causes : Muscle weakness in elderly, the risk of falling has been attributed to the effect of vitamin D on muscle function. Vitamin D deficiency is a common cause of secondary hyperparathyroidism. Elevations of PTH levels, especially in elderly vitamin D deficient adults can result in osteomalacia, increased bone turnover, reduced bone mass and risk of bone fractures. Low vitamin D (25-OH) concentrations are also associated with lower bone mineral density.\n\nThe results should always be assessed in conjunction with the patient’s medical history, clinical examination and other findings.", "specialReport": "", "reportName": "25 Hydroxy Vitamin D3", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1200.0, "test_profile": "25 Hydroxy Vitamin D3", "reference_range": "< 20      : Deficiency\n20-29     : Insufficiency\n30-100    : Sufficiency\n> 100     : Toxicity", "result_unit": "ng/ml", "decimals": 1}, {"id": 4, "department": "Immunology", "testName": "5 Alpha, DHT - Di hydro testosterone", "emrClassification": "test", "shortName": "5 Alpha, DHT - Di hy", "displayName": "5 Alpha, DHT - Di hydro testosterone", "hmsCode": "653.0", "internationalCode": "653.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "5 Alpha, DHT - Di hydro testosterone", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1700.0, "test_profile": "5 Alpha, DHT - Di hydro testosterone", "reference_range": "Adult: Male  : 250 - 990 Female: Premenopausal : 24 - 368 Postmenopausal: 10 - 181", "result_unit": "pg/ml", "decimals": 0}, {"id": 5, "department": "Immunology", "testName": "5-HIAA (Hydroxy Indole Acetic Acid), Urine", "emrClassification": "test", "shortName": "5-HIAA (Hydroxy Indo", "displayName": "5-HIAA (Hydroxy Indole Acetic Acid), Urine", "hmsCode": "13.0", "internationalCode": "13.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "5-HIAA (Hydroxy Indole Acetic Acid), Urine", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 4000.0, "test_profile": "5-HIAA (Hydroxy Indole Acetic Acid), Urine", "reference_range": "2 TO 10 Years : Less than 12 >10 Years     : Less than 10", "result_unit": "mg/g creatinine", "decimals": 2}, {"id": 6, "department": "Immunology", "testName": "5-HIAA (Hydroxy Indole Acetic Acid), Urine 24H", "emrClassification": "test", "shortName": "5-HIAA (Hydroxy Indo", "displayName": "5-HIAA (Hydroxy Indole Acetic Acid), Urine 24H", "hmsCode": "682.0", "internationalCode": "682.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "5-HIAA (Hydroxy Indole Acetic Acid), Urine 24H", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 4000.0, "test_profile": "5-HIAA (Hydroxy Indole Acetic Acid), Urine 24H", "reference_range": "2.00 - 8.00", "result_unit": "mg/24 hrs", "decimals": 1}, {"id": 7, "department": "Immunology", "testName": "ACTH (Adreno Corticotropic Hormone)", "emrClassification": "test", "shortName": "ACTH (Adreno Cortico", "displayName": "ACTH (Adreno Corticotropic Hormone)", "hmsCode": "17.0", "internationalCode": "17.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "ACTH (Adreno Corticotropic Hormone)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1200.0, "test_profile": "ACTH (Adreno Corticotropic Hormone)", "reference_range": "Cord Blood : 50 - 570 New Born   : 10 - 85 Adults     : Upto 46", "result_unit": "pg/ml", "decimals": 1}, {"id": 8, "department": "Immunology", "testName": "Activated Protein C Resisitance", "emrClassification": "test", "shortName": "Activated Protein C ", "displayName": "Activated Protein C Resisitance", "hmsCode": "1429.0", "internationalCode": "1429.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Activated Protein C Resisitance", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 3900.0, "test_profile": "Activated Protein C Resisitance", "reference_range": ">119.99", "result_unit": "seconds", "decimals": 0}, {"id": 9, "department": "Immunology", "testName": "ALDOSTERONE", "emrClassification": "test", "shortName": "ALDOSTERONE", "displayName": "ALDOSTERONE", "hmsCode": "507.0", "internationalCode": "507.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "ALDOSTERONE", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2000.0, "test_profile": "ALDOSTERONE", "reference_range": "Posture          |  Reference Range  ----------------------------------- Upright (serum)  |   2.52 - 39.2 Supine (serum)   |   1.76 - 23.2 Upright (plasma) |   2.21 - 35.3 Supine (plasma)  |   1.17 - 23.6", "result_unit": "ng/dl", "decimals": 2}, {"id": 10, "department": "Immunology", "testName": "ALPHA 1. ANTITRIPSIN", "emrClassification": "test", "shortName": "ALPHA 1. ANTITRIPSIN", "displayName": "ALPHA 1. ANTITRIPSIN", "hmsCode": "508.0", "internationalCode": "508.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "ALPHA 1. ANTITRIPSIN", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1500.0, "test_profile": "ALPHA 1. ANTITRIPSIN", "reference_range": "90.00 - 200.00", "result_unit": "mg/dl", "decimals": 1}, {"id": 11, "department": "Immunology", "testName": "Alpha Fetoprotein (AFP)", "emrClassification": "test", "shortName": "Alpha Fetoprotein (A", "displayName": "Alpha Fetoprotein (AFP)", "hmsCode": "505.0", "internationalCode": "505.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Alpha Fetoprotein (AFP)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 950.0, "test_profile": "Alpha Fetoprotein (AFP)", "reference_range": "Fetal  First Trimester : 200 - 400 Cord blood      : <5 Child 1 year    : <30 Adult           : <15 Tumor marker Early marker    : 10-20 Cancer          : >1000", "result_unit": "ng/ml", "decimals": 2}, {"id": 12, "department": "Immunology", "testName": "ANDROSTENEDIONE (A4)", "emrClassification": "test", "shortName": "ANDROSTENEDIONE (A4)", "displayName": "ANDROSTENEDIONE (A4)", "hmsCode": "512.0", "internationalCode": "512.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "ANDROSTENEDIONE (A4)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "ANDROSTENEDIONE (A4)", "reference_range": "MALES   : 0.7-3.6 FEMALES : 0.3-3.5", "result_unit": "ng/ml", "decimals": 2}, {"id": 13, "department": "Immunology", "testName": "<PERSON> Muller<PERSON> (AMH)", "emrClassification": "test", "shortName": "<PERSON> Muller<PERSON>", "displayName": "<PERSON> Muller<PERSON> (AMH)", "hmsCode": "1305.0", "internationalCode": "1305.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "<PERSON> Muller<PERSON> (AMH)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1500.0, "test_profile": "<PERSON> Muller<PERSON> (AMH)", "reference_range": "Healthy Men: 1.43 - 11.6   Healthy Women 20 - 24 Yrs: 1.66 - 9.49  25 - 29 Yrs: 1.18 - 9.16   30 - 34 Yrs: 0.672 - 7.55   35 - 39 Yrs: 0.777 - 5.24   40 - 44 Yrs: 0.097 - 2.96   45 - 50 Yrs: 0.046 - 2.06    PCOS Women : 2.41 - 17.1", "result_unit": "ng/ml", "decimals": 2}, {"id": 14, "department": "Immunology", "testName": "ANTI THROMBIN III activity", "emrClassification": "test", "shortName": "ANTI THROMBIN III ac", "displayName": "ANTI THROMBIN III activity", "hmsCode": "516.0", "internationalCode": "516.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "ANTI THROMBIN III activity", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1500.0, "test_profile": "ANTI THROMBIN III activity", "reference_range": "Day 1 : 58 - 90 Day 3 : 60 - 89 Upto 1 Year : 72 - 134 1 - 5 Years : 101 - 131  6 - 10 Years : 95 - 134 11 - 16 Years : 96 - 126 Adult : 80 - 120", "result_unit": "%", "decimals": 1}, {"id": 15, "department": "Immunology", "testName": "ANTI TPO", "emrClassification": "test", "shortName": "ANTI TPO", "displayName": "ANTI TPO", "hmsCode": "519.0", "internationalCode": "519.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "ANTI TPO", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 0.0, "test_profile": "ANTI TPO", "reference_range": "UP TO 35", "result_unit": "IU/ml", "decimals": 1}, {"id": 16, "department": "Immunology", "testName": "BETA hCG", "emrClassification": "test", "shortName": "BETA hCG", "displayName": "BETA hCG", "hmsCode": "522.0", "internationalCode": "522.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "BETA hCG", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 600.0, "test_profile": "BETA hCG", "reference_range": "MALE  : 0 - 10 \nFEMALE: Non-pregnant 0 - 10 \n      : Pregnancy 1week : 10 - 30\n      : 2nd week : 30 - 100\n      : 3rd week : 100 - 1000 \n      : 4th week : 1000 - 10000\n      : 2nd-3rd months:30000-100000\n      : 2nd trimester : 10000 - 30000\n      : 3rd trimester : 5000 - 15000", "result_unit": "mIU/ml", "decimals": 2}, {"id": 17, "department": "Immunology", "testName": "Beta-2-microglobulin", "emrClassification": "test", "shortName": "Beta-2-microglobulin", "displayName": "Beta-2-microglobulin", "hmsCode": "641.0", "internationalCode": "641.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Beta-2-microglobulin", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1500.0, "test_profile": "Beta-2-microglobulin", "reference_range": "609-2366", "result_unit": "ng/ml", "decimals": 0}, {"id": 18, "department": "Immunology", "testName": "C-peptide, Fasting", "emrClassification": "test", "shortName": "C-peptide, Fasting", "displayName": "C-peptide, Fasting", "hmsCode": "523.0", "internationalCode": "523.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "C-peptide assay is intended for use as an aid in the diagnosis and treatment of patients with abnormal insulin secretion. In normal adults, a molar ration of C-peptide/insulin >5 in the peripheral blood is probably due to the longer half-life of C-peptide (30 minutes) compared to insulin (5-10 minutes). In the diagnosis and management of diabetes mellitus, measurement of serum insulin levels usually provides superior information to that of serum C-peptide.\n\nElevated levels: Chloroquine, danazol, ethinyl estradiol, oral contraceptives, Obesity\nDecreased levels: proinsulin, EDTA and hemolysis.\nIn rare cases, interference due to extremely high titers of antibodies to ruthenium or streptavidin can occur. For diagnostic purposes, the results should always be assessed in conjunction with the patient’s medical history, clinical examination and other findings.", "instructions": "C-peptide assay is intended for use as an aid in the diagnosis and treatment of patients with abnormal insulin secretion. In normal adults, a molar ration of C-peptide/insulin >5 in the peripheral blood is probably due to the longer half-life of C-peptide (30 minutes) compared to insulin (5-10 minutes). In the diagnosis and management of diabetes mellitus, measurement of serum insulin levels usually provides superior information to that of serum C-peptide.\n\nElevated levels: Chloroquine, danazol, ethinyl estradiol, oral contraceptives, Obesity\nDecreased levels: proinsulin, EDTA and hemolysis.\nIn rare cases, interference due to extremely high titers of antibodies to ruthenium or streptavidin can occur. For diagnostic purposes, the results should always be assessed in conjunction with the patient’s medical history, clinical examination and other findings.", "specialReport": "", "reportName": "C-peptide, Fasting", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1100.0, "test_profile": "C-peptide, Fasting", "reference_range": "", "result_unit": "ng/ml", "decimals": 2}, {"id": 19, "department": "Immunology", "testName": "C-peptide, postprandial", "emrClassification": "test", "shortName": "C-peptide, postprand", "displayName": "C-peptide, postprandial", "hmsCode": "524.0", "internationalCode": "524.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "C-peptide assay is intended for use as an aid in the diagnosis and treatment of patients with abnormal insulin secretion. In normal adults, a molar ration of C-peptide/insulin >5 in the peripheral blood is probably due to the longer half-life of C-peptide (30 minutes) compared to insulin (5-10 minutes). In the diagnosis and management of diabetes mellitus, measurement of serum insulin levels usually provides superior information to that of serum C-peptide.\nElevated levels: Chloroquine, danazol, ethinyl estradiol, oral contraceptives, Obesity\nDecreased levels: proinsulin, EDTA and hemolysis.\nIn rare cases, interference due to extremely high titers of antibodies to ruthenium or streptavidin can occur. For diagnostic purposes, the results should always be assessed in conjunction with the patient’s medical history, clinical examination and other findings.", "instructions": "C-peptide assay is intended for use as an aid in the diagnosis and treatment of patients with abnormal insulin secretion. In normal adults, a molar ration of C-peptide/insulin >5 in the peripheral blood is probably due to the longer half-life of C-peptide (30 minutes) compared to insulin (5-10 minutes). In the diagnosis and management of diabetes mellitus, measurement of serum insulin levels usually provides superior information to that of serum C-peptide.\nElevated levels: Chloroquine, danazol, ethinyl estradiol, oral contraceptives, Obesity\nDecreased levels: proinsulin, EDTA and hemolysis.\nIn rare cases, interference due to extremely high titers of antibodies to ruthenium or streptavidin can occur. For diagnostic purposes, the results should always be assessed in conjunction with the patient’s medical history, clinical examination and other findings.", "specialReport": "", "reportName": "C-peptide, postprandial", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1100.0, "test_profile": "C-peptide, postprandial", "reference_range": "Not established.\nStimulation test: 2.73-5.64\n(after 1mg glucagon IV\nstimulation)", "result_unit": "ng/ml", "decimals": 2}, {"id": 20, "department": "Immunology", "testName": "C1 Esterase Inhibitor", "emrClassification": "test", "shortName": "C1 Esterase Inhibito", "displayName": "C1 Esterase Inhibitor", "hmsCode": "658.0", "internationalCode": "658.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "C1 Esterase Inhibitor", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2300.0, "test_profile": "C1 Esterase Inhibitor", "reference_range": "0.21 to 0.39", "result_unit": "g/L", "decimals": 0}, {"id": 21, "department": "Immunology", "testName": "CA 15.3", "emrClassification": "test", "shortName": "CA 15.3", "displayName": "CA 15.3", "hmsCode": "525.0", "internationalCode": "525.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "CA 15.3", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1000.0, "test_profile": "CA 15.3", "reference_range": "<PERSON><PERSON> 30", "result_unit": "U/ml", "decimals": 1}, {"id": 22, "department": "Immunology", "testName": "CA 19 .9", "emrClassification": "test", "shortName": "CA 19 .9", "displayName": "CA 19 .9", "hmsCode": "526.0", "internationalCode": "526.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "Note\n· Test is not recommended to screen Pancreatic cancer in the general population.\n· False negative / positive results are observed in patients receiving mouse monoclonal antibodies for diagnosis or therapy\n· CA 19.9, regardless of level, should not be interpreted as absolute evidence for the presence or absence of malignant disease. The assay value should be\nused in conjunction with findings from clinical evaluation and other diagnostic procedures.\n· Persistently elevated CA 19.9 levels are usually indicative of progressive malignant disease and poor therapeutic response\n· The concentration of CA 19.9 in a given specimen, determined with assays from different manufacturers, may not be comparable.", "instructions": "Note\n· Test is not recommended to screen Pancreatic cancer in the general population.\n· False negative / positive results are observed in patients receiving mouse monoclonal antibodies for diagnosis or therapy\n· CA 19.9, regardless of level, should not be interpreted as absolute evidence for the presence or absence of malignant disease. The assay value should be\nused in conjunction with findings from clinical evaluation and other diagnostic procedures.\n· Persistently elevated CA 19.9 levels are usually indicative of progressive malignant disease and poor therapeutic response\n· The concentration of CA 19.9 in a given specimen, determined with assays from different manufacturers, may not be comparable.", "specialReport": "", "reportName": "CA 19 .9", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1000.0, "test_profile": "CA 19 .9", "reference_range": "Less than 39.0", "result_unit": "U/ml", "decimals": 1}, {"id": 23, "department": "Immunology", "testName": "CA 72.4 ( TAG-72)", "emrClassification": "test", "shortName": "CA 72.4 ( TAG-72)", "displayName": "CA 72.4 ( TAG-72)", "hmsCode": "665.0", "internationalCode": "665.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "CA 72.4 ( TAG-72)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1700.0, "test_profile": "CA 72.4 ( TAG-72)", "reference_range": "UP TO 6.9", "result_unit": "U/mL", "decimals": 2}, {"id": 24, "department": "Immunology", "testName": "CALCITONIN", "emrClassification": "test", "shortName": "CALCITONIN", "displayName": "CALCITONIN", "hmsCode": "527.0", "internationalCode": "527.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "CALCITONIN", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1800.0, "test_profile": "CALCITONIN", "reference_range": "Male   :   Less than 8.4 Female :   Less than 5.0", "result_unit": "pg/ml", "decimals": 1}, {"id": 25, "department": "Immunology", "testName": "Cancer Antigen 125 (CA-125)", "emrClassification": "test", "shortName": "Cancer Antigen 125 (", "displayName": "Cancer Antigen 125 (CA-125)", "hmsCode": "633.0", "internationalCode": "633.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Cancer Antigen 125 (CA-125)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "Cancer Antigen 125 (CA-125)", "reference_range": "<35", "result_unit": "U/mL", "decimals": 2}, {"id": 26, "department": "Immunology", "testName": "CARBAMAZEPINE", "emrClassification": "test", "shortName": "CARBAMAZEPINE", "displayName": "CARBAMAZEPINE", "hmsCode": "528.0", "internationalCode": "528.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "CARBAMAZEPINE", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 800.0, "test_profile": "CARBAMAZEPINE", "reference_range": "Therapeutic Range : 4 - 12 Toxic Level : More than 15", "result_unit": "Ug/ml", "decimals": 2}, {"id": 27, "department": "Immunology", "testName": "CEA (Carcinoembryonic Antigen)", "emrClassification": "test", "shortName": "CEA (Carcinoembryoni", "displayName": "CEA (Carcinoembryonic Antigen)", "hmsCode": "533.0", "internationalCode": "533.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "CEA determinations are not recommended for cancer screening in the general population and CEA concentrations within the normal range do not exclude the possible\npresence of a malignant disease. For diagnostic purposes, the results should always be assessed in conjunction with the patient’s medical history, clinical examination\nand other findings. The concentration of CEA in a given specimen, determined with assays from different manufacturers, may not be comparable due to differences in\nassay methods, calibration, and reagent specificity.\n1. The carcinoembryonic antigen (CEA) test is generally used to monitor treatment or to stage cancer disease.\n2. Slight to moderate CEA elevations can also occur iNn non-malignant diseases of the intestine, the pancreas, the liver, and the lungs (i.e. liver cirrhosis, chronic\nhepatitis, pancreatitis, ulcerative colitis, <PERSON><PERSON><PERSON>'s Disease).\n3. Smoking can also lead to elevated CEA values and needs to be taken into account when interpreting CEA levels.\n4. False negative / positive results are observed in patients receiving mouse monoclonal antibodies for diagnosis or therapy", "instructions": "CEA determinations are not recommended for cancer screening in the general population and CEA concentrations within the normal range do not exclude the possible\npresence of a malignant disease. For diagnostic purposes, the results should always be assessed in conjunction with the patient’s medical history, clinical examination\nand other findings. The concentration of CEA in a given specimen, determined with assays from different manufacturers, may not be comparable due to differences in\nassay methods, calibration, and reagent specificity.\n1. The carcinoembryonic antigen (CEA) test is generally used to monitor treatment or to stage cancer disease.\n2. Slight to moderate CEA elevations can also occur iNn non-malignant diseases of the intestine, the pancreas, the liver, and the lungs (i.e. liver cirrhosis, chronic\nhepatitis, pancreatitis, ulcerative colitis, <PERSON><PERSON><PERSON>'s Disease).\n3. Smoking can also lead to elevated CEA values and needs to be taken into account when interpreting CEA levels.\n4. False negative / positive results are observed in patients receiving mouse monoclonal antibodies for diagnosis or therapy", "specialReport": "", "reportName": "CEA (Carcinoembryonic Antigen)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 700.0, "test_profile": "CEA (Carcinoembryonic Antigen)", "reference_range": "", "result_unit": "ng/ml", "decimals": 2}, {"id": 28, "department": "Immunology", "testName": "Chromogranin A", "emrClassification": "test", "shortName": "Chromogranin A", "displayName": "Chromogranin A", "hmsCode": "1331.0", "internationalCode": "1331.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Chromogranin A", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 7000.0, "test_profile": "Chromogranin A", "reference_range": "Less than 76.3", "result_unit": "ng/ml", "decimals": 1}, {"id": 29, "department": "Immunology", "testName": "COPPER", "emrClassification": "test", "shortName": "COPPER", "displayName": "COPPER", "hmsCode": "536.0", "internationalCode": "536.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "COPPER", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 400.0, "test_profile": "COPPER", "reference_range": "Birth  – 6 months : 20-70 6 y           : 90-190 12 y           : 80-160  Adult  Male : 70-140 Female : 80-155  >60 yr Male : 85-170 Female : 85-190  Pregnancy at term: 118-302", "result_unit": "ug/dl", "decimals": 1}, {"id": 30, "department": "Immunology", "testName": "COPPER- SPOT URINE", "emrClassification": "test", "shortName": "COPPER- SPOT URINE", "displayName": "COPPER- SPOT URINE", "hmsCode": "1306.0", "internationalCode": "1306.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "COPPER- SPOT URINE", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1500.0, "test_profile": "COPPER- SPOT URINE", "reference_range": "2.00 - 80.00", "result_unit": "ug/L", "decimals": 0}, {"id": 31, "department": "Immunology", "testName": "CORTISOL (AM)", "emrClassification": "test", "shortName": "CORTISOL (AM)", "displayName": "CORTISOL (AM)", "hmsCode": "540.0", "internationalCode": "540.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "CORTISOL (AM)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 800.0, "test_profile": "CORTISOL (AM)", "reference_range": "AM : 6.2 to 19.4.", "result_unit": "ug/dl", "decimals": 2}, {"id": 32, "department": "Immunology", "testName": "CORTISOL (PM)", "emrClassification": "test", "shortName": "CORTISOL (PM)", "displayName": "CORTISOL (PM)", "hmsCode": "671.0", "internationalCode": "671.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "CORTISOL (PM)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 800.0, "test_profile": "CORTISOL (PM)", "reference_range": "PM : 2.3 to 11.9.", "result_unit": "ug/dl", "decimals": 2}, {"id": 33, "department": "Immunology", "testName": "Cortisol, Total", "emrClassification": "test", "shortName": "Cortisol, Total", "displayName": "Cortisol, Total", "hmsCode": "539.0", "internationalCode": "539.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Cortisol, Total", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 800.0, "test_profile": "Cortisol, Total", "reference_range": "AM : 6.2 to 19.4 PM : 2.3 to 11.9", "result_unit": "ug/dl", "decimals": 2}, {"id": 34, "department": "Immunology", "testName": "CYSTICERCOSIS IgG", "emrClassification": "test", "shortName": "CYSTICERCOSIS IgG", "displayName": "CYSTICERCOSIS IgG", "hmsCode": "546.0", "internationalCode": "546.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "CYSTICERCOSIS IgG", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2200.0, "test_profile": "CYSTICERCOSIS IgG", "reference_range": "Negative: < 0.3 Positive: >= 0.3", "result_unit": "OD RATIO", "decimals": 2}, {"id": 35, "department": "Immunology", "testName": "Dehydroepiandrosterone Sulfate (DHEA-S)", "emrClassification": "test", "shortName": "Dehydroepiandrostero", "displayName": "Dehydroepiandrosterone Sulfate (DHEA-S)", "hmsCode": "549.0", "internationalCode": "549.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Dehydroepiandrosterone Sulfate (DHEA-S)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 800.0, "test_profile": "Dehydroepiandrosterone Sulfate (DHEA-S)", "reference_range": "", "result_unit": "ug/dL", "decimals": 2}, {"id": 36, "department": "Immunology", "testName": "DESMOGLEIN  I Antibody", "emrClassification": "test", "shortName": "DESMOGLEIN  I Antibo", "displayName": "DESMOGLEIN  I Antibody", "hmsCode": "1572.0", "internationalCode": "1572.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "DESMOGLEIN  I Antibody", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 0, "test_profile": "DESMOGLEIN  I Antibody", "reference_range": "Negative: < 20.0 Positive: > 20.0", "result_unit": "RU/ml", "decimals": 0}, {"id": 37, "department": "Immunology", "testName": "DESMOGLEIN  III Antibody", "emrClassification": "test", "shortName": "DESMOGLEIN  III Anti", "displayName": "DESMOGLEIN  III Antibody", "hmsCode": "1573.0", "internationalCode": "1573.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "DESMOGLEIN  III Antibody", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 0, "test_profile": "DESMOGLEIN  III Antibody", "reference_range": "Negative: < 20.0 Positive: > 20.0", "result_unit": "RU/ml", "decimals": 0}, {"id": 38, "department": "Immunology", "testName": "Determination of  Chromium - Urine", "emrClassification": "test", "shortName": "Determination of  Ch", "displayName": "Determination of  Chromium - Urine", "hmsCode": "1320.0", "internationalCode": "1320.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Determination of  Chromium - Urine", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 0, "test_profile": "Determination of  Chromium - Urine", "reference_range": "<5.0", "result_unit": "ug/L", "decimals": 2}, {"id": 39, "department": "Immunology", "testName": "DHEA-DehydroepiAndrostenedione", "emrClassification": "test", "shortName": "DHEA-DehydroepiAndro", "displayName": "DHEA-DehydroepiAndrostenedione", "hmsCode": "548.0", "internationalCode": "548.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "DHEA-DehydroepiAndrostenedione", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2800.0, "test_profile": "DHEA-DehydroepiAndrostenedione", "reference_range": "1.2 - 6.3", "result_unit": "ng/ml", "decimals": 2}, {"id": 40, "department": "Immunology", "testName": "DIGOXIN", "emrClassification": "test", "shortName": "DIGOXIN", "displayName": "DIGOXIN", "hmsCode": "550.0", "internationalCode": "550.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "DIGOXIN", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 700.0, "test_profile": "DIGOXIN", "reference_range": "0.80- 2.00", "result_unit": "ng/ml", "decimals": 2}, {"id": 41, "department": "Immunology", "testName": "Dopamine", "emrClassification": "test", "shortName": "Dopamine", "displayName": "Dopamine", "hmsCode": "692.0", "internationalCode": "692.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Dopamine", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 3000.0, "test_profile": "Dopamine", "reference_range": "Less than 100", "result_unit": "pg/ml", "decimals": 2}, {"id": 42, "department": "Immunology", "testName": "Erythropoietin", "emrClassification": "test", "shortName": "Erythropoietin", "displayName": "Erythropoietin", "hmsCode": "662.0", "internationalCode": "662.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Erythropoietin", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1500.0, "test_profile": "Erythropoietin", "reference_range": "4.3 to 29", "result_unit": "mIU/ml", "decimals": 2}, {"id": 43, "department": "Immunology", "testName": "ESTRADIOL", "emrClassification": "test", "shortName": "ESTRADIOL", "displayName": "ESTRADIOL", "hmsCode": "553.0", "internationalCode": "553.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "ESTRADIOL", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 700.0, "test_profile": "ESTRADIOL", "reference_range": "MALE   : 7.63 - 42.6  FEMALE   Follicular Phase : 12.5  - 166  Ovulation Phase  : 85.8  - 498  Luteal Phase     : 43.8  - 211  Post menopausal  : < 5.0 - 54.7", "result_unit": "pg/ml", "decimals": 1}, {"id": 44, "department": "Immunology", "testName": "ESTRIOL - E3", "emrClassification": "test", "shortName": "ESTRIOL - E3", "displayName": "ESTRIOL - E3", "hmsCode": "554.0", "internationalCode": "554.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "ESTRIOL - E3", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 800.0, "test_profile": "ESTRIOL - E3", "reference_range": "14th Weeks   -  0.14 - 0.61 ng/ml   15th Weeks   -  0.26 - 0.86 ng/ml   16th Weeks   -  0.38 - 1.16 ng/ml   17th Weeks  -   0.52 - 1.55 ng.ml   18th Weeks   -  0.77 - 2.15 ng/ml   19th Weeks   -  1.00 - 3.02 ng/ml   20th Weeks   -  0.89 - 4.49 ng/ml   21st Weeks   -  1.13 - 6.47 ng/ml   22nd Weeks   -  1.80 - 9.10 ng/ml", "result_unit": "ng/ml", "decimals": 1}, {"id": 45, "department": "Immunology", "testName": "Estrone", "emrClassification": "test", "shortName": "Estrone", "displayName": "Estrone", "hmsCode": "1499.0", "internationalCode": "1499.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Estrone", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 6500.0, "test_profile": "Estrone", "reference_range": "1st Trimester Pregnancy: 61.70 -715.40 2nd Trimester Pregnancy: 166.80-1861.50 3rd Trimester Pregnancy: 1039.20-3209.50  Postmenopausal with ERT   : 40.0-346.0 Postmenopausal without ERT: 14.10-102.60  Oral contraceptives: 23.60-83.10  Follicular Phase: 37.20-137.70 Luteal Phase    : 49.80-114.10 Peri -Ovulatory : 59.90-229.20", "result_unit": "pg/ml", "decimals": 2}, {"id": 46, "department": "Immunology", "testName": "FACTOR IX ACTIVITY", "emrClassification": "test", "shortName": "FACTOR IX ACTIVITY", "displayName": "FACTOR IX ACTIVITY", "hmsCode": "639.0", "internationalCode": "639.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "FACTOR IX ACTIVITY", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1900.0, "test_profile": "FACTOR IX ACTIVITY", "reference_range": "70 to 120", "result_unit": "%", "decimals": 0}, {"id": 47, "department": "Immunology", "testName": "FACTOR V, FUNCTIONAL", "emrClassification": "test", "shortName": "FACTOR V, FUNCTIONAL", "displayName": "FACTOR V, FUNCTIONAL", "hmsCode": "696.0", "internationalCode": "696.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "FACTOR V, FUNCTIONAL", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2200.0, "test_profile": "FACTOR V, FUNCTIONAL", "reference_range": "70 - 120", "result_unit": "%", "decimals": 0}, {"id": 48, "department": "Immunology", "testName": "FACTOR VIII FUNCTIONAL", "emrClassification": "test", "shortName": "FACTOR VIII FUNCTION", "displayName": "FACTOR VIII FUNCTIONAL", "hmsCode": "640.0", "internationalCode": "640.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "FACTOR VIII FUNCTIONAL", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2500.0, "test_profile": "FACTOR VIII FUNCTIONAL", "reference_range": "60 - 150", "result_unit": "%", "decimals": 0}, {"id": 49, "department": "Immunology", "testName": "FERRITIN", "emrClassification": "test", "shortName": "FERRITIN", "displayName": "FERRITIN", "hmsCode": "556.0", "internationalCode": "556.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "FERRITIN", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 700.0, "test_profile": "FERRITIN", "reference_range": "", "result_unit": "ng/ml", "decimals": 1}, {"id": 50, "department": "Immunology", "testName": "FOLIC ACID", "emrClassification": "test", "shortName": "FOLIC ACID", "displayName": "FOLIC ACID", "hmsCode": "557.0", "internationalCode": "557.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "FOLIC ACID", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "FOLIC ACID", "reference_range": "3.1 - 20.5", "result_unit": "ng/ml", "decimals": 1}, {"id": 51, "department": "Immunology", "testName": "FOLICACID (RED CELLS)", "emrClassification": "test", "shortName": "FOLICACID (RED CELLS", "displayName": "FOLICACID (RED CELLS)", "hmsCode": "676.0", "internationalCode": "676.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "FOLICACID (RED CELLS)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2500.0, "test_profile": "FOLICACID (RED CELLS)", "reference_range": "280 - 791", "result_unit": "ng/ml", "decimals": 1}, {"id": 52, "department": "Immunology", "testName": "Free Androgen Index (FAI)", "emrClassification": "test", "shortName": "Free Androgen Index ", "displayName": "Free Androgen Index (FAI)", "hmsCode": "655.0", "internationalCode": "655.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Free Androgen Index (FAI)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2800.0, "test_profile": "Free Androgen Index (FAI)", "reference_range": "Female:- Oral Contraceptives : Upto 3.4 Postmenopausal Untreated : Upto 6.6 Hirsute : 1.7 - 20.6  Normal Menstruating: 0 - 8.5  Follicular phase: 0.8 - 9.3  Midcycle: 1.3 - 17  Luteal phase: 0.8 - 11  Males:14.8- 94.8", "result_unit": "", "decimals": 2}, {"id": 53, "department": "Immunology", "testName": "Free Beta HCG.", "emrClassification": "test", "shortName": "Free Beta HCG.", "displayName": "Free Beta HCG.", "hmsCode": "670.0", "internationalCode": "670.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Free Beta HCG.", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "Free Beta HCG.", "reference_range": "Healthy males,Non pregnant Females,post menopausal Females : < 2.0", "result_unit": "ng/ml", "decimals": 0}, {"id": 54, "department": "Immunology", "testName": "FREE T3", "emrClassification": "test", "shortName": "FREE T3", "displayName": "FREE T3", "hmsCode": "561.0", "internationalCode": "561.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "FREE T3", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 200.0, "test_profile": "FREE T3", "reference_range": "", "result_unit": "pg/ml", "decimals": 2}, {"id": 55, "department": "Immunology", "testName": "FREE T4", "emrClassification": "test", "shortName": "FREE T4", "displayName": "FREE T4", "hmsCode": "562.0", "internationalCode": "562.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "FREE T4", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 200.0, "test_profile": "FREE T4", "reference_range": "", "result_unit": "ng/ml", "decimals": 2}, {"id": 56, "department": "Immunology", "testName": "Free Testosterone", "emrClassification": "test", "shortName": "Free Testosterone", "displayName": "Free Testosterone", "hmsCode": "563.0", "internationalCode": "563.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Free Testosterone", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 800.0, "test_profile": "Free Testosterone", "reference_range": "", "result_unit": "pg/ml", "decimals": 2}, {"id": 57, "department": "Immunology", "testName": "FRUCTOSAMINE", "emrClassification": "test", "shortName": "FRUCTOSAMINE", "displayName": "FRUCTOSAMINE", "hmsCode": "565.0", "internationalCode": "565.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "FRUCTOSAMINE", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 800.0, "test_profile": "FRUCTOSAMINE", "reference_range": "LESS THAN 286", "result_unit": "umol/L", "decimals": 0}, {"id": 58, "department": "Immunology", "testName": "FSH", "emrClassification": "test", "shortName": "FSH", "displayName": "FSH", "hmsCode": "566.0", "internationalCode": "566.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "FSH", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 500.0, "test_profile": "FSH", "reference_range": "MALE             : 1.50 - 12.40     FEMALE   Follicular Phase : 3.50 - 12.50  Ovulation  Phase : 4.70 - 21.50  Luteal Phase     : 1.70 - 7.70  Post Menopausal  : 25.80 - 134.80", "result_unit": "miU/ml", "decimals": 2}, {"id": 59, "department": "Immunology", "testName": "GAD-65 - IgG", "emrClassification": "test", "shortName": "GAD-65 - IgG", "displayName": "GAD-65 - IgG", "hmsCode": "567.0", "internationalCode": "567.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "Comments :  Glutamic Acid Decarboxylase (GAD) autoantibodies are detected in most newly diagnosed Type1 A Diabetes patients and in about 80% of prediabetic first degree relatives of patients. Anti GAD are directed primarily at the GAD 65 isoform which is found mainly in pancreatic islet cells and in the central nervous system. Presence of GAD autoantibodies is also associated with Stiff man syndrome.\n\nUsed to diagnose Insulin Dependent Diabetes mellitus (IDDM) and differentiate from Type 2 Diabetes  (NIDDM),  to assess risk and predict onset of development of IDDM specially in first degree relatives and to assess risk of development of related endocrine disorders.", "instructions": "Comments :  Glutamic Acid Decarboxylase (GAD) autoantibodies are detected in most newly diagnosed Type1 A Diabetes patients and in about 80% of prediabetic first degree relatives of patients. Anti GAD are directed primarily at the GAD 65 isoform which is found mainly in pancreatic islet cells and in the central nervous system. Presence of GAD autoantibodies is also associated with Stiff man syndrome.\n\nUsed to diagnose Insulin Dependent Diabetes mellitus (IDDM) and differentiate from Type 2 Diabetes  (NIDDM),  to assess risk and predict onset of development of IDDM specially in first degree relatives and to assess risk of development of related endocrine disorders.", "specialReport": "", "reportName": "GAD-65 - IgG", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 4500.0, "test_profile": "GAD-65 - IgG", "reference_range": "Negative : Below 10.0  Positive  : Above 10.0", "result_unit": "IU/ml", "decimals": 2}, {"id": 60, "department": "Immunology", "testName": "GASTRIN", "emrClassification": "test", "shortName": "GASTRIN", "displayName": "GASTRIN", "hmsCode": "642.0", "internationalCode": "642.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "GASTRIN", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1200.0, "test_profile": "GASTRIN", "reference_range": "13.00 - 115.00", "result_unit": "pg/ml", "decimals": 2}, {"id": 61, "department": "Immunology", "testName": "GROWTH HORMONE", "emrClassification": "test", "shortName": "GROWTH HORMONE", "displayName": "GROWTH HORMONE", "hmsCode": "571.0", "internationalCode": "571.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "GROWTH HORMONE", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 600.0, "test_profile": "GROWTH HORMONE", "reference_range": "", "result_unit": "ng/ml", "decimals": 3}, {"id": 62, "department": "Immunology", "testName": "HBDH", "emrClassification": "test", "shortName": "HBDH", "displayName": "HBDH", "hmsCode": "568.0", "internationalCode": "568.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "HBDH", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1200.0, "test_profile": "HBDH", "reference_range": "90 - 180", "result_unit": "U/L", "decimals": 2}, {"id": 63, "department": "Immunology", "testName": "Holotranscobalamin (Active Vitamin B12)", "emrClassification": "test", "shortName": "Holotranscobalamin (", "displayName": "Holotranscobalamin (Active Vitamin B12)", "hmsCode": "1620.0", "internationalCode": "1620.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Holotranscobalamin (Active Vitamin B12)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1600.0, "test_profile": "Holotranscobalamin (Active Vitamin B12)", "reference_range": "25.1 - 165", "result_unit": "pmol/L", "decimals": 1}, {"id": 64, "department": "Immunology", "testName": "Homocysteine, Urine - Qualitative", "emrClassification": "test", "shortName": "Homocysteine, Urine ", "displayName": "Homocysteine, Urine - Qualitative", "hmsCode": "698.0", "internationalCode": "698.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Homocysteine, Urine - Qualitative", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1500.0, "test_profile": "Homocysteine, Urine - Qualitative", "reference_range": "Negative", "result_unit": "", "decimals": 0}, {"id": 65, "department": "Immunology", "testName": "IgA", "emrClassification": "test", "shortName": "IgA", "displayName": "IgA", "hmsCode": "574.0", "internationalCode": "574.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "IgA", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "IgA", "reference_range": "0.7 - 4.0", "result_unit": "gm/L", "decimals": 2}, {"id": 66, "department": "Immunology", "testName": "IgE", "emrClassification": "test", "shortName": "IgE", "displayName": "IgE", "hmsCode": "637.0", "internationalCode": "637.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "IgE", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "IgE", "reference_range": "IU/mL", "result_unit": "", "decimals": 0}, {"id": 67, "department": "Immunology", "testName": "IGF BP3", "emrClassification": "test", "shortName": "IGF BP3", "displayName": "IGF BP3", "hmsCode": "583.0", "internationalCode": "583.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "IGF BP3", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2500.0, "test_profile": "IGF BP3", "reference_range": "1.1-5.2", "result_unit": "ug/ml", "decimals": 2}, {"id": 68, "department": "Immunology", "testName": "IgG", "emrClassification": "test", "shortName": "IgG", "displayName": "IgG", "hmsCode": "572.0", "internationalCode": "572.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "IgG", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "IgG", "reference_range": "7.0 - 16.0", "result_unit": "gm/L", "decimals": 2}, {"id": 69, "department": "Immunology", "testName": "IgG4 Sub Class", "emrClassification": "test", "shortName": "IgG4 Sub Class", "displayName": "IgG4 Sub Class", "hmsCode": "1313.0", "internationalCode": "1313.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "IgG4 Sub Class", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 4600.0, "test_profile": "IgG4 Sub Class", "reference_range": "0.049-1.985", "result_unit": "g/L", "decimals": 3}, {"id": 70, "department": "Immunology", "testName": "IgM", "emrClassification": "test", "shortName": "IgM", "displayName": "IgM", "hmsCode": "576.0", "internationalCode": "576.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "IgM", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "IgM", "reference_range": "0.40 - 2.40", "result_unit": "gm/L", "decimals": 2}, {"id": 71, "department": "Immunology", "testName": "INHIBIN - A", "emrClassification": "test", "shortName": "INHIBIN - A", "displayName": "INHIBIN - A", "hmsCode": "681.0", "internationalCode": "681.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "INHIBIN - A", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1400.0, "test_profile": "INHIBIN - A", "reference_range": "Normal Male : <1 - 3.58  Female Early Follicular Phase : 5.46 - 28.16 Mid Follicular Phase   : 7.87 - 34.54 Late Follicular Phase  : 19.49 - 102.3 Mid Cycle              : 49.92 - 155.5 Early Luteal           : 35.93 - 132.7 Mid Luteal             : 13.15 - 159.6 Late Luteal            : 7.28 - 89.95 IVF Peak levels        : 354.2 - 1690.0 PCOS - Ovulatory       : 5.65 - 15.99 Postmenopausal         : < 1 - 3.88   Pregnancy Gestational  age in weeks    |    Medians in pg/mL --------------------------------------         15                   157.55         16                   153.29         17                   151.20         18                   155.14         19                   165.18         20                   185.76 --------------------------------------", "result_unit": "pg/ml", "decimals": 0}, {"id": 72, "department": "Immunology", "testName": "INHIBIN - B", "emrClassification": "test", "shortName": "INHIBIN - B", "displayName": "INHIBIN - B", "hmsCode": "578.0", "internationalCode": "578.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "INHIBIN - B", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1700.0, "test_profile": "INHIBIN - B", "reference_range": "Men     : 151.70 - 173.90 Female  : 134.70 - 150.50", "result_unit": "pg/ml", "decimals": 2}, {"id": 73, "department": "Immunology", "testName": "INSULIN (F)", "emrClassification": "test", "shortName": "INSULIN (F)", "displayName": "INSULIN (F)", "hmsCode": "579.0", "internationalCode": "579.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "Determination of insulin is utilized in the diagnosis and therapy of various disorders of carbohydrate metabolism, including diabetes mellitus and hypoglycemia.\n\nElevated levels: Acromegaly, Cushing syndrome, Use of corticosteroids, levodopa, oral contraceptives, Fructose or galactose intolerance, Insulinomas, Obesity and Insulin resistance.\n\nDecreased levels: Diabetes, Hypopituitarism and Pancreatic diseases.\n\nPatients treated with bovine, porcine or human insulin sometimes contain anti-insulin antibodies which can affect the test result. Samples should not be taken from patients receiving therapy with Vit B7 doses (i.e. > 5 mg/day) until at least 8 hours following the last administration. For diagnostic purposes, Insulin levels must be evaluated in context with the patient’s medical history, clinical examination and other findings.", "instructions": "Determination of insulin is utilized in the diagnosis and therapy of various disorders of carbohydrate metabolism, including diabetes mellitus and hypoglycemia.\n\nElevated levels: Acromegaly, Cushing syndrome, Use of corticosteroids, levodopa, oral contraceptives, Fructose or galactose intolerance, Insulinomas, Obesity and Insulin resistance.\n\nDecreased levels: Diabetes, Hypopituitarism and Pancreatic diseases.\n\nPatients treated with bovine, porcine or human insulin sometimes contain anti-insulin antibodies which can affect the test result. Samples should not be taken from patients receiving therapy with Vit B7 doses (i.e. > 5 mg/day) until at least 8 hours following the last administration. For diagnostic purposes, Insulin levels must be evaluated in context with the patient’s medical history, clinical examination and other findings.", "specialReport": "", "reportName": "INSULIN (F)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "INSULIN (F)", "reference_range": "", "result_unit": "uiU/ml", "decimals": 2}, {"id": 74, "department": "Immunology", "testName": "INSULIN (PP)", "emrClassification": "test", "shortName": "INSULIN (PP)", "displayName": "INSULIN (PP)", "hmsCode": "580.0", "internationalCode": "580.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "INSULIN (PP)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "INSULIN (PP)", "reference_range": "22 - 75", "result_unit": "uiU/ml", "decimals": 2}, {"id": 75, "department": "Immunology", "testName": "INSULIN (R)", "emrClassification": "test", "shortName": "INSULIN (R)", "displayName": "INSULIN (R)", "hmsCode": "643.0", "internationalCode": "643.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "INSULIN (R)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "INSULIN (R)", "reference_range": "", "result_unit": "uiU/ml", "decimals": 0}, {"id": 76, "department": "Immunology", "testName": "Insulin - 120mts", "emrClassification": "test", "shortName": "Insulin - 120mts", "displayName": "Insulin - 120mts", "hmsCode": "694.0", "internationalCode": "694.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Insulin - 120mts", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "Insulin - 120mts", "reference_range": "", "result_unit": "", "decimals": 0}, {"id": 77, "department": "Immunology", "testName": "Insulin - 30 mts", "emrClassification": "test", "shortName": "Insulin - 30 mts", "displayName": "Insulin - 30 mts", "hmsCode": "693.0", "internationalCode": "693.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Insulin - 30 mts", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "Insulin - 30 mts", "reference_range": "", "result_unit": "uiU/ml", "decimals": 0}, {"id": 78, "department": "Immunology", "testName": "<PERSON><PERSON><PERSON> (Free)", "emrClassification": "test", "shortName": "<PERSON><PERSON><PERSON> (Fr", "displayName": "<PERSON><PERSON><PERSON> (Free)", "hmsCode": "656.0", "internationalCode": "656.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "<PERSON><PERSON><PERSON> (Free)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1500.0, "test_profile": "<PERSON><PERSON><PERSON> (Free)", "reference_range": "Negative  :    < 10 Positive  :   >= 10", "result_unit": "U/mL", "decimals": 2}, {"id": 79, "department": "Immunology", "testName": "INSULIN LIKE GROWTH  FACTOR (IGF-1)", "emrClassification": "test", "shortName": "INSULIN LIKE GROWTH ", "displayName": "INSULIN LIKE GROWTH  FACTOR (IGF-1)", "hmsCode": "582.0", "internationalCode": "582.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "INSULIN LIKE GROWTH  FACTOR (IGF-1)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 0, "test_profile": "INSULIN LIKE GROWTH  FACTOR (IGF-1)", "reference_range": "", "result_unit": "ng/ml", "decimals": 1}, {"id": 80, "department": "Immunology", "testName": "Interleukin 6 (IL-6)", "emrClassification": "test", "shortName": "Interleukin 6 (IL-6)", "displayName": "Interleukin 6 (IL-6)", "hmsCode": "1357.0", "internationalCode": "1357.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Interleukin 6 (IL-6)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 3000.0, "test_profile": "Interleukin 6 (IL-6)", "reference_range": "0.00 - 7.00", "result_unit": "pg/ml", "decimals": 1}, {"id": 81, "department": "Immunology", "testName": "IONISED CALCIUM", "emrClassification": "test", "shortName": "IONISED CALCIUM", "displayName": "IONISED CALCIUM", "hmsCode": "559.0", "internationalCode": "559.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "IONISED CALCIUM", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 300.0, "test_profile": "IONISED CALCIUM", "reference_range": "4.5 - 5.6.", "result_unit": "mg/dL", "decimals": 1}, {"id": 82, "department": "Immunology", "testName": "Islet cell anibody (ICA-512)", "emrClassification": "test", "shortName": "Islet cell anibody (", "displayName": "Islet cell anibody (ICA-512)", "hmsCode": "663.0", "internationalCode": "663.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Islet cell anibody (ICA-512)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2500.0, "test_profile": "Islet cell anibody (ICA-512)", "reference_range": "NEGATIVE", "result_unit": "", "decimals": 0}, {"id": 83, "department": "Immunology", "testName": "Islet Cell Antobody", "emrClassification": "test", "shortName": "Islet Cell Antobody", "displayName": "Islet Cell Antobody", "hmsCode": "679.0", "internationalCode": "679.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Islet Cell Antobody", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2500.0, "test_profile": "Islet Cell Antobody", "reference_range": "", "result_unit": "", "decimals": 0}, {"id": 84, "department": "Immunology", "testName": "JAK - 2 Mutation", "emrClassification": "test", "shortName": "JAK - 2 Mutation", "displayName": "JAK - 2 Mutation", "hmsCode": "664.0", "internationalCode": "664.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "JAK - 2 Mutation", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 6000.0, "test_profile": "JAK - 2 Mutation", "reference_range": "NEGATIVE", "result_unit": "", "decimals": 0}, {"id": 85, "department": "Immunology", "testName": "LDH- ISOENZYMES", "emrClassification": "test", "shortName": "LDH- ISOENZYMES", "displayName": "LDH- ISOENZYMES", "hmsCode": "1504.0", "internationalCode": "1504.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "LDH- ISOENZYMES", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 4500.0, "test_profile": "LDH- ISOENZYMES", "reference_range": "", "result_unit": "", "decimals": 0}, {"id": 86, "department": "Immunology", "testName": "LEAD", "emrClassification": "test", "shortName": "LEAD", "displayName": "LEAD", "hmsCode": "584.0", "internationalCode": "584.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "LEAD", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1300.0, "test_profile": "LEAD", "reference_range": "Blood: General Population - Less than 10 Industrial workers - Less than 42 Action Required    - More than 42", "result_unit": "ug/dl", "decimals": 2}, {"id": 87, "department": "Immunology", "testName": "Leishmania (KALA AZAR) Ab IgG", "emrClassification": "test", "shortName": "Leishmania (KALA AZA", "displayName": "Leishmania (KALA AZAR) Ab IgG", "hmsCode": "1353.0", "internationalCode": "1353.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Leishmania (KALA AZAR) Ab IgG", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2000.0, "test_profile": "Leishmania (KALA AZAR) Ab IgG", "reference_range": "<0.9      : Negative 0.9 - 1.1 : Equivocal >1.1      : Positive", "result_unit": "index", "decimals": 0}, {"id": 88, "department": "Immunology", "testName": "Leptin (Human Leptin)", "emrClassification": "test", "shortName": "Leptin (Human Leptin", "displayName": "Leptin (Human Leptin)", "hmsCode": "666.0", "internationalCode": "666.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Leptin (Human Leptin)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 5000.0, "test_profile": "Leptin (Human Leptin)", "reference_range": "", "result_unit": "ng/ml", "decimals": 1}, {"id": 89, "department": "Immunology", "testName": "Levetiracetam", "emrClassification": "test", "shortName": "Levetiracetam", "displayName": "Levetiracetam", "hmsCode": "1632.0", "internationalCode": "1632.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Levetiracetam", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 6000.0, "test_profile": "Levetiracetam", "reference_range": "6 - 46", "result_unit": "ug/ml", "decimals": 2}, {"id": 90, "department": "Immunology", "testName": "LH", "emrClassification": "test", "shortName": "LH", "displayName": "LH", "hmsCode": "585.0", "internationalCode": "585.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "LH", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 500.0, "test_profile": "LH", "reference_range": "MALE  : 1.7 - 8.6     FEMALE  Follicular Phase: 2.4 - 12.6  Ovulation  Phase: 14.0 - 95.6  Luteal Phase    : 1.0 - 11.4   Post Menopausal : 7.7 - 58.5", "result_unit": "miU/ml", "decimals": 2}, {"id": 91, "department": "Immunology", "testName": "Mercury, Blood", "emrClassification": "test", "shortName": "Mercury, Blood", "displayName": "Mercury, Blood", "hmsCode": "1317.0", "internationalCode": "1317.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Mercury, Blood", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 3000.0, "test_profile": "Mercury, Blood", "reference_range": "0.46 - 7.5", "result_unit": "ug/L", "decimals": 2}, {"id": 92, "department": "Immunology", "testName": "Mullerian Inhibiting Substance - AMH", "emrClassification": "test", "shortName": "Mullerian Inhibiting", "displayName": "Mullerian Inhibiting Substance - AMH", "hmsCode": "657.0", "internationalCode": "657.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Mullerian Inhibiting Substance - AMH", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1700.0, "test_profile": "Mullerian Inhibiting Substance - AMH", "reference_range": "Optimal Fertility : 4.0 - 6.79 Satisfactory Fertility : 2.19 - 4.0 Low Fertility : 0.3 - 2.19 Very low fertility : <0.3  High Levels : >6.79 (?PCOD)", "result_unit": "ng/ml", "decimals": 2}, {"id": 93, "department": "Immunology", "testName": "NMO (Aquaporin 4) Neuromylitis Optica Ab", "emrClassification": "test", "shortName": "NMO (Aquaporin 4) Ne", "displayName": "NMO (Aquaporin 4) Neuromylitis Optica Ab", "hmsCode": "1548.0", "internationalCode": "1548.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "NMO (Aquaporin 4) Neuromylitis Optica Ab", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 4800.0, "test_profile": "NMO (Aquaporin 4) Neuromylitis Optica Ab", "reference_range": "Negative", "result_unit": "", "decimals": 0}, {"id": 94, "department": "Immunology", "testName": "NMO with MOG Antibody", "emrClassification": "test", "shortName": "NMO with MOG Antibod", "displayName": "NMO with MOG Antibody", "hmsCode": "688.0", "internationalCode": "688.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "NMO with MOG Antibody", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 4500.0, "test_profile": "NMO with MOG Antibody", "reference_range": "NEGATIVE", "result_unit": "", "decimals": 0}, {"id": 95, "department": "Immunology", "testName": "Nor-Adrenaline (Nor-epinephrine)", "emrClassification": "test", "shortName": "Nor-Adrenaline (Nor-", "displayName": "Nor-Adrenaline (Nor-epinephrine)", "hmsCode": "587.0", "internationalCode": "587.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Nor-Adrenaline (Nor-epinephrine)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 4200.0, "test_profile": "Nor-Adrenaline (Nor-epinephrine)", "reference_range": "Less than 600", "result_unit": "pg/ml", "decimals": 2}, {"id": 96, "department": "Immunology", "testName": "NT pro B-type natriuretic peptide", "emrClassification": "test", "shortName": "NT pro B-type natriu", "displayName": "NT pro B-type natriuretic peptide", "hmsCode": "650.0", "internationalCode": "650.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "NT pro B-type natriuretic peptide", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2100.0, "test_profile": "NT pro B-type natriuretic peptide", "reference_range": "<300", "result_unit": "pg/ml", "decimals": 1}, {"id": 97, "department": "Immunology", "testName": "PAPPa", "emrClassification": "test", "shortName": "PAPPa", "displayName": "PAPPa", "hmsCode": "661.0", "internationalCode": "661.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "PAPPa", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 0, "test_profile": "PAPPa", "reference_range": "", "result_unit": "miU/mL", "decimals": 0}, {"id": 98, "department": "Immunology", "testName": "PARACETAMOL", "emrClassification": "test", "shortName": "PARACETAMOL", "displayName": "PARACETAMOL", "hmsCode": "592.0", "internationalCode": "592.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "PARACETAMOL", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1200.0, "test_profile": "PARACETAMOL", "reference_range": "Toxic range : Above 50 at 12hrs after ingestion  Therapeutic range : 10 to 30  Toxic range : Above 150 at 4hrs after ingestion", "result_unit": "ug/ml", "decimals": 0}, {"id": 99, "department": "Immunology", "testName": "Parathyroid Hormone, Intact (iPTH)", "emrClassification": "test", "shortName": "Parathyroid <PERSON>e,", "displayName": "Parathyroid Hormone, Intact (iPTH)", "hmsCode": "601.0", "internationalCode": "601.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Parathyroid Hormone, Intact (iPTH)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1000.0, "test_profile": "Parathyroid Hormone, Intact (iPTH)", "reference_range": "15 - 65", "result_unit": "pg/ml", "decimals": 2}, {"id": 100, "department": "Immunology", "testName": "PHENOBARBITONE", "emrClassification": "test", "shortName": "PHENOBARBITONE", "displayName": "PHENOBARBITONE", "hmsCode": "593.0", "internationalCode": "593.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "PHENOBARBITONE", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "PHENOBARBITONE", "reference_range": "Therapeutic Level : 15 - 40  Toxic Level   : >40", "result_unit": "ug/ml", "decimals": 1}, {"id": 101, "department": "Immunology", "testName": "PHENOBARBITONE, Animal", "emrClassification": "test", "shortName": "PHENOBARBITONE, <PERSON><PERSON>", "displayName": "PHENOBARBITONE, Animal", "hmsCode": "1564.0", "internationalCode": "1564.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "PHENOBARBITONE, Animal", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1500.0, "test_profile": "PHENOBARBITONE, Animal", "reference_range": "", "result_unit": "ug/ml", "decimals": 1}, {"id": 102, "department": "Immunology", "testName": "PHENYTOIN", "emrClassification": "test", "shortName": "PHENYTOIN", "displayName": "PHENYTOIN", "hmsCode": "594.0", "internationalCode": "594.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "PHENYTOIN", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 700.0, "test_profile": "PHENYTOIN", "reference_range": "Therapeutic Range : 10 - 20  Toxic Range       : > 20.0", "result_unit": "ug/ml", "decimals": 1}, {"id": 103, "department": "Immunology", "testName": "PIIINP (PROCOLLAGEN TYPE III AMINO TERMINAL PROPEPTIDE)", "emrClassification": "test", "shortName": "PIIINP (PROCOLLAGEN ", "displayName": "PIIINP (PROCOLLAGEN TYPE III AMINO TERMINAL PROPEPTIDE)", "hmsCode": "1630.0", "internationalCode": "1630.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "PIIINP (PROCOLLAGEN TYPE III AMINO TERMINAL PROPEPTIDE)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 5000.0, "test_profile": "PIIINP (PROCOLLAGEN TYPE III AMINO TERMINAL PROPEPTIDE)", "reference_range": "Less than 7.85", "result_unit": "ng/ml", "decimals": 2}, {"id": 104, "department": "Immunology", "testName": "PLA2 receptor antibody", "emrClassification": "test", "shortName": "PLA2 receptor antibo", "displayName": "PLA2 receptor antibody", "hmsCode": "1319.0", "internationalCode": "1319.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "PLA2 receptor antibody", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 5500.0, "test_profile": "PLA2 receptor antibody", "reference_range": "NEGATIVE  Sample Screening dilution 1:10", "result_unit": "", "decimals": 0}, {"id": 105, "department": "Immunology", "testName": "PLACENTAL GROWTH FACTOR (PIGF)", "emrClassification": "test", "shortName": "PLACENTAL GROWTH FAC", "displayName": "PLACENTAL GROWTH FACTOR (PIGF)", "hmsCode": "1366.0", "internationalCode": "1366.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "PLACENTAL GROWTH FACTOR (PIGF)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 4300.0, "test_profile": "PLACENTAL GROWTH FACTOR (PIGF)", "reference_range": "Refer Interpretation", "result_unit": "pg/ml", "decimals": 1}, {"id": 106, "department": "Immunology", "testName": "Prenatal Screening - 1st TRIMESTER", "emrClassification": "test", "shortName": "Prenatal Screening -", "displayName": "Prenatal Screening - 1st TRIMESTER", "hmsCode": "644.0", "internationalCode": "644.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Prenatal Screening - 1st TRIMESTER", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 0.0, "test_profile": "Prenatal Screening - 1st TRIMESTER", "reference_range": "", "result_unit": "", "decimals": 0}, {"id": 107, "department": "Immunology", "testName": "Prenatal Screening - 2nd TRIMESTER", "emrClassification": "test", "shortName": "Prenatal Screening -", "displayName": "Prenatal Screening - 2nd TRIMESTER", "hmsCode": "638.0", "internationalCode": "638.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Prenatal Screening - 2nd TRIMESTER", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2200.0, "test_profile": "Prenatal Screening - 2nd TRIMESTER", "reference_range": "", "result_unit": "", "decimals": 0}, {"id": 108, "department": "Immunology", "testName": "APOLIPOPROTEIN B", "emrClassification": "test", "shortName": "APOLIPOPROTEIN B", "displayName": "APOLIPOPROTEIN B", "hmsCode": "31.0", "internationalCode": "31.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "APOLIPOPROTEIN B", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 0, "test_profile": "APOLIPOPROTEIN B", "reference_range": "Male : 55 - 140 Female : 55 - 125", "result_unit": "mg/dL", "decimals": 1}, {"id": 109, "department": "Immunology", "testName": "Procalcitonin", "emrClassification": "test", "shortName": "Procalcitonin", "displayName": "Procalcitonin", "hmsCode": "659.0", "internationalCode": "659.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Procalcitonin", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2000.0, "test_profile": "Procalcitonin", "reference_range": "<0.5 : Represent a low risk of severe sepsis and/or septic shock  >2.0 : Represent a high risk of severe sepsis and/or septic shock", "result_unit": "ng/ml", "decimals": 3}, {"id": 110, "department": "Immunology", "testName": "PROGESTERONE", "emrClassification": "test", "shortName": "PROGESTERONE", "displayName": "PROGESTERONE", "hmsCode": "596.0", "internationalCode": "596.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "PROGESTERONE", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "PROGESTERONE", "reference_range": "MALE   : 0.20 - 1.4  FEMALE         : Follicular Phase    : 0.20 - 1.5         : Ovulation Phase     : 0.80 - 3.0        : Luteal Phase        : 1.70 - 27.0        : Post menopausal     : 0.10 - 0.80  First trimester : 7.25 - 44.0 Second trimester: 19.50 - 82.50 Third trimester : 65.0 - 229.0 (ECLIA)", "result_unit": "ng/ml", "decimals": 2}, {"id": 111, "department": "Immunology", "testName": "PROLACTIN", "emrClassification": "test", "shortName": "PROLACTIN", "displayName": "PROLACTIN", "hmsCode": "597.0", "internationalCode": "597.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "PROLACTIN", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 500.0, "test_profile": "PROLACTIN", "reference_range": "", "result_unit": "ng/ml", "decimals": 2}, {"id": 112, "department": "Immunology", "testName": "PROTEIN-C Activity", "emrClassification": "test", "shortName": "PROTEIN-C Activity", "displayName": "PROTEIN-C Activity", "hmsCode": "646.0", "internationalCode": "646.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "PROTEIN-C Activity", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2600.0, "test_profile": "PROTEIN-C Activity", "reference_range": "65 - 140", "result_unit": "%", "decimals": 0}, {"id": 113, "department": "Immunology", "testName": "PROTEIN-C Antigen", "emrClassification": "test", "shortName": "PROTEIN-C Antigen", "displayName": "PROTEIN-C Antigen", "hmsCode": "598.0", "internationalCode": "598.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "PROTEIN-C Antigen", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 4500.0, "test_profile": "PROTEIN-C Antigen", "reference_range": "70 - 140", "result_unit": "%", "decimals": 0}, {"id": 114, "department": "Immunology", "testName": "PROTEIN-S Activity", "emrClassification": "test", "shortName": "PROTEIN-S Activity", "displayName": "PROTEIN-S Activity", "hmsCode": "647.0", "internationalCode": "647.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "PROTEIN-S Activity", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2600.0, "test_profile": "PROTEIN-S Activity", "reference_range": "55-123", "result_unit": "%", "decimals": 0}, {"id": 115, "department": "Immunology", "testName": "PROTEIN-S Free Antigen", "emrClassification": "test", "shortName": "PROTEIN-S Free Antig", "displayName": "PROTEIN-S Free Antigen", "hmsCode": "599.0", "internationalCode": "599.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "PROTEIN-S Free Antigen", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 5000.0, "test_profile": "PROTEIN-S Free Antigen", "reference_range": "60 - 150", "result_unit": "%", "decimals": 0}, {"id": 116, "department": "Immunology", "testName": "PSA", "emrClassification": "test", "shortName": "PSA", "displayName": "PSA", "hmsCode": "600.0", "internationalCode": "600.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "This assay, a quantitative in vitro diagnostic test for total (free + complexed) prostate-specific antigen (tPSA) in human serum and plasma, is indicated for the measurement of total PSA in conjunction with digital rectal examination (DRE) as an aid in the detection of prostate cancer in men aged 50 years or older. Prostate biopsy is required for diagnosis of prostate cancer. The test is further indicated for serial measurement of tPSA to aid in the management of cancer patients.The PSA test may give false-positive or false-negative results due to various factors. Rigorous physical activity affecting the prostate, such as bicycle riding, may cause a temporary rise in PSA level. Ejaculation within 24 hours of testing can be associated with elevated PSA levels and should be avoided. Large doses of some chemotherapeutic drugs, such as cyclophosphamide and methotrexate, may increase or decrease PSA levels. An inflammation or trauma of the prostate (e.g. in cases of urinary retention or following rectal examination, cystoscopy, coloscopy, transurethral biopsy, laser treatment or ergometry) can lead to PSA elevations of varying duration and magnitude.", "instructions": "This assay, a quantitative in vitro diagnostic test for total (free + complexed) prostate-specific antigen (tPSA) in human serum and plasma, is indicated for the measurement of total PSA in conjunction with digital rectal examination (DRE) as an aid in the detection of prostate cancer in men aged 50 years or older. Prostate biopsy is required for diagnosis of prostate cancer. The test is further indicated for serial measurement of tPSA to aid in the management of cancer patients.The PSA test may give false-positive or false-negative results due to various factors. Rigorous physical activity affecting the prostate, such as bicycle riding, may cause a temporary rise in PSA level. Ejaculation within 24 hours of testing can be associated with elevated PSA levels and should be avoided. Large doses of some chemotherapeutic drugs, such as cyclophosphamide and methotrexate, may increase or decrease PSA levels. An inflammation or trauma of the prostate (e.g. in cases of urinary retention or following rectal examination, cystoscopy, coloscopy, transurethral biopsy, laser treatment or ergometry) can lead to PSA elevations of varying duration and magnitude.", "specialReport": "", "reportName": "PSA", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 600.0, "test_profile": "PSA", "reference_range": "", "result_unit": "ng/ml", "decimals": 2}, {"id": 117, "department": "Immunology", "testName": "Renin Activity (PRA)", "emrClassification": "test", "shortName": "Renin Activity (PRA)", "displayName": "Renin Activity (PRA)", "hmsCode": "672.0", "internationalCode": "672.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Renin Activity (PRA)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 3500.0, "test_profile": "Renin Activity (PRA)", "reference_range": "Lying Position: 0.15-2.33 Standing Position: 0.10-6.56", "result_unit": "ng/ml/hr", "decimals": 2}, {"id": 118, "department": "Immunology", "testName": "Serotonin (5-Hydroxy Tryptamine)", "emrClassification": "test", "shortName": "Serotonin (5-Hydroxy", "displayName": "Serotonin (5-Hydroxy Tryptamine)", "hmsCode": "701.0", "internationalCode": "701.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Serotonin (5-Hydroxy Tryptamine)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 5000.0, "test_profile": "Serotonin (5-Hydroxy Tryptamine)", "reference_range": "70 - 270", "result_unit": "ng/ml", "decimals": 0}, {"id": 119, "department": "Immunology", "testName": "SEX HORMONE BINDING GL<PERSON><PERSON><PERSON>LIN", "emrClassification": "test", "shortName": "SEX HORMONE BINDING ", "displayName": "SEX HORMONE BINDING GL<PERSON><PERSON><PERSON>LIN", "hmsCode": "611.0", "internationalCode": "611.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "SEX HORMONE BINDING GL<PERSON><PERSON><PERSON>LIN", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 600.0, "test_profile": "SEX HORMONE BINDING GL<PERSON><PERSON><PERSON>LIN", "reference_range": "Males  20-49 years    : 18.3 - 54.1 Above 50 years : 20.6 - 76.7 Females 20-49 years    : 32.4 - 128 Above 50 years : 27.1 - 128", "result_unit": "nmol/L", "decimals": 1}, {"id": 120, "department": "Immunology", "testName": "Tacrolimus - Blood", "emrClassification": "test", "shortName": "Tacrolimus - Blood", "displayName": "Tacrolimus - Blood", "hmsCode": "678.0", "internationalCode": "678.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Tacrolimus - Blood", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 3000.0, "test_profile": "Tacrolimus - Blood", "reference_range": "", "result_unit": "ng/ml", "decimals": 0}, {"id": 121, "department": "Immunology", "testName": "Testosterone, Total", "emrClassification": "test", "shortName": "Testosterone, Total", "displayName": "Testosterone, Total", "hmsCode": "616.0", "internationalCode": "616.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Testosterone, Total", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 800.0, "test_profile": "Testosterone, Total", "reference_range": "", "result_unit": "ng/ml", "decimals": 3}, {"id": 122, "department": "Immunology", "testName": "5-HIAA (Hydroxy Indole Acetic Acid), Urine", "emrClassification": "test", "shortName": "5-HIAA (Hydroxy Indo", "displayName": "5-HIAA (Hydroxy Indole Acetic Acid), Urine", "hmsCode": "13.0", "internationalCode": "13.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "5-HIAA (Hydroxy Indole Acetic Acid), Urine", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 4000.0, "test_profile": "5-HIAA (Hydroxy Indole Acetic Acid), Urine", "reference_range": "2 TO 10 Years : Less than 12 >10 Years     : Less than 10", "result_unit": "mg/g creatinine", "decimals": 2}, {"id": 123, "department": "Immunology", "testName": "THYROGLOBULIN", "emrClassification": "test", "shortName": "THYROGLOBULIN", "displayName": "THYROGLOBULIN", "hmsCode": "619.0", "internationalCode": "619.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "THYROGLOBULIN", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 800.0, "test_profile": "THYROGLOBULIN", "reference_range": "UPTO 55", "result_unit": "ng/ml", "decimals": 2}, {"id": 124, "department": "Immunology", "testName": "Thyroid peroxidase Antibody (Anti-TPO/ATMA)", "emrClassification": "test", "shortName": "Thyroid peroxidase A", "displayName": "Thyroid peroxidase Antibody (Anti-TPO/ATMA)", "hmsCode": "514.0", "internationalCode": "514.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Thyroid peroxidase Antibody (Anti-TPO/ATMA)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 900.0, "test_profile": "Thyroid peroxidase Antibody (Anti-TPO/ATMA)", "reference_range": "", "result_unit": "IU/ml", "decimals": 2}, {"id": 125, "department": "Immunology", "testName": "Thyroid Stimulating Hormone (TSH)", "emrClassification": "test", "shortName": "Thyroid Stimulating ", "displayName": "Thyroid Stimulating Hormone (TSH)", "hmsCode": "623.0", "internationalCode": "623.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "Note: TSH has a diurnal rhythm, peaks at 2.00-4.00 am and has lowest level at 5.00-6.00 pm with ultradian variation. Hence thyroid test is only a snapshot of what is occurring within a dynamic system and for treatment purpose, the results should be accessed in conjugation with patient medical history, clinical examination & other tests/finding for confirmation. Many multivitamins (such as Vit B7), supplements (especially hair, skin, and nail) and over-the-counter and prescription medications may affect thyroid test results, and their use should be discussed with the healthcare practitioner prior to testing.  \nWhen a high serum TSH concentration and normal free T4 is found, repeat measurement 3-6 months later along with thyroid antibodies after excluding nonthyroidal illness and drug interference is recommended.", "instructions": "Note: TSH has a diurnal rhythm, peaks at 2.00-4.00 am and has lowest level at 5.00-6.00 pm with ultradian variation. Hence thyroid test is only a snapshot of what is occurring within a dynamic system and for treatment purpose, the results should be accessed in conjugation with patient medical history, clinical examination & other tests/finding for confirmation. Many multivitamins (such as Vit B7), supplements (especially hair, skin, and nail) and over-the-counter and prescription medications may affect thyroid test results, and their use should be discussed with the healthcare practitioner prior to testing.  \nWhen a high serum TSH concentration and normal free T4 is found, repeat measurement 3-6 months later along with thyroid antibodies after excluding nonthyroidal illness and drug interference is recommended.", "specialReport": "", "reportName": "Thyroid Stimulating Hormone (TSH)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 200.0, "test_profile": "Thyroid Stimulating Hormone (TSH)", "reference_range": "", "result_unit": "ulU/ml", "decimals": 2}, {"id": 126, "department": "Immunology", "testName": "Thyroxine Binding Globulin (TBG)", "emrClassification": "test", "shortName": "Thyroxine Binding Gl", "displayName": "Thyroxine Binding Globulin (TBG)", "hmsCode": "1330.0", "internationalCode": "1330.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Thyroxine Binding Globulin (TBG)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 6000.0, "test_profile": "Thyroxine Binding Globulin (TBG)", "reference_range": "13 - 39", "result_unit": "ug/ml", "decimals": 2}, {"id": 127, "department": "Immunology", "testName": "Thyroxine Total (TT4)", "emrClassification": "test", "shortName": "Thyroxine Total (TT4", "displayName": "Thyroxine Total (TT4)", "hmsCode": "615.0", "internationalCode": "615.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Thyroxine Total (TT4)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 150.0, "test_profile": "Thyroxine Total (TT4)", "reference_range": "3.2 - 12.6 (Overall Range)", "result_unit": "ug/dl", "decimals": 2}, {"id": 130, "department": "Immunology", "testName": "TNF (Tumour Necrosis Factor) Alpha", "emrClassification": "test", "shortName": "TNF (Tumour Necrosis", "displayName": "TNF (Tumour Necrosis Factor) Alpha", "hmsCode": "1360.0", "internationalCode": "1360.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "TNF (Tumour Necrosis Factor) Alpha", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 4400.0, "test_profile": "TNF (Tumour Necrosis Factor) Alpha", "reference_range": "< 8.10", "result_unit": "pg/ml", "decimals": 0}, {"id": 131, "department": "Immunology", "testName": "Transferrin", "emrClassification": "test", "shortName": "Transferrin", "displayName": "Transferrin", "hmsCode": "622.0", "internationalCode": "622.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Transferrin", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1100.0, "test_profile": "Transferrin", "reference_range": "190.00 - 375.00", "result_unit": "mg/dL", "decimals": 1}, {"id": 132, "department": "Immunology", "testName": "Tri-Iodothyronine Total (TT3)", "emrClassification": "test", "shortName": "Tri-Iodothyronine To", "displayName": "Tri-Iodothyronine Total (TT3)", "hmsCode": "614.0", "internationalCode": "614.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Tri-Iodothyronine Total (TT3)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 150.0, "test_profile": "Tri-Iodothyronine Total (TT3)", "reference_range": "", "result_unit": "ng/ml", "decimals": 2}, {"id": 133, "department": "Immunology", "testName": "TSH Receptor antibody", "emrClassification": "test", "shortName": "TSH Receptor antibod", "displayName": "TSH Receptor antibody", "hmsCode": "660.0", "internationalCode": "660.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "TSH Receptor antibody", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 3000.0, "test_profile": "TSH Receptor antibody", "reference_range": "<1.22", "result_unit": "IU/L", "decimals": 0}, {"id": 134, "department": "Immunology", "testName": "VALPROIC ACID", "emrClassification": "test", "shortName": "VALPROIC ACID", "displayName": "VALPROIC ACID", "hmsCode": "626.0", "internationalCode": "626.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "VALPROIC ACID", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 600.0, "test_profile": "VALPROIC ACID", "reference_range": "Therapeutic Range : 50 - 100 Toxic Range       : >100.0", "result_unit": "ug/ml", "decimals": 2}, {"id": 135, "department": "Immunology", "testName": "VALPROIC ACID (F)", "emrClassification": "test", "shortName": "VALPROIC ACID (F)", "displayName": "VALPROIC ACID (F)", "hmsCode": "627.0", "internationalCode": "627.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "VALPROIC ACID (F)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 600.0, "test_profile": "VALPROIC ACID (F)", "reference_range": "Therapeutic Range : 50 - 100 Toxic Range       : >100.0", "result_unit": "ug/ml", "decimals": 2}, {"id": 136, "department": "Immunology", "testName": "Vancomycin", "emrClassification": "test", "shortName": "Vancomycin", "displayName": "Vancomycin", "hmsCode": "1507.0", "internationalCode": "1507.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Vancomycin", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 0, "test_profile": "Vancomycin", "reference_range": "Peak  : 20 - 40 Trough: 05 - 10", "result_unit": "ug/ml", "decimals": 2}, {"id": 137, "department": "Immunology", "testName": "VITAMIN  A (Retinol)", "emrClassification": "test", "shortName": "VITAMIN  A (Retinol)", "displayName": "VITAMIN  A (Retinol)", "hmsCode": "628.0", "internationalCode": "628.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "VITAMIN  A (Retinol)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 0, "test_profile": "VITAMIN  A (Retinol)", "reference_range": "0.3 - 0.6", "result_unit": "mg/L", "decimals": 2}, {"id": 138, "department": "Immunology", "testName": "VITAMIN  B1, (THIAMINE)", "emrClassification": "test", "shortName": "VITAMIN  B1, (THIAMI", "displayName": "VITAMIN  B1, (THIAMINE)", "hmsCode": "689.0", "internationalCode": "689.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "VITAMIN  B1, (THIAMINE)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 0, "test_profile": "VITAMIN  B1, (THIAMINE)", "reference_range": "28 - 85", "result_unit": "ug/L", "decimals": 2}, {"id": 139, "department": "Immunology", "testName": "VITAMIN  K", "emrClassification": "test", "shortName": "VITAMIN  K", "displayName": "VITAMIN  K", "hmsCode": "1587.0", "internationalCode": "1587.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "VITAMIN  K", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 0, "test_profile": "VITAMIN  K", "reference_range": "0.13 - 1.19", "result_unit": "ng/ml", "decimals": 2}, {"id": 140, "department": "Immunology", "testName": "VITAMIN B 12", "emrClassification": "test", "shortName": "VITAMIN B 12", "displayName": "VITAMIN B 12", "hmsCode": "629.0", "internationalCode": "629.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "Note: Vitamin B12 or cyanocobalamin, is a complex corrinoid compound found exclusively from animal dietary sources, such as meat, eggs and milk. It is critical in normal DNA synthesis, which in turn affects erythrocyte maturation and in the formation of myelin sheath.Vitamin-B12 is used to find out neurological abnormalities and impaired DNA synthesis associated with macrocytic anemias. The test results should always be assessed in conjunction with the patients medical history, clinical examination and other findings.", "instructions": "Note: Vitamin B12 or cyanocobalamin, is a complex corrinoid compound found exclusively from animal dietary sources, such as meat, eggs and milk. It is critical in normal DNA synthesis, which in turn affects erythrocyte maturation and in the formation of myelin sheath.Vitamin-B12 is used to find out neurological abnormalities and impaired DNA synthesis associated with macrocytic anemias. The test results should always be assessed in conjunction with the patients medical history, clinical examination and other findings.", "specialReport": "", "reportName": "VITAMIN B 12", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 1200.0, "test_profile": "VITAMIN B 12", "reference_range": "", "result_unit": "pg/ml", "decimals": 0}, {"id": 141, "department": "Immunology", "testName": "Vitamin B2 (Riboflavin)", "emrClassification": "test", "shortName": "Vitamin B2 (R<PERSON><PERSON><PERSON>", "displayName": "Vitamin B2 (Riboflavin)", "hmsCode": "1362.0", "internationalCode": "1362.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Vitamin B2 (Riboflavin)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 3000.0, "test_profile": "Vitamin B2 (Riboflavin)", "reference_range": "180 - 295", "result_unit": "ug/L", "decimals": 2}, {"id": 142, "department": "Immunology", "testName": "Vitamin B6,  (Pyridoxine)", "emrClassification": "test", "shortName": "Vitamin B6,  (Pyrido", "displayName": "Vitamin B6,  (Pyridoxine)", "hmsCode": "687.0", "internationalCode": "687.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "Vitamin B6,  (Pyridoxine)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 0, "test_profile": "Vitamin B6,  (Pyridoxine)", "reference_range": "8.7-27.2", "result_unit": "ng/ml", "decimals": 0}, {"id": 143, "department": "Immunology", "testName": "VITAMIN C (ASCORBIC ACID)", "emrClassification": "test", "shortName": "VITAMIN C (ASCORBIC ", "displayName": "VITAMIN C (ASCORBIC ACID)", "hmsCode": "680.0", "internationalCode": "680.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "VITAMIN C (ASCORBIC ACID)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2500.0, "test_profile": "VITAMIN C (ASCORBIC ACID)", "reference_range": "2.0 - 14.0", "result_unit": "mg/L", "decimals": 2}, {"id": 144, "department": "Immunology", "testName": "VITAMIN E (Tocopherol)", "emrClassification": "test", "shortName": "VITAMIN E (Tocophero", "displayName": "VITAMIN E (Tocopherol)", "hmsCode": "631.0", "internationalCode": "631.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "VITAMIN E (Tocopherol)", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 2900.0, "test_profile": "VITAMIN E (Tocopherol)", "reference_range": "3.00 - 9.00", "result_unit": "mg/L", "decimals": 2}, {"id": 145, "department": "Immunology", "testName": "ZINC", "emrClassification": "test", "shortName": "ZINC", "displayName": "ZINC", "hmsCode": "632.0", "internationalCode": "632.0", "method": "", "primarySpecimen": "", "specimen": "", "container": "", "interpretation": "", "instructions": "", "specialReport": "", "reportName": "ZINC", "subTests": [], "unacceptableConditions": "", "minSampleQty": "", "cutoffTime": "", "testSuffix": "", "suffixDesc": "", "minProcessTime": 0, "minProcessPeriod": "", "emergencyProcessTime": 0, "emergencyProcessPeriod": "", "expiryTime": 0, "expiryPeriod": "", "serviceTime": "", "applicableTo": "both", "reportingDays": 0, "testDoneOn": {"sun": false, "mon": true, "tue": true, "wed": true, "thu": true, "fri": true, "sat": false}, "alertSMS": false, "alertPeriod": "", "alertMessage": "", "options": {"noSale": false, "inactive": false, "noBarCode": false, "allowDiscount": true, "hideOnlineReport": false, "noDiscount": false, "allowModifySpecimen": false, "editComment": false, "accreditedTest": false, "preferDoctor": false, "appointment": false, "allowNegative": false, "onlineRegistration": true, "automatedService": false, "allowIncreaseAmount": false, "noHouseVisit": false, "editBill": false, "noResult": false, "allowComma": false, "autoAuthorise": false, "isCovid": false, "noLoyalty": false, "outsourced": false, "editQuantity": false, "attachServiceDoctor": false, "noSMS": false, "noMembershipDiscount": false, "noAppDiscount": false, "printInsideBox": false}, "is_active": true, "created_at": "2025-06-19T21:43:42.040903", "updated_at": "2025-06-19T21:43:42.040903", "created_by": 1, "test_price": 450.0, "test_profile": "ZINC", "reference_range": "60 - 120", "result_unit": "ug/dL", "decimals": 1}]