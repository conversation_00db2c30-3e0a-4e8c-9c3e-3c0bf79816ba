/* Dashboard Styles */

.dashboard-container {
  padding: 1rem;
}

/* Card Styles */
.border-left-primary {
  border-left: 0.25rem solid var(--primary) !important;
}

.border-left-success {
  border-left: 0.25rem solid var(--success) !important;
}

.border-left-info {
  border-left: 0.25rem solid var(--info) !important;
}

.border-left-warning {
  border-left: 0.25rem solid var(--warning) !important;
}

.border-left-danger {
  border-left: 0.25rem solid var(--danger) !important;
}

/* Chart Area */
.chart-area {
  position: relative;
  height: 300px;
  margin: 0 auto;
}

/* Loading State */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.2rem;
  color: var(--primary);
}

/* Badge Styles */
.badge {
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 700;
  border-radius: 50rem;
}

.bg-success {
  background-color: var(--success) !important;
}

.bg-warning {
  background-color: var(--warning) !important;
}

.bg-info {
  background-color: var(--info) !important;
}

.bg-danger {
  background-color: var(--danger) !important;
}

.bg-primary {
  background-color: var(--primary) !important;
}

/* Table Styles */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-sm th,
.table-sm td {
  padding: 0.5rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .chart-area {
    height: 250px;
  }
  
  .dashboard-container h1 {
    font-size: 1.5rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .h5 {
    font-size: 1.1rem;
  }
}
