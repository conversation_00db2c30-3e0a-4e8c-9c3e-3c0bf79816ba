[{"id": "ERR_20250620_094012_15832", "timestamp": "2025-06-20T09:40:12.392987", "error_type": "test_matching_failed", "error_message": "Failed to match 5 tests", "severity": "medium", "context": {"billing_id": 1, "unmatched_tests": ["Complete Blood Count (CBC)", "HbA1c", "<PERSON><PERSON>", "HbA1c", "Lipid Profile"], "total_tests": 5, "match_rate": 0.0}, "user_id": 1, "tenant_id": 3, "resolved": false, "resolution_notes": null}, {"id": "ERR_20250620_100218_13496", "timestamp": "2025-06-20T10:02:18.171018", "error_type": "test_matching_failed", "error_message": "Failed to match 1 tests", "severity": "medium", "context": {"billing_id": 40, "unmatched_tests": [""], "total_tests": 1, "match_rate": 0.0}, "user_id": null, "tenant_id": 1, "resolved": false, "resolution_notes": null}, {"id": "ERR_20250620_100922_28116", "timestamp": "2025-06-20T10:09:22.778241", "error_type": "test_matching_failed", "error_message": "Failed to match 3 tests", "severity": "medium", "context": {"billing_id": 1, "unmatched_tests": ["HbA1c", "HbA1c", "Lipid Profile"], "total_tests": 5, "match_rate": 0.4}, "user_id": 1, "tenant_id": 3, "resolved": false, "resolution_notes": null}, {"id": "ERR_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.857845", "error_type": "test_matching_failed", "error_message": "Failed to match 1 tests", "severity": "medium", "context": {"billing_id": 32, "unmatched_tests": ["Lipid Profile"], "total_tests": 1, "match_rate": 0.0}, "user_id": 2, "tenant_id": 2, "resolved": false, "resolution_notes": null}, {"id": "ERR_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.923105", "error_type": "test_matching_failed", "error_message": "Failed to match 1 tests", "severity": "medium", "context": {"billing_id": 33, "unmatched_tests": ["Basic Health Checkup"], "total_tests": 1, "match_rate": 0.0}, "user_id": 19, "tenant_id": 1, "resolved": false, "resolution_notes": null}, {"id": "ERR_20250814_135628_25824", "timestamp": "2025-08-14T13:56:28.626299", "error_type": "test_matching_failed", "error_message": "Failed to match 1 tests", "severity": "medium", "context": {"billing_id": 86, "unmatched_tests": ["ID:374"], "total_tests": 2, "match_rate": 0.5}, "user_id": 4, "tenant_id": 1, "resolved": false, "resolution_notes": null}, {"id": "ERR_20250820_155629_69924", "timestamp": "2025-08-20T15:56:29.658719", "error_type": "test_matching_failed", "error_message": "Failed to match 1 tests", "severity": "medium", "context": {"billing_id": 91, "unmatched_tests": ["ID:f66ca840-5a91-4e9f-acf0-5b6d19a05f6d"], "total_tests": 2, "match_rate": 0.5}, "user_id": 4, "tenant_id": 1, "resolved": false, "resolution_notes": null}]