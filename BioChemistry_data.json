[{"Test Name": "17 HYDROXY CORTICO STEROID 24 HR URINE", "code": 3, "Department": "Biochemistry", "Referance Range": "< 1 Years     :  <=  1.0 (Both) 1 - 10 Years  :  3 - 6 (Both) > 10 Years    :  3 - 10 (Male)", "Result Unit": "mg/24hrs", "Price": 4000, "Result Type": "Pick List", "Short Name": "17HY", "Method code": 45, "Method": "Column Chromatography", "Specimen Code": 3, "Specimen": "24 H<PERSON> <PERSON><PERSON>", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Instructions": "10 ml of 50% HCL as a preservative, Total volume to be mentioned", "Min. Sample Qty": "50ml", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 10}, {"Test Name": "17- KETOSTEROIDS 24 hr.Urine", "code": 228, "Department": "Biochemistry", "Referance Range": "Infants        :  Upto 1.0 mg/24hrs 3-6 years      :  Upto 3.0 mg/24hrs 7-10 years     :  Upto 4.0 mg/24hrs 10-12yrs (F)   :  Upto 5 mg/24hrs Adolescent(F)  :  3-12 mg/24hrs 10-12yrs (M)   :  Upto 6 mg/24hrs Adolescent(M)  :  3-15 mg/24hrs 1-3 years      :  Upto 2.0 mg/24hrs Adult male     :   10 - 25 mg/24hrs Adult female   :   6 - 14 mg/24hrs", "Result Unit": "mg/24hrs", "Price": 4000, "Result Type": "-", "Short Name": "17KT", "Method code": 45, "Method": "Column Chromatography", "Specimen Code": 3, "Specimen": "24 H<PERSON> <PERSON><PERSON>", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Instructions": "10 ml of 50% (6M) HCL as a preservative, Total volume to be mentioned", "Min. Sample Qty": "50ml", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 10}, {"Test Name": "24 hrs URINE PROTEIN CREATININE RATIO", "code": 9, "Department": "Biochemistry", "Price": 400, "Result Type": "Pick List", "Short Name": "PCR", "Specimen Code": 56, "Specimen": "URINE", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "Instructions": "sample to be collected in Lab container(10% Thymol 5ml), mention total volume", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 4}, {"Test Name": "Acetylcholine Receptor (AChR) Antibody", "code": 15, "Department": "Biochemistry", "Referance Range": "<0.40", "Result Unit": "nmoI/L", "Price": 3000, "Result Type": "Pick List", "Short Name": "ACRA", "Method code": 50, "Method": "E.I.A", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 7}, {"Test Name": "ACID PHOSPHATASE -PROSTATIC", "code": 16, "Department": "Biochemistry", "Price": 300, "Result Type": "-", "Short Name": "ACPP", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "expect sunday", "Applicable to": "Both", "  Reporting Days": 2}, {"Test Name": "ACID PHOSPHATASE -TOTAL", "code": 210, "Department": "Biochemistry", "Referance Range": "Upto 10.0", "Result Unit": "U/L", "No of decimals": 2, "Price": 300, "Result Type": "-", "Short Name": "ACPT", "Method code": 32, "Method": "Colorimetric", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "expect sunday", "Applicable to": "Both"}, {"Test Name": "ADA", "code": 18, "Department": "Biochemistry", "Referance Range": "CSF Sample  Normal  : <10.0  Positive: >10.0    <PERSON><PERSON>, Plasma, Pleural,  Pericardil & Ascitic Fluids  Normal         : < 30  Suspect        : 30 - 40  Strong Suspect : >40 - 60   Positive       : >60", "Result Unit": "U/L", "No of decimals": 1, "Price": 800, "Result Type": "-", "Method code": 60, "Method": "Enzymatic", "Min. Process Time": 4, "      Test Done On": "expect sunday", "Applicable to": "Both"}, {"Test Name": "ADA (Adenosine Deaminase)", "code": 1300, "Department": "Biochemistry", "Price": 800, "Result Type": "Pick List", "Short Name": "ADA", "Method code": 60, "Method": "Enzymatic", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Alanine aminotransferase (ALT/SGPT)", "code": 149, "Department": "Biochemistry", "Referance Range": "Female : Less than 33\nMale  : Less than 41", "Result Unit": "U/L", "No of decimals": 2, "Price": 100, "Result Type": "-", "Short Name": "ALT", "Method code": 88, "Method": "IFCC", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Albumin", "code": 25, "Department": "Biochemistry", "Referance Range": "Adult - 3.5 - 5.2", "Result Unit": "g/dL", "No of decimals": 2, "Price": 100, "Result Type": "-", "Short Name": "Alb", "Method code": 19, "Method": "Colorimetric: Bromocresol Green (BCG)", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Albumin/Globulin", "code": 297, "Department": "Biochemistry", "Result Unit": "<PERSON><PERSON>", "No of decimals": 1, "Price": 0, "Result Type": "-", "Method code": 20, "Method": "Calculated", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ALCOHOL IN SERUM (Ethanol)", "code": 26, "Department": "Biochemistry", "Referance Range": "Not detected", "Result Unit": "mg/dL", "Price": 1000, "Result Type": "Pick List", "Short Name": "ALCO", "Method code": 63, "Method": "Enzymatic Method", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "Instructions": "DO NOT USE ANY ALCOHOL FOR <PERSON><PERSON>ANING WHILE COLLECTING BLOOD SAMPLE", "Min. Process Time": 2, "      Test Done On": "expect sunday", "Applicable to": "Both", "  Reporting Days": 2}, {"Test Name": "Aldolase", "code": 250, "Department": "Biochemistry", "Referance Range": "<7.40", "Result Unit": "U/L", "Price": 900, "Result Type": "-", "Short Name": "ALDO", "Method code": 60, "Method": "Enzymatic", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 7, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Alkaline phosphatase", "code": 27, "Department": "Biochemistry", "Referance Range": "Children : 47 - 406 (Age and gender dependent)  Adults   : 80 - 306", "Result Unit": "U/L", "No of decimals": 1, "Price": 100, "Result Type": "-", "Short Name": "ALP", "Method code": 38, "Method": "PNPP-DGKC", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Amino leveulinic acid (ALA)", "code": 245, "Department": "Biochemistry", "Referance Range": "1 - 7", "Result Unit": "mg/dL", "No of decimals": 2, "Price": 1300, "Result Type": "-", "Method code": 45, "Method": "Column Chromatography", "Specimen Code": 45, "Specimen": "SPOT URINE", "Min. Process Time": 2, "      Test Done On": "expect saturday", "Applicable to": "Both"}, {"Test Name": "AMMONIA", "code": 28, "Department": "Biochemistry", "Referance Range": "Male   : 16 - 60 Female : 11 - 51", "Result Unit": "umoI/L", "No of decimals": 1, "Price": 600, "Result Type": "-", "Short Name": "NH3", "Method code": 40, "Method": "Colorimetric", "Specimen Code": 18, "Specimen": "EDTA PLASMA", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 24, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON>", "code": 29, "Department": "Biochemistry", "Referance Range": "Adult : 28 - 100", "Result Unit": "U/L", "No of decimals": 1, "Price": 450, "Result Type": "-", "Short Name": "AMY", "Method code": 206, "Method": "CNPG3", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "AMYLASE  (Body Fluid)", "code": 33, "Department": "Biochemistry", "Price": 400, "Result Type": "Pick List", "Short Name": "AMY", "Specimen Code": 11, "Specimen": "Body Fluids", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Amyloid A", "code": 1594, "Department": "Biochemistry", "Referance Range": "Less than 6.4", "Result Unit": "mg/L", "No of decimals": 2, "Price": 4150, "Result Type": "Numeric", "Short Name": "AMYA", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 7}, {"Test Name": "Angiotensin Converting enzyme (ACE)", "code": 295, "Department": "Biochemistry", "Notes": "• Elevated levels of ACE acti vity is seen in pati ents with acti ve sarcoidosis, tuberculosis, <PERSON><PERSON><PERSON>’s disease, leprosy and\noccasionally in premature infants with respiratory distress syndrome.\nClinical Uti lity:\n• Measurement of ACE levels aid in the diagnosis and in management of sarcoidosis.\nNote:\n•\nACE activity is inhibited by EDTA and by heavy metal ions that may serve to replace the zinc ion of the enzyme.\n• Upon administration of the angiotensin-converting enzyme inhibitory drug, ACE serum activity is markedly reduced but\nusually returns to normal levels in about 12 hours.", "Referance Range": "Serum : 8 - 52", "Result Unit": "U/L", "No of decimals": 2, "Price": 1200, "Result Type": "Numeric", "Short Name": "ACE", "Method code": 131, "Method": "Spectrophotometry", "Specimen Code": 48, "Specimen": "Serum/CSF", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Anion gap", "code": 1329, "Department": "Biochemistry", "Referance Range": "10 - 20", "Result Unit": "mmoI/L", "No of decimals": 1, "Price": 420, "Result Type": "Numeric", "Method code": 20, "Method": "Calculated", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "APOLIPOPROTEIN A1", "code": 30, "Department": "Biochemistry", "Referance Range": "MALE   : 104 - 202 FEMALE : 108 - 225", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 700, "Result Type": "Numeric", "Short Name": "APOA", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "APOLIPOPROTEIN B", "code": 31, "Department": "Biochemistry", "Referance Range": "Male : 55 - 140 Female : 55 - 125", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 700, "Result Type": "Numeric", "Short Name": "APOB", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Gamma Glutamyl-Transferase (GGT)", "code": 79, "Department": "Biochemistry", "Result Unit": "U/L", "No of decimals": 2, "Short Name": "GGT", "Method code": 36, "Method": "Carboxy Substrate", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Aspartate aminotransferase (AST/SGOT)", "code": 148, "Department": "Biochemistry", "Result Unit": "U/L", "No of decimals": 2, "Price": 100, "Result Type": "Numeric", "Short Name": "AST", "Method code": 88, "Method": "IFCC", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "BENZODIAZAPINE", "code": 247, "Department": "Biochemistry", "Referance Range": "NEGATIVE : <200 POSITIVE : >=200", "Result Unit": "ng/ml", "Price": 600, "Result Type": "Pick List", "Method code": 90, "Method": "Immuno Chromatography", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Bicarbonate", "code": 38, "Department": "Biochemistry", "Referance Range": "Adult : 23 - 30 >60y : 23- 31 >90y : 20-29", "Result Unit": "mmoI/L", "No of decimals": 1, "Price": 150, "Result Type": "Numeric", "Short Name": "Co2", "Method code": 109, "Method": "Manual : Phosphoenol Pyruvate Carboxylase", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Bile acids - Total", "code": 271, "Department": "Biochemistry", "Referance Range": "0.5 - 10.0", "Result Unit": "umoI/L", "No of decimals": 2, "Price": 2000, "Result Type": "Numeric", "Short Name": "BILE", "Method code": 60, "Method": "Enzymatic", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 7, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON><PERSON><PERSON>, Direct", "code": 217, "Department": "Biochemistry", "Referance Range": "0.0 - 0.2", "Result Unit": "mg/dL", "No of decimals": 2, "Price": 150, "Result Type": "Numeric", "Short Name": "BilD", "Method code": 14, "Method": "DCA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Bilirubin, Indirect", "code": 231, "Department": "Biochemistry", "Referance Range": "0.1 - 1.0", "Result Unit": "mg/dL", "No of decimals": 2, "Price": 0, "Result Type": "Calculated", "Short Name": "BilID", "Method code": 20, "Method": "Calculated", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON><PERSON><PERSON>, Total", "code": 216, "Department": "Biochemistry", "Referance Range": "Premature:  Cord       : < 2.0 0 - 1 day  :<8.0 1 - 2 days : <12.0 3 - 5 days : <16.0  Full term:  Cord       : < 2.0 0 - 1 day  : 1.4 - 8.7 1 - 2 days : 3.4 - 11.5 3 - 5 days : 1.5 - 12  >5 days - 60y      : 0.3 - 1.2 60 - 90 y : 0.2 - 1.1 >90y : 0.2 - 0.9", "Result Unit": "mg/dL", "No of decimals": 2, "Price": 150, "Result Type": "Pick List", "Short Name": "BilT", "Method code": 14, "Method": "DCA", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Biotinidase", "code": 281, "Department": "Biochemistry", "Result Unit": "nmoI/min/ML", "Price": 3500, "Method code": 60, "Method": "Enzymatic", "Specimen Code": 9, "Specimen": "BLOOD", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Blood Urea Nitrogen (BUN)", "code": 419, "Department": "Biochemistry", "Referance Range": "6.9-18.0", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 80, "Result Type": "Numeric", "Method code": 60, "Method": "Enzymatic", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "BUN", "code": 215, "Department": "Biochemistry", "Referance Range": "6.9-18.0", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 100, "Result Type": "Numeric", "Short Name": "<PERSON>un", "Method code": 60, "Method": "Enzymatic", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "BUN - URINE", "code": 1703, "Department": "Biochemistry", "Price": 200, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Bun/Creatinine", "code": 1364, "Department": "Biochemistry", "Price": 1, "Result Type": "Calculated", "Method code": 20, "Method": "Calculated", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Calcium", "code": 208, "Department": "Biochemistry", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 150, "Result Type": "Numeric", "Short Name": "Ca", "Method code": 59, "Method": "End point : Arsenazo III", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Calcium -  Urine", "code": 179, "Department": "Biochemistry", "Result Unit": "mg/dL", "Price": 150, "Result Type": "Pick List", "Method code": 190, "Method": "Colorimetric : 5-nitro-5’-methyl-BAPTA", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Calcium, Urine 24Hr", "code": 40, "Department": "Biochemistry", "Price": 200, "Result Type": "-", "Method code": 99, "Method": "Ion Selective Electrode", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Instructions": "Sample to be collected in Lab container(10 % Thymol 5ml), mention total volume", "Min. Process Time": 6, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Cannabis (Marijuana).", "code": 275, "Department": "Biochemistry", "Referance Range": "Negative : < 50", "Result Unit": "ng/ml", "Price": 800, "Result Type": "Pick List", "Short Name": "CAN", "Method code": 80, "Method": "IC", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Calculus Study", "code": 282, "Price": 650, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": 41, "Department": "Biochemistry", "Referance Range": "0.2 - 0.6", "Result Unit": "g/L", "No of decimals": 3, "Price": 900, "Result Type": "-", "Short Name": "CER", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 6, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CHLORIDE - PLEURAL  FLUID", "code": 45, "Department": "Biochemistry", "Result Unit": "mEq/L", "Price": 200, "Result Type": "Pick List", "Method code": 191, "Method": "ISE Indirect", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CHLORIDE SYNOVIAL FLUID", "code": 48, "Department": "Biochemistry", "Result Unit": "mEq/L", "Price": 200, "Result Type": "Pick List", "Method code": 191, "Method": "ISE Indirect", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Chloride, CSF", "code": 43, "Department": "Biochemistry", "Referance Range": "115 - 130", "Result Unit": "mmoI/L", "Price": 250, "Result Type": "-", "Short Name": "Cl", "Method code": 99, "Method": "Ion Selective Electrode", "Specimen Code": 13, "Specimen": "Cerebrospinal fluid", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Chloride, Urine", "code": 230, "Department": "Biochemistry", "Referance Range": "Adult (< 40 Yr) Male  : 27 - 371 Female: 20 - 295  Adult (= 40 Yr) Male  : 30 - 260 Female: 24 - 225", "Result Unit": "mmoI/L", "Price": 250, "Result Type": "Pick List", "Method code": 99, "Method": "Ion Selective Electrode", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Chloride, Urine 24 Hrs", "code": 44, "Department": "Biochemistry", "Price": 250, "Result Type": "-", "Method code": 99, "Method": "Ion Selective Electrode", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Instructions": "Sample to be collected in Lab container(10 % Thymol 5ml), mention total volume", "Min. Process Time": 6, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Chloride", "code": 207, "Department": "Biochemistry", "Referance Range": "Cord       : 96 - 104 Premature  : 95 - 110 0 - 30 days: 98 - 113 Adult      : 98 - 107 >90 years  : 98 - 111", "Result Unit": "mmoI/L", "Price": 250, "Result Type": "-", "Short Name": "Cl", "Method code": 99, "Method": "Ion Selective Electrode", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Cholesterol, HDL", "code": 87, "Department": "Biochemistry", "Referance Range": "Low  : < 40 High : >=60", "Result Unit": "mg/dL", "No of decimals": 1, "Critical Low": 15, "   Critical High": 80, "Price": 150, "Result Type": "-", "Short Name": "HDL", "Method code": 49, "Method": "Direct", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Cholesterol, LDL", "code": 100, "Department": "Biochemistry", "Referance Range": "Children (ACC 2018) Acceptable: <110 Borderline: 110-129 Abnormal  : >=130  Adult (NCEP ATP-III) Optimal               : <100 Near or above optimal : 100 - 129 Borderline high       : 130 - 159 High                  : 160 - 189 Very high             : >=190", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 200, "Result Type": "-", "Short Name": "LDL", "Method code": 49, "Method": "Direct", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Cholesterol, LDL (Direct)", "code": 242, "Department": "Biochemistry", "Referance Range": "Optimal               : <100 Near or above optimal : 100 - 129 Borderline high       : 130 - 159 High                  : 160 - 189 Very high             : >190", "Result Unit": "mg/dL", "No of decimals": 2, "Price": 200, "Result Type": "-", "Short Name": "LDLD", "Method code": 49, "Method": "Direct", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON><PERSON><PERSON>, Total", "code": 50, "Department": "Biochemistry", "Notes": "Note: Above Biological interval  is based on 9 to 12 hours fasting.", "Referance Range": "CHILDREN (ACC 2018) Acceptable: <170 Borderline: 170-199 Abnormal  : >=200  ADULT (NCEP ATP-III) Desirable          : <200 Borderline high : 200 - 239 High                : >239", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 80, "Result Type": "-", "Short Name": "Chol", "Method code": 61, "Method": "Enzymatic : CHOD-PAP", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Cholesterol, VLDL", "code": 203, "Department": "Biochemistry", "Referance Range": "Less than 30  (NCEP ATP-III)", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 0, "Result Type": "-", "Short Name": "VLDL", "Method code": 21, "Method": "Calculation", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Cholesterol/HDL Ratio", "code": 298, "Department": "Biochemistry", "Referance Range": "Castelli's Risk Index -I Ideal : <3.5 Good: 3.5-5.0 High: >=5", "No of decimals": 1, "Price": 0, "Result Type": "-", "Method code": 21, "Method": "Calculation", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Cholinesterase", "code": 49, "Department": "Biochemistry", "Referance Range": "2180 - 9180", "Result Unit": "U/L", "Critical Low": 1500, "   Critical High": 20000, "Price": 700, "Result Type": "-", "Short Name": "CHE", "Method code": 60, "Method": "Enzymatic", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Chylomicron, Qualitative", "code": 285, "Department": "Biochemistry", "Referance Range": "NEGATIVE", "Price": 500, "Result Type": "Pick List", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Citrate, Urine 24 Hrs", "code": 290, "Department": "Biochemistry", "Price": 1000, "Result Type": "Pick List", "Short Name": "CI24", "Specimen Code": 3, "Specimen": "24 H<PERSON> <PERSON><PERSON>", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CK-MB", "code": 1645, "Department": "Biochemistry", "Notes": "MB is the myocardial fraction associated with MI and occurs in certain other states. MB can be used in estimation of infarct size. MB increases have been reported with entities which cause damage to the myocardium, such as myocarditis, some instances of cardiomyopathy, and with extensive rhabdomyolysis, Duchenne muscular dystrophy, malignant hyperthermia, polymyositis, dermatomyositis, mixed connective tissue disease, myoglobinemia, Rocky Mountain spotted fever, <PERSON><PERSON> syndrome, and rarely in rheumatoid arthritis with high titer RF.2 CK-MB does not generally abruptly rise and fall in such nonacute MI settings, as it does in acute myocardial infarct (AMI).\n\n\n\nLIMITATION:- Triglycerides >300 mg/dL will cause >20% loss of CK-MB activity. Exercise, intramuscular injections, myxedema, grand mal seizures, prior trauma or surgery and acute MI very early or late lead to the combination of increased total CK but usually normal CK-MB. Increased CK-MB has been described in marathon runners without MI.3 CK isoenzyme analysis is not usually practical when the total CK is very low, although in elderly people with low muscle mass, the use of sensitive mass concentration assays may be useful. A single CK isoenzyme examination may be misleading. One should look for a pattern in serial CK isoenzyme analyses and seek confirmation with the isoenzymes of LD (LDH), ideally beginning with onset to establish the baseline. LD isoenzyme 1:2 flip is most consistently found about two days after onset of acute infarction of myocardium. The diagnosis of myocardial injury should not be based solely on MB isoenzyme, but rather should be supported by clinical findings, ECG, and often other laboratory parameters (ie, confirmation by LD isoenzymes). \n\n\n\nADDITIONAL INFORMATION:- CK-MB is usually not elevated in exercise (total CK elevated); myxedema (total CK elevated in about half of cases); injections into muscle (total CK elevated); strokes, CVA, and other brain disorders in which total CK may be increased; pericarditis; pneumonias or other lung diseases; pulmonary embolus; seizures (CK may be very high but no great MB increase, if any). Although CK-MB is not usually increased in angina, some CK-MB elevations are recognized in angina patients, depending partly on laboratory methodology.", "Result Unit": "U/L", "No of decimals": 1, "Price": 600, "Result Type": "-", "Short Name": "CK MB", "Primary specimen code": 39, "Primary specimen ": "SERUM", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CLOZAPINE WITH NOR-CLOZAPINE", "code": 1432, "Department": "Biochemistry", "Price": 4000, "Result Type": "Template", "Method code": 105, "Method": "LC-MS/MS", "Specimen Code": 39, "Specimen": "SERUM", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "COMPLEMENT C3", "code": 52, "Department": "Biochemistry", "Referance Range": "90 - 180", "Result Unit": "mg/dL", "No of decimals": 2, "Price": 600, "Result Type": "-", "Short Name": "C3", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 24, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "COMPLEMENT C4", "code": 53, "Department": "Biochemistry", "Referance Range": "9.0 - 36.0", "Result Unit": "mg/dL", "No of decimals": 2, "Price": 600, "Result Type": "-", "Short Name": "C4", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 24, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CPK", "code": 54, "Department": "Biochemistry", "Referance Range": "MALE   : 46 - 171 FEMALE : 35 - 145", "Result Unit": "U/L", "No of decimals": 2, "Price": 450, "Result Type": "-", "Method code": 88, "Method": "IFCC", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CREATININE CLEARENCE- 24 HR URINE", "code": 58, "Department": "Biochemistry", "Price": 250, "Result Type": "Pick List", "Min. Process Time": 6, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "C<PERSON><PERSON>ine, Urine", "code": 182, "Department": "Biochemistry", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 150, "Result Type": "-", "Method code": 63, "Method": "Enzymatic Method", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "C<PERSON><PERSON><PERSON>, Urine 24Hr", "code": 225, "Department": "Biochemistry", "Price": 250, "Result Type": "Pick List", "Min. Process Time": 6, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Creatinine", "code": 57, "Department": "Biochemistry", "Referance Range": "0.67 - 1.17 (SYS-400 OF DIASYS)", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 100, "Result Type": "-", "Short Name": "Cr", "Method code": 60, "Method": "Enzymatic", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CRP", "code": 1103, "Department": "Biochemistry", "Notes": "Note:The CRP response frequently precedes clinical symptoms such as infections, inflammatory diseases and malignant neoplasms. Alterations are detectable within 6 to 8 hours and the peak value is reached within 24 to 48 hours. Levels of up to thousandfold the normal value are associated with severe stimuli such as myocardial infarction, major trauma, surgery, or malignant neoplasms.\n\nCRP levels can be elevated in the later stages of pregnancy, birth control pills or hormone replacement therapy (i.e., estrogen) and obesity. Significantly decreased CRP values may be obtained from samples taken from patients who have been treated with carboxypenicillins. In very rare cases, gammopathy, in particular type IgM (<PERSON><PERSON><PERSON>’s macroglobulinemia) and patients who have been treated or recieved monoclonal mouse antibodies, may cause unreliable results.  CRP determination may replace the classical determination of Erythrocytes Sedimentation Rate (ESR), due to its prompt response to changes in disease activity (CRP increases sooner and then decreases more rapidly than the ESR) and its good correlation to ESR.", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 400, "Result Type": "-", "Short Name": "CRP", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CSF Index", "code": 1501, "Department": "Biochemistry", "Price": 2500, "Result Type": "Template", "Short Name": "INDEX", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 7}, {"Test Name": "Cyclosporin (C2)", "code": 274, "Department": "Biochemistry", "Result Unit": "ug/L", "No of decimals": 1, "Price": 3100, "Result Type": "-", "Method code": 29, "Method": "CMIA", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (CO)", "code": 260, "Department": "Biochemistry", "Result Unit": "ug/L", "No of decimals": 1, "Price": 3100, "Result Type": "-", "Method code": 105, "Method": "LC-MS/MS", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "CYSTATIN-C", "code": 248, "Department": "Biochemistry", "Referance Range": "0.53 - 0.95", "Result Unit": "mg/L", "No of decimals": 0, "Price": 1500, "Result Type": "-", "Short Name": "CYSC", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 7, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "D-Dimer (Quantitative)", "code": 61, "Department": "Biochemistry", "Notes": "Note\n\n1. Degree of D-dimer increase does not definitely correlate with the clinical severity of associated disease state.\n\n2. Lipemia falsely decreases D-dimer levels\n\nComments\n\nD-Dimer is one of the measurable byproducts of activation of the fibrinolytic system. It assesses fibrinolytic activation and intravascular thrombosis. D-dimer assays are characteristic for Disseminated Intravascular Coagulation (DIC) as this test demonstrates simultaneous presence of thrombin and plasmin formation. It can also be elevated in individuals with large vessel thrombosis, soft tissue hematomas, Pulmonary embolism, recent surgery, active or recent bleeding, pregnancy, liver disease, malignancy and hypercoagulable states. D-Dimer is of particular value in excluding the diagnosis of venous thromboembolism among patients at high risk.", "Result Unit": "microgm/mL", "No of decimals": 2, "Price": 800, "Result Type": "-", "Short Name": "D-Dimer", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 14, "Specimen": "Citrate Plasma", "Container Code": 8, "Container": "Citrate Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Determination of  Arsenic", "code": 940, "Department": "Biochemistry", "Referance Range": "0.4-11.9", "Result Unit": "ug/L", "No of decimals": 2, "Price": 3000, "Result Type": "-", "Method code": 81, "Method": "ICPMS", "Specimen Code": 61, "Specimen": "WHOLE BLOOD", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ELECTROPHORESIS OF CSF", "code": 73, "Department": "Biochemistry", "Price": 550, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ELECTROPHORESIS- 24 HRS URINE", "code": 74, "Department": "Biochemistry", "Price": 1350, "Result Type": "Pick List", "Short Name": "EPP24", "Method code": 179, "Method": "Gel Electrophoresis - High resolution", "Specimen Code": 3, "Specimen": "24 H<PERSON> <PERSON><PERSON>", "Container Code": 5, "Container": "Culture Container", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 5}, {"Test Name": "DRUGS OF ABUSE - 10 Drugs", "code": 1512, "Department": "Biochemistry", "Price": 4000, "Result Type": "Pick List", "Short Name": "DOA10", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "DRUGS OF ABUSE - 12 Drugs", "code": 1407, "Department": "Biochemistry", "Price": 6400, "Result Type": "Template", "Short Name": "DOA12", "Method code": 80, "Method": "IC", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 5}, {"Test Name": "DRUGS OF ABUSE - 5 Drugs", "code": 1510, "Department": "Biochemistry", "Price": 1800, "Result Type": "Pick List", "Short Name": "DOA5", "Method code": 80, "Method": "IC", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "DRUGS OF ABUSE - 9 Drugs", "code": 255, "Department": "Biochemistry", "Price": 4300, "Result Type": "Pick List", "Short Name": "DOA9", "Method code": 90, "Method": "Immuno Chromatography", "Specimen Code": 55, "Specimen": "URINE", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Electrophoresis - HB", "code": 72, "Department": "Biochemistry", "Price": 1200, "Result Type": "Pick List", "Short Name": "HBEP", "Method code": 79, "Method": "HPLC", "Specimen Code": 61, "Specimen": "WHOLE BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 5, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "ELECTROPHORESIS OF CSF", "code": 73, "Department": "Biochemistry", "Price": 550, "Result Type": "Pick List"}, {"Test Name": "ELECTROPHORESIS- 24 HRS URINE", "code": 74, "Department": "Biochemistry", "Price": 1350, "Result Type": "Pick List"}, {"Test Name": "ELECTROPHORESIS-PROTEIN", "code": 219, "Department": "Biochemistry", "Price": 400, "Result Type": "Pick List", "Short Name": "EPP", "Method code": 64, "Method": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 5, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Est. Glomerular Filtration Rate", "code": 1411, "Department": "Biochemistry", "Referance Range": ">= 90.0 : Normal 60 - 89 : Mild decrease 30 - 59 : Moderate decrease 15 - 29 : Severe decrease <15     : Kidney failure", "Result Unit": "ml/min", "No of decimals": 1, "Price": 0, "Result Type": "-", "Short Name": "egfr", "Method code": 21, "Method": "Calculation", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "EVEROLIMUS", "code": 1574, "Department": "Biochemistry", "Referance Range": "Refer Interpretation", "Result Unit": "ng/ml", "Price": 5400, "Result Type": "Pick List", "Short Name": "EVER", "Method code": 105, "Method": "LC-MS/MS", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "FDP", "code": 76, "Department": "Biochemistry", "Referance Range": "Negative", "Price": 0, "Result Type": "Pick List", "Short Name": "FDP", "Method code": 7, "Method": "Agglutination", "Specimen Code": 14, "Specimen": "Citrate Plasma", "Container Code": 8, "Container": "Citrate Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "G6PD LEVEL", "code": 356, "Department": "Biochemistry", "Price": 900, "Result Type": "-", "Short Name": "G6PD", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Gamma Glutamyl-Transferase (GGT)", "code": 79, "Department": "Biochemistry", "Result Unit": "U/L", "No of decimals": 2, "Price": 200, "Result Type": "-", "Short Name": "GGT", "Method code": 36, "Method": "Carboxy Substrate", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "GCT (100 GMS)", "code": 80, "Department": "Biochemistry", "Result Unit": "mg/dL", "Price": 150, "Result Type": "Pick List", "Short Name": "GCT (100)", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Specimen Code": 23, "Specimen": "FLURIDE PLASMA", "Container Code": 10, "Container": "<PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "GCT (50 gms)", "code": 84, "Department": "Biochemistry", "Referance Range": "< 140", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 150, "Result Type": "-", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Specimen Code": 35, "Specimen": "Plasma", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "GCT (75 gms)", "code": 284, "Department": "Biochemistry", "Price": 150, "Result Type": "-", "Short Name": "GCT", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Globulin", "code": 81, "Department": "Biochemistry", "Referance Range": "2.0-3.9", "Result Unit": "g/dL", "No of decimals": 2, "Price": 0, "Result Type": "-", "Method code": 20, "Method": "Calculated", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glomerular Filtration Rate (eGFR)", "code": 256, "Department": "Biochemistry", "Price": 250, "Result Type": "Pick List", "Short Name": "GFR", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON>", "code": 29, "Department": "Biochemistry", "Referance Range": "Adult : 28 - 100", "Result Unit": "U/L", "No of decimals": 1, "Price": 300, "Result Type": "-", "Short Name": "AMY", "Method code": 206, "Method": "CNPG3", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "APOLIPOPROTEIN A1", "code": 30, "Department": "Biochemistry", "Referance Range": "MALE   : 104 - 202 FEMALE : 108 - 225", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 300, "Result Type": "-", "Short Name": "APOA", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Alkaline phosphatase", "code": 27, "Department": "Biochemistry", "Referance Range": "Children : 47 - 406 (Age and gender dependent)  Adults   : 80 - 306", "Result Unit": "U/L", "No of decimals": 1, "Price": 300, "Result Type": "-", "Short Name": "ALP", "Method code": 38, "Method": "PNPP-DGKC", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glucose, 120 min", "code": 238, "Department": "Biochemistry", "Referance Range": "Less than 155", "Result Unit": "mg/dL", "No of decimals": 0, "Price": 20, "Result Type": "-", "Short Name": "120m", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Specimen Code": 24, "Specimen": "Fluoride", "Container Code": 10, "Container": "<PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glucose, 150 min", "code": 239, "Department": "Biochemistry", "Price": 20, "Result Type": "-", "Short Name": "150m", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Specimen Code": 24, "Specimen": "Fluoride", "Container Code": 10, "Container": "<PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glucose, 180 min", "code": 240, "Department": "Biochemistry", "Referance Range": "Less Than 140", "Result Unit": "mg/dL", "Price": 20, "Result Type": "-", "Short Name": "180m", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Specimen Code": 24, "Specimen": "Fluoride", "Container Code": 10, "Container": "<PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glucose, 30 min", "code": 235, "Department": "Biochemistry", "Result Unit": "mg/dL", "Price": 20, "Result Type": "-", "Short Name": "30m", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Specimen Code": 24, "Specimen": "Fluoride", "Container Code": 10, "Container": "<PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glucose, 60 min", "code": 236, "Department": "Biochemistry", "Referance Range": "Less than 180", "Result Unit": "mg/dL", "Price": 20, "Result Type": "-", "Short Name": "60m", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Specimen Code": 24, "Specimen": "Fluoride", "Container Code": 10, "Container": "<PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glucose, 90 min", "code": 237, "Department": "Biochemistry", "Price": 20, "Result Type": "-", "Short Name": "90m", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Specimen Code": 24, "Specimen": "Fluoride", "Container Code": 10, "Container": "<PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glucose, Ascitic Fluid", "code": 152, "Department": "Biochemistry", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 100, "Result Type": "Pick List", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Specimen Code": 8, "Specimen": "Ascitic fluid", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glucose, Body fluids", "code": 164, "Department": "Biochemistry", "Result Unit": "mg/dL", "Price": 100, "Result Type": "-", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glucose, CSF", "code": 162, "Department": "Biochemistry", "Referance Range": "Children : 60-80  Adult    : 40-70", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 100, "Result Type": "-", "Method code": 151, "Method": "Hexokinase", "Specimen Code": 13, "Specimen": "Cerebrospinal fluid", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glucose, Fasting", "code": 75, "Department": "Biochemistry", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 20, "Result Type": "-", "Short Name": "FBS", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Specimen Code": 24, "Specimen": "Fluoride", "Container Code": 10, "Container": "<PERSON>", "Instructions": "Samples to be collected between 10 to 12 hrs of Fasting in Fluoride Tube", "Cut-off Time": 0.5416666666666666, "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glucose, Pleural Fluid", "code": 158, "Department": "Biochemistry", "Result Unit": "mg/dL", "Price": 100, "Result Type": "Pick List", "Method code": 43, "Method": "Colorimetric : GOD-PAP.", "Specimen Code": 37, "Specimen": "Pleural Fluid", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glu<PERSON><PERSON>, Post-Dinner", "code": 1665, "Department": "Biochemistry", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 0, "Result Type": "-", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Specimen Code": 24, "Specimen": "Fluoride", "Container Code": 10, "Container": "<PERSON>", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glucose, Post-Lunch", "code": 1664, "Department": "Biochemistry", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 20, "Result Type": "-", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Specimen Code": 24, "Specimen": "Fluoride", "Container Code": 10, "Container": "<PERSON>", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON>lu<PERSON>e, Post-prandial", "code": 123, "Department": "Biochemistry", "Notes": "Blood glucose level primarily depends upon individual characters like type and quantity of food intake, physical activity and the body’s metabolic response.  Lower postprandial blood glucose level than fasting level can be noticed in variety of conditions in both normal population and diabetics. Various modifiable factors along with underlying condition of patient that affect blood glucose levels are:\r\n\r\n1. Pre-analytical factors such as smoking, caffeinated drinks, use of hypoglycemic drugs, heavy exercise, anxiety, strenuous activity before sampling & time of sample collection. 2.Change in glucagon to insulin ratio, the commonest cause of impaired fasting glucose tolerance and diabetes mellitus. 3. high carbohydrate meal at bedtime or not enough diabetic medication, disturbed sleep, and other lesser known entities like Dawn phenomenon and Somogyi effect. 4.Chewing and eating slower or gastroparesis can reduce the reactive glucose surge post meal. 5.Consumption of less or eat non-carbohydrate meal before testing for PPBG level.\r\n\r\nDue to individual variation of FBG and PPBG and large imprecision in analysis, researchers have advocated the use of HbA1c only for diabetes diagnosis.", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 20, "Result Type": "-", "Short Name": "PPBS", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Specimen Code": 24, "Specimen": "Fluoride", "Container Code": 10, "Container": "<PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glucose, Pre Dinner", "code": 1666, "Department": "Biochemistry", "Result Unit": "mg/dL", "No of decimals": 1, "Critical Low": 50, "   Critical High": 300, "Price": 20, "Result Type": "-", "Short Name": "GPD", "Method code": 42, "Method": "Colorimetric : GOD - POD", "Primary specimen code": 24, "Primary specimen ": "Fluoride", "Specimen Code": 23, "Specimen": "FLURIDE PLASMA", "Container Code": 10, "Container": "<PERSON>", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Glucose, Pre-Lunch", "code": 1675, "Department": "Biochemistry", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 20, "Result Type": "-", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Specimen Code": 23, "Specimen": "FLURIDE PLASMA", "Container Code": 10, "Container": "<PERSON>", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "code": 241, "Department": "Biochemistry", "Notes": "Blood glucose level primarily depends upon individual characters like type and quantity of food intake, physical activity and the body’s metabolic response.  Lower postprandial blood glucose level than fasting level can be noticed in variety of conditions in both normal population and diabetics. Various modifiable factors along with underlying condition of patient that affect blood glucose levels are:\r\n\r\n1. Pre-analytical factors such as smoking, caffeinated drinks, use of hypoglycemic drugs, heavy exercise, anxiety, strenuous activity before sampling & time of sample collection. 2.Change in glucagon to insulin ratio, the commonest cause of impaired fasting glucose tolerance and diabetes mellitus. 3. high carbohydrate meal at bedtime or not enough diabetic medication, disturbed sleep, and other lesser known entities like Dawn phenomenon and Somogyi effect. 4.Chewing and eating slower or gastroparesis can reduce the reactive glucose surge post meal. 5.Consumption of less or eat non-carbohydrate meal before testing for PPBG level.\r\n\r\nDue to individual variation of FBG and PPBG and large imprecision in analysis, researchers have advocated the use of HbA1c only for diabetes diagnosis.", "Result Unit": "mg/dL", "No of decimals": 2, "Price": 20, "Result Type": "-", "Short Name": "RBS", "Method code": 37, "Method": "Colorimetric : GOD-POD", "Specimen Code": 24, "Specimen": "Fluoride", "Container Code": 10, "Container": "<PERSON>", "Min. Process Time": 4, "      Test Done On": "expect sunday", "Applicable to": "Both"}, {"Test Name": "Glucose,Synovial Fluid", "code": 267, "Department": "Biochemistry", "Price": 100, "Result Type": "Pick List", "Short Name": "GSFD", "Method code": 42, "Method": "Colorimetric : GOD - POD", "Specimen Code": 53, "Specimen": "Synovial Fluid", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HAPTOGLOBIN", "code": 263, "Department": "Biochemistry", "Referance Range": "30 - 200", "Result Unit": "mg/dL", "Price": 1500, "Result Type": "-", "Short Name": "HAP", "Method code": 98, "Method": "Immunoturbidimetry", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 5, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HbA1c", "code": 86, "Department": "Biochemistry", "Notes": "HbA1c level reflects the mean glucose concentration over the previous period (approximately 6-8 weeks) and provides a much better\r\nindication of long term glycemic control than blood and urine glucose determinations. The American Diabetes Association recommends\r\nmeasurement of HbA1c every 3 months to determine whether a patient’s metabolic control has remained continuously within the target\r\nrange.A1C test should be performed at least 2 times a year in patients who are meeting treatment goals (and who have stable glycemic\r\ncontrol). A1C test should be performed quarterly in patients whose therapy has changed or who are not meeting glycemic goals. Predicting\r\ndevelopment and progression of diabetic microvascular complications. This assay is not useful in determining day to day glucose control\r\nand should not be used to replace routine blood glucose testing.", "Price": 400, "Result Type": "-", "Short Name": "A1c", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 17, "Specimen": "EDTA BLOOD", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "HDL/LDL-Direct Ratio", "code": 1533, "Department": "Biochemistry", "Result Unit": "<PERSON><PERSON>", "No of decimals": 2, "Price": 1, "Result Type": "Calculated", "Method code": 21, "Method": "Calculation", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Hemosiderin", "code": 294, "Department": "Biochemistry", "Price": 600, "Result Type": "No Unit / Ref. Value", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Homocysteine", "code": 570, "Department": "Biochemistry", "Referance Range": "Folate supplemented diet Children <15 Yr: <8   Adult 15-65 Yr : <12  Elderly >65 Yr : <16   Pregnancy      : <8.0    No Folate supplementation Children <15 Yr: <10   Adult 15-65 Yr : <15   Elderly >65 Yr : <20 Pregnancy      : <10.0", "Result Unit": "umoI/L", "No of decimals": 1, "Price": 1100, "Result Type": "-", "Short Name": "HCY", "Method code": 20, "Method": "Edta Plasma/Serum", "Min. Process Time": 1, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "hs- CRP", "code": 1134, "Department": "Biochemistry", "Referance Range": "< 2.87", "Result Unit": "mg/dL", "No of decimals": 2, "Price": 600, "Result Type": "Numeric", "Short Name": "hscrp", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "IA2 –INSULIN", "code": 1523, "Department": "Biochemistry", "Referance Range": "Negative:<28.0 Positive:>28.0", "Result Unit": "U/mL", "No of decimals": 1, "Price": 3000, "Result Type": "Numeric", "Short Name": "IA2", "Method code": 50, "Method": "E.I.A", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "INHIBIN B", "code": 265, "Department": "Biochemistry", "Referance Range": "Below 6 years   : Less than 73 pg/ml  6 to 9 years    : Less than 129  10 years        : Less than 103  11 years        : 20 - 186  12 - 18 years   : 14 - 362  Early Follicular: Less than 261  Late Follicular : Less than 286  Pre Ovulatory   : Less than 189  Mid Luteal      : Less than 164  End Luteal      : Less than 107  Menopause       : Less than 7", "Result Unit": "pg/ml", "No of decimals": 0, "Price": 1700, "Result Type": "Pick List", "Short Name": "INHB", "Method code": 53, "Method": "EIA", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "IRON", "code": 90, "Department": "Biochemistry", "Referance Range": "Adults : 50 - 120 (Ferro<PERSON>ine)", "Result Unit": "ug/dL", "No of decimals": 1, "Price": 350, "Result Type": "Numeric", "Short Name": "Fe", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Ketone (D3 Hydroxybutyrate)", "code": 283, "Department": "Biochemistry", "Referance Range": "0.21 - 2.81", "Result Unit": "mg/dL", "Price": 2500, "Result Type": "Pick List", "Short Name": "KED3", "Method code": 60, "Method": "Enzymatic", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Lactate", "code": 93, "Department": "Biochemistry", "Referance Range": "0.5 - 2.2", "Result Unit": "mmoL/L", "No of decimals": 2, "Price": 1000, "Result Type": "-", "Short Name": "Lactate", "Specimen Code": 23, "Specimen": "FLURIDE PLASMA", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Lactate Dehydrogenase (LDH)", "code": 94, "Department": "Biochemistry", "Referance Range": "Females  : 135 - 214  Males    : 135 - 225  Children : 120 - 300   Newborns : 225 - 600", "Result Unit": "U/L", "No of decimals": 0, "Price": 350, "Result Type": "-", "Short Name": "LDH", "Method code": 33, "Method": "Colorimetric :   Lactate -  Pyruvate", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Lactate Dehydrogenase (LDH), Ascitic Fluid", "code": 96, "Department": "Biochemistry", "Result Unit": "U/L", "No of decimals": 0, "Price": 355, "Result Type": "Numeric", "Method code": 88, "Method": "IFCC", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Lactate Dehydrogenase (LDH), Pleural fluid", "code": 98, "Department": "Biochemistry", "Referance Range": "114-220 IU/L", "Result Unit": "U/l", "No of decimals": 0, "Price": 350, "Result Type": "Pick List", "Method code": 88, "Method": "IFCC", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Lactate, CSF", "code": 92, "Department": "Biochemistry", "Referance Range": "Adult       : 1.1 - 2.4  Neonates    : 1.1 - 6.7 3 to 10 days: 1.1 - 4.4 >10 days    : 1.1 - 2.8", "Result Unit": "mmoL/L", "No of decimals": 2, "Price": 1000, "Result Type": "Numeric", "Short Name": "LACC", "Method code": 108, "Method": "LOD - POD", "Specimen Code": 13, "Specimen": "Cerebrospinal fluid", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "LDH - CSF", "code": 95, "Department": "Biochemistry", "Notes": "No established reference for LDH in body fluids. Suggested the physician to correlate the results clinically.", "Result Unit": "U/L", "No of decimals": 0, "Price": 350, "Result Type": "Pick List", "Method code": 88, "Method": "IFCC", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "LDH -Peritoneal Fluid", "code": 97, "Department": "Biochemistry", "Result Unit": "U/L", "No of decimals": 0, "Price": 350, "Result Type": "Pick List", "Method code": 88, "Method": "IFCC", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "LDL-Direct/HDL  Ratio", "code": 1409, "Department": "Biochemistry", "Result Unit": "<PERSON><PERSON>", "No of decimals": 1, "Price": 1, "Result Type": "Calculated", "Short Name": "L/HR", "Method code": 21, "Method": "Calculation", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "LDL/HDL Ratio", "code": 1436, "Department": "Biochemistry", "Referance Range": "Castelli's Risk Index -II Ideal : <2.0 Good: 2.0-5.0 High: >=5", "Result Unit": "<PERSON><PERSON>", "No of decimals": 1, "Price": 1, "Result Type": "Calculated", "Method code": 21, "Method": "Calculation", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Lipase", "code": 101, "Department": "Biochemistry", "Referance Range": "ADULT : 13 - 60", "Result Unit": "U/L", "No of decimals": 1, "Price": 600, "Result Type": "Numeric", "Short Name": "Lip", "Method code": 91, "Method": "Methyl Re<PERSON>ufin", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "LIPOPROTEIN (a)", "code": 103, "Department": "Biochemistry", "Referance Range": "Less than 75.0", "Result Unit": "nmol/L", "No of decimals": 1, "Price": 700, "Result Type": "Numeric", "Short Name": "LPA", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 24, "      Test Done On": "all ", "Applicable to": "Both"}, {"Test Name": "Lithium", "code": 105, "Department": "Biochemistry", "Referance Range": "0.6 - 1.2", "Result Unit": "mmoL/L", "No of decimals": 2, "Price": 600, "Result Type": "Numeric", "Short Name": "Li", "Method code": 99, "Method": "Ion Selective Electrode", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "Min. Process Time": 10, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "LP-PLA2 (LIPOPROTEIN ASSOCIATED PHOSPHOLIPASE A2)", "code": 1413, "Department": "Biochemistry", "Referance Range": "Low Risk  : < 275  High Risk : > 275", "Result Unit": "U/L", "No of decimals": 0, "Price": 1500, "Result Type": "Numeric", "Short Name": "PLA2", "Method code": 60, "Method": "Enzymatic", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 12, "Container": "<PERSON><PERSON> Container", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Magnesium", "code": 106, "Department": "Biochemistry", "Referance Range": "1.3 - 2.5", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 350, "Result Type": "Numeric", "Short Name": "Mg", "Method code": 144, "Method": "Xylidyl Blue", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Magnesium, Urine", "code": 1316, "Department": "Biochemistry", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 700, "Result Type": "Numeric", "Short Name": "MgU", "Method code": 15, "Method": "Biochemical", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "METANEPHRINE (SPOT)", "code": 270, "Department": "Biochemistry", "Price": 2000, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Met<PERSON><PERSON><PERSON>, 24 Hrs", "code": 249, "Department": "Biochemistry", "Price": 3000, "Result Type": "-", "Short Name": "MT24", "Specimen Code": 3, "Specimen": "24 H<PERSON> <PERSON><PERSON>", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "Min. Process Time": 10, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Metanephrine-free, plasma", "code": 289, "Department": "Biochemistry", "Referance Range": "Less than 65", "Result Unit": "pg/ml", "No of decimals": 1, "Price": 4000, "Result Type": "-", "Short Name": "MTFP", "Method code": 51, "Method": "E.L.I.S.A", "Specimen Code": 18, "Specimen": "EDTA PLASMA", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Methyl Malonic Acid", "code": 1321, "Department": "Biochemistry", "Referance Range": "Negative", "Price": 900, "Result Type": "Pick List", "Method code": 15, "Method": "Biochemical", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Min. Process Time": 7, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "METHYLMALONIC ACID –QUANTITATIVE", "code": 1525, "Department": "Biochemistry", "Result Unit": "mg/g creatinine", "No of decimals": 2, "Price": 2500, "Result Type": "-", "Short Name": "MMAQ", "Method code": 105, "Method": "LC-MS/MS", "Specimen Code": 43, "Specimen": "SERUM/URINE", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Microalbumin", "code": 107, "Department": "Biochemistry", "Price": 500, "Result Type": "-", "Short Name": "MAU", "Method code": 114, "Method": "NEPHELOMETRY", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Microalbumin, Urine 24Hr", "code": 12, "Department": "Biochemistry", "Price": 500, "Result Type": "-", "Short Name": "MAU", "Method code": 57, "Method": "<PERSON><PERSON>", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Instructions": "sample to be collected in Lab container(10 % Thymol 5ml), mention total volume", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Microalbumin/<PERSON> , Urine", "code": 177, "Department": "Biochemistry", "Price": 500, "Result Type": "Pick List", "Short Name": "MA/CR", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "MUCOPOLYSACCHARIDES (Glycosaminoglycan (GAG))", "code": 194, "Department": "Biochemistry", "Price": 2700, "Result Type": "Pick List", "Method code": 15, "Method": "Biochemical", "Specimen Code": 55, "Specimen": "URINE", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON>og<PERSON>bin, Urine", "code": 279, "Department": "Biochemistry", "Notes": "INTERPRETATION :-  Patients with urine myoglobin greater than 15,000 µg/L are at risk of acute renal failure. Results between 1,000 and 15,000 µg/L may be associated with the following conditionscrush injury, myocardial infarction, electric shock, post convulsions, sea snake bite, progressive muscle diseases like polymyositis, dermatomyositis, SLE and muscular dystrophy, drugs like cocaine, heroin, methadone,diazepam, amphetamines, barbiturates, carbon monoxide poisoning.\r\n\r\n ASSOCIATED TEST : - serum myoglobin, serum creatine phosphokinase (CPK)\r\n\r\n NOTE:  Patients on Biotin supplement may have interference in some immunoassays. With individuals taking high dose Biotin (more than 5 mg per day) supplements, at least 8-hour wait time before blood draw is recommended", "Referance Range": "0 - 1000", "Result Unit": "ug/L", "Price": 700, "Result Type": "Pick List", "Short Name": "MYOU", "Method code": 52, "Method": "ECLIA", "Specimen Code": 55, "Specimen": "URINE", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON>", "code": 1333, "Department": "Biochemistry", "Referance Range": "0.14 - 1.00", "Result Unit": "ug/L", "No of decimals": 2, "Price": 2500, "Result Type": "-", "Short Name": "NIC", "Method code": 81, "Method": "ICPMS", "Specimen Code": 20, "Specimen": "Edta Plasma/Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON> (Cotinine) Metaboli<PERSON>, Card", "code": 1547, "Department": "Biochemistry", "Referance Range": "Negative", "Price": 2000, "Result Type": "Pick List", "Method code": 90, "Method": "Immuno Chromatography", "Specimen Code": 56, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Nicotine Metabolite", "code": 264, "Department": "Biochemistry", "Notes": "Note:\r\n\r\nThis test is used for the quantitative measurement of cotinine and other principal metabolites of nicotine in biological specimens, as an aid in distinguishing tobacco smokers from non-smokers.", "Referance Range": "SERUM Smokers     : Above 25  Non-smokers : Below 25   URINE Negative    : Below 500 Positive    : 500 & Above", "Result Unit": "ng/ml", "No of decimals": 1, "Price": 1900, "Result Type": "-", "  Reporting Days": 2}, {"Test Name": "Non-Invasive Prenatal Testing (NIPT)", "code": 1663, "Department": "Biochemistry", "Price": 10000, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Nor-Metanephrine, Urine 24 Hrs", "code": 1332, "Department": "Biochemistry", "Price": 2400, "Result Type": "-", "Short Name": "Nor-Metane", "Specimen Code": 3, "Specimen": "24 H<PERSON> <PERSON><PERSON>", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 10}, {"Test Name": "Oligoclonal band,CSF", "code": 1355, "Department": "Biochemistry", "Price": 5000, "Result Type": "Template", "Short Name": "OLI", "      Test Done On": "expect sunday", "Applicable to": "Both", "  Reporting Days": 7}, {"Test Name": "Opiates (Morphine)", "code": 187, "Department": "Biochemistry", "Referance Range": "Negative", "Price": 800, "Result Type": "Pick List", "Short Name": "OPI", "Method code": 80, "Method": "IC", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 3}, {"Test Name": "OSMOLALITY (SERUM)", "code": 296, "Department": "Biochemistry", "Referance Range": "280 - 305.", "Result Unit": "mOsm/kg", "No of decimals": 1, "Price": 700, "Result Type": "-", "Short Name": "OSMS", "Method code": 20, "Method": "Calculated", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "OSMOLALITY (Urine)", "code": 588, "Department": "Biochemistry", "Referance Range": "50 - 1400.", "Result Unit": "mOsm/kg", "No of decimals": 1, "Price": 500, "Result Type": "-", "Short Name": "OSMU", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PhenylAlanine Screen", "code": 276, "Department": "Biochemistry", "Referance Range": "NOT DETECTED", "Price": 1600, "Result Type": "-", "Specimen Code": 64, "Specimen": "Serum", "Min. Process Time": 10, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Phosphorous", "code": 209, "Department": "Biochemistry", "Result Unit": "mg/dL", "No of decimals": 2, "Price": 150, "Result Type": "-", "Short Name": "<PERSON><PERSON>", "Method code": 120, "Method": "Phosphomolybdate complex", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PLASMA ACETONE", "code": 147, "Department": "Biochemistry", "Referance Range": "Not Present", "Price": 100, "Result Type": "Pick List", "Method code": 60, "Method": "Enzymatic", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PLEURAL FLUID AMYLASE", "code": 120, "Department": "Biochemistry", "Result Unit": "U/L", "Price": 450, "Result Type": "Pick List", "Method code": 60, "Method": "Enzymatic", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON>rphop<PERSON><PERSON><PERSON>, <PERSON><PERSON> 24 Hrs,", "code": 1412, "Department": "Biochemistry", "Price": 5000, "Result Type": "Pick List", "Short Name": "POR", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Min. Process Time": 10, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Potassium, Urine", "code": 121, "Department": "Biochemistry", "Referance Range": "Adult (< 40 Yr) Male  : 11 - 80 Female: 17 - 145  Adult (= 40 Yr) Male  : 17 - 99 Female: 22 - 164", "Result Unit": "mmol/L", "Price": 250, "Result Type": "Pick List", "Short Name": "UK+", "Method code": 99, "Method": "Ion Selective Electrode", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Potassium, Urine 24Hr", "code": 122, "Department": "Biochemistry", "Price": 250, "Short Name": "UP24", "Method code": 99, "Method": "Ion Selective Electrode", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Potassium", "code": 206, "Department": "Biochemistry", "Referance Range": "New Born : 3.7 - 5.9 Infant   : 4.1 - 5.3 Child    : 3.4 - 4.7 Adults   : 3.5 - 5.1", "Result Unit": "mmol/L", "No of decimals": 1, "Price": 250, "Short Name": "K", "Method code": 99, "Method": "Ion Selective Electrode", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PROSTATIC ACID PHOSPHATASE", "code": 211, "Department": "Biochemistry", "Referance Range": "Upto 3.0", "Result Unit": "U/L", "No of decimals": 1, "Price": 300, "Method code": 60, "Method": "Enzymatic", "Specimen Code": 64, "Specimen": "Serum", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Protein Creatinine <PERSON>, Urine", "code": 200, "Department": "Biochemistry", "Price": 400, "Result Type": "Pick List", "Short Name": "PC RT", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "Container Code": 3, "Container": "<PERSON><PERSON>tainer", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON>, <PERSON>citic Fluid", "code": 126, "Department": "Biochemistry", "Result Unit": "g/dL", "No of decimals": 1, "Price": 200, "Result Type": "Pick List", "Method code": 16, "Method": "Colorimetric-Biuret", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Protein,  Pericardial fluid", "code": 125, "Department": "Biochemistry", "Referance Range": "Transudate : <2.5  Exudate    : >3.0", "Result Unit": "g/dL", "No of decimals": 1, "Price": 200, "Method code": 16, "Method": "Colorimetric-Biuret", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON>tein, Body fluids", "code": 128, "Department": "Biochemistry", "Result Unit": "g/dL", "Price": 200, "Result Type": "Pick List", "Method code": 16, "Method": "Colorimetric-Biuret", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON>, CSF", "code": 136, "Department": "Biochemistry", "Referance Range": "15- 45", "Result Unit": "mg/dL", "Price": 200, "Short Name": "Prot", "Method code": 39, "Method": "Colorimetric : Pyrogallol red", "Specimen Code": 13, "Specimen": "Cerebrospinal fluid", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Protein, Peritoneal Fluid", "code": 139, "Department": "Biochemistry", "Referance Range": "Transudate : 1.0 - 3.0  Exudate    : >3.0", "Result Unit": "g/dL", "Price": 200, "Method code": 16, "Method": "Colorimetric-Biuret", "Specimen Code": 34, "Specimen": "Peritoneal fluid", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON>, Pleural Fluid", "code": 130, "Department": "Biochemistry", "Referance Range": "Transudate : <2.5 Exudate    : >3.0", "Result Unit": "g/dL", "No of decimals": 2, "Price": 200, "Result Type": "Numeric", "Method code": 16, "Method": "Colorimetric-Biuret", "Specimen Code": 36, "Specimen": "<PERSON><PERSON><PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON>, Urine", "code": 199, "Department": "Biochemistry", "Referance Range": "Less than 15.0", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 250, "Result Type": "Numeric", "Short Name": "UPT", "Method code": 39, "Method": "Colorimetric : Pyrogallol red", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON>, <PERSON>rine 24Hr", "code": 11, "Department": "Biochemistry", "Price": 350, "Result Type": "Pick List", "Short Name": "24PU", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Instructions": "sample to be collected in Lab container(10% Thymol 5ml), mention total volume", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON>,Synovial Fluid", "code": 268, "Department": "Biochemistry", "Referance Range": "Transudate : <2.5 Exudate    : >3.0", "Result Unit": "g/dL", "Price": 250, "Result Type": "Pick List", "Method code": 16, "Method": "Colorimetric-Biuret", "Specimen Code": 53, "Specimen": "Synovial Fluid", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "PYRUVATE", "code": 1522, "Department": "Biochemistry", "Referance Range": "0.0 – 0.7", "Result Unit": "mg/dL", "Price": 2500, "Result Type": "Pick List", "Short Name": "PYRU", "Method code": 60, "Method": "Enzymatic", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 4, "Container": "Serum Container", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Sodium, Urine", "code": 151, "Department": "Biochemistry", "Referance Range": "Adult (< 40 Yr) Male  : 25 - 301 Female: 15 - 267  Adult (>= 40 Yr) Male  : 18 - 214 Female: 15 - 237", "Result Unit": "mmol/L", "Price": 250, "Result Type": "Pick List", "Short Name": "UNa", "Method code": 99, "Method": "Ion Selective Electrode", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Sodium, Urine 24Hr", "code": 150, "Department": "Biochemistry", "Price": 250, "Short Name": "UN24", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Sodium", "code": 205, "Department": "Biochemistry", "Referance Range": "New Born : 133 - 146 Infant   : 139 - 146 Child    : 138 - 145 Adult    : 136 - 145 >90 years: 132 - 146", "Result Unit": "mmol/L", "Price": 250, "Short Name": "Na", "Method code": 99, "Method": "Ion Selective Electrode", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "expect sunday", "Applicable to": "Both"}, {"Test Name": "STONE ANALYSIS", "code": 291, "Department": "Biochemistry", "Price": 1000, "Result Type": "Pick List", "Method code": 73, "Method": "FTIR", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "SUCROSE LYSIS TEST(PNH SCREENING TEST)", "code": 243, "Department": "Biochemistry", "Referance Range": "NEGATIVE", "Price": 450, "Result Type": "Pick List", "Method code": 73, "Method": "FTIR", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Total Iron Binding Capacity", "code": 166, "Department": "Biochemistry", "Price": 500, "Short Name": "TIBC", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Total Protein.", "code": 167, "Department": "Biochemistry", "Referance Range": "Adult : 6.6 - 8.7", "Result Unit": "g/dL", "No of decimals": 2, "Price": 100, "Short Name": "TP", "Method code": 16, "Method": "Colorimetric-Biuret", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Toxic Elements - 22", "code": 1513, "Department": "Biochemistry", "Price": 3000, "Result Type": "Template", "Short Name": "TOXI", "Method code": 81, "Method": "ICPMS", "Specimen Code": 61, "Specimen": "WHOLE BLOOD", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "TPMT ENZYME ACTIVITY (Thiopurine Methyl Transferase)", "code": 1431, "Department": "Biochemistry", "Referance Range": "<5.5    : Low Activity 5.5-15.5: Intermediate Activity >15.5   : High Activity", "Result Unit": "units", "No of decimals": 1, "Price": 4500, "Short Name": "TPMT", "Method code": 53, "Method": "EIA", "Specimen Code": 18, "Specimen": "EDTA PLASMA", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Transferrin Saturation", "code": 168, "Department": "Biochemistry", "Price": 400, "Short Name": "TSAT", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Tricyclic Antidepressants (TCA)", "code": 1434, "Department": "Biochemistry", "Price": 1150, "Result Type": "Pick List", "Short Name": "TCA", "Method code": 90, "Method": "Immuno Chromatography", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Triglycerides", "code": 169, "Department": "Biochemistry", "Notes": "Note: Above Biological interval  is based on 9 to 12 hours fasting", "Result Unit": "mg/dL", "No of decimals": 1, "Critical Low": 50, "   Critical High": 700, "Price": 100, "Short Name": "TGL", "Method code": 77, "Method": "Glycerol-3-phosphate oxidase-PAP", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Troponin I", "code": 170, "Department": "Biochemistry", "Notes": "NOTE :Troponin I is part of a three subunit complex comprising of Troponin T and Troponin C. Along with tropomyosin, this structural complex forms the main component that regulates the calcium sensitive ATPase activity of actomyosin in striated skeletal and cardiac muscle.  After cardiac injury occurs, Troponin I is released into the blood 4-6 hours after the onset of pain. The release pattern of cTnI is similar to CK-MB, but while CK-MB levels return to normal after 72 hours, Troponin I remain elevated for 6-10 days, thus providing for a longer window of detection for cardiac injury. The high specificity of cTnI measurements for the identification of myocardial damage has been demonstrated in conditions such as the perioperative period, after marathon runs, and blunt chest trauma.  cTnI release has also been documented in cardiac conditions other than acute myocardial infarction (AMI) such as unstable angina, congestive heart failure, and ischemic damage due to coronary artery bypass surgery.  Because of its high specificity and sensitivity in the myocardial tissue, Troponin I has recently become the most preferred biomarker for myocardial infarction.", "Price": 700, "Result Type": "Pick List", "Short Name": "TP I", "Method code": 26, "Method": "Chromatographic Lateral Flow Immunoassay", "Specimen Code": 50, "Specimen": "Serum/Whole Blood", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Troponin T", "code": 171, "Department": "Biochemistry", "Notes": "Note:\r\nCardiac troponin T is a muscle structural protein which is released in the event of cell damage (necrosis) to the myocardium. The Trop T sensitive test is designed for quantitative determination of cardiac troponin T in the blood as an aid for risk stratification of patients with unstable angina pectoris and for diagnosis of myocardial infarction. A negative troponin T result does not rule out myocardial infarction as the release of troponin T from the damaged myocardial cells into the circulating blood occurs with time delays which vary from person to person. Troponin T can first be detected in the blood after a time period of 2 or more than 10 hours after the onset of symptoms.  Typical or atypical symptoms and a negative troponin T result call for further diagnostic measures to be applied, including more troponin T tests. Due to its release kinetics, troponin T can be detected for upto 14 days after onset of cardiac infarction.\r\n\r\nA positive result means that the concentration of troponin T in the sample is above the test’s threshold value of 0.1 ng/mL as an evidence of cell damage to the myocardium. All positive results should be interpreted with clinical significance", "Referance Range": "Negative (<0.1 ng/ml)", "Price": 800, "Result Type": "Pick List", "Short Name": "TP T", "Method code": 80, "Method": "IC", "Specimen Code": 16, "Specimen": "EDTA", "Container Code": 7, "Container": "EDTA Container", "Min. Process Time": 2, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Troponin- I (high sensitive), Quantitative", "code": 1584, "Department": "Biochemistry", "Referance Range": "Less than 26.20", "Result Unit": "pg/mL", "No of decimals": 2, "Price": 1700, "Result Type": "Numeric", "Method code": 29, "Method": "CMIA", "Specimen Code": 39, "Specimen": "SERUM", "Container Code": 15, "Container": "PLAIN", "      Test Done On": "all", "Applicable to": "Both", "  Reporting Days": 1}, {"Test Name": "Troponin- T (high sensitive), Quantitative", "code": 287, "Department": "Biochemistry", "Referance Range": "<14.0 : Negative >14.0 : Positive", "Result Unit": "pg/mL", "Price": 1700, "Result Type": "Pick List", "Method code": 52, "Method": "ECLIA", "Specimen Code": 18, "Specimen": "EDTA PLASMA", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Urea", "code": 214, "Department": "Biochemistry", "Result Unit": "mg/dL", "No of decimals": 2, "Price": 100, "Result Type": "Numeric", "Short Name": "<PERSON><PERSON>", "Method code": 165, "Method": "Urease/GLDH", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "UREA CLEARANCE 24 HOUR URINE", "code": 174, "Department": "Biochemistry", "Referance Range": "41 - 68", "Result Unit": "ml/min", "Price": 250, "Result Type": "Numeric", "Method code": 21, "Method": "Calculation", "Specimen Code": 3, "Specimen": "24 H<PERSON> <PERSON><PERSON>", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Urea, Urine", "code": 173, "Department": "Biochemistry", "Result Unit": "mg/dL", "Price": 200, "Result Type": "Pick List", "Method code": 64, "Method": "Enzymatic-Kinetic", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Urea, Urine 24Hr", "code": 172, "Department": "Biochemistry", "Price": 250, "Result Type": "Pick List", "Specimen Code": 57, "Specimen": "<PERSON><PERSON>", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON>, <PERSON>rine 24Hr", "code": 221, "Department": "Biochemistry", "Price": 250, "Short Name": "UU24", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Uric Acid.", "code": 175, "Department": "Biochemistry", "Referance Range": "Child  : 2.0 - 5.0 Adult Male   : 3.5 - 7.2 Female : 2.6 - 6.0", "Result Unit": "mg/dL", "No of decimals": 2, "Price": 100, "Short Name": "UA", "Method code": 140, "Method": "Uricase/peroxidase", "Specimen Code": 64, "Specimen": "Serum", "Container Code": 15, "Container": "PLAIN", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URICACID / CREATININE", "code": 253, "Department": "Biochemistry", "Price": 200, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Uricacid, Synovial fluid", "code": 259, "Department": "Biochemistry", "Result Unit": "mg/dL", "Price": 350, "Result Type": "Pick List", "Method code": 140, "Method": "Uricase/peroxidase", "Specimen Code": 53, "Specimen": "Synovial Fluid", "Min. Process Time": 4, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE  ALKAPTANURIA (Homogentisic Acid)", "code": 202, "Department": "Biochemistry", "Referance Range": "NEGATIVE", "Price": 1000, "Result Type": "Pick List", "Method code": 15, "Method": "Biochemical", "Specimen Code": 55, "Specimen": "URINE", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE ALCOHOL", "code": 227, "Department": "Biochemistry", "Referance Range": "NOT DETECTABLE", "Result Unit": "mg/dL", "Price": 1100, "Result Type": "Pick List", "Method code": 60, "Method": "Enzymatic", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE CALCIUM CREATININE RATIO", "code": 180, "Department": "Biochemistry", "Price": 500, "Result Type": "Pick List", "Short Name": "UCCR", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE CHROMATOGRAPHY", "code": 251, "Department": "Biochemistry", "Price": 500, "Result Type": "No Unit / Ref. Value", "Method code": 137, "Method": "TLC", "Specimen Code": 55, "Specimen": "URINE", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "<PERSON><PERSON> (Nicotine) Card", "code": 1730, "Department": "Biochemistry", "Referance Range": "Negative", "Price": 0, "Result Type": "Pick List", "Method code": 55, "Method": "URINE", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE METABOLIC SCREENING", "code": 1678, "Department": "Biochemistry", "Price": 6100, "Result Type": "Pick List", "Primary specimen code": 55, "Primary specimen ": "URINE", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Urine Organic Acid, Complete Panel", "code": 195, "Department": "Biochemistry", "Price": 4000, "Result Type": "Template", "Specimen Code": 55, "Specimen": "URINE", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE PHOSPHOROUS", "code": 198, "Department": "Biochemistry", "Result Unit": "mg/dL", "Price": 300, "Result Type": "Pick List", "Method code": 112, "Method": "Molybdate UV", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE URIC ACID", "code": 201, "Department": "Biochemistry", "Referance Range": "Random Urine Male < 40 yr : 9 - 63 >=40 yr : 6 - 114  Female < 40 yr : 6 - 71 >=40 yr : 4 - 93", "Result Unit": "mg/dL", "No of decimals": 1, "Price": 250, "Method code": 60, "Method": "Enzymatic", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "URINE-CALCIUM", "code": 252, "Department": "Biochemistry", "Result Unit": "mg/dL", "Price": 200, "Result Type": "Pick List", "Method code": 190, "Method": "Colorimetric : 5-nitro-5’-methyl-BAPTA", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "VMA (SPOT)", "code": 269, "Department": "Biochemistry", "Referance Range": "<18.80", "Result Unit": "mg/g creatinine", "No of decimals": 2, "Price": 2500, "Short Name": "VMAS", "Method code": 79, "Method": "HPLC", "Specimen Code": 45, "Specimen": "SPOT URINE", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "VMA -24 H<PERSON> Urine", "code": 204, "Department": "Biochemistry", "Price": 4000, "Result Type": "Pick List", "Short Name": "VA24", "Specimen Code": 2, "Specimen": "24 Hr <PERSON><PERSON>", "Container Code": 13, "Container": "<PERSON><PERSON><PERSON>er", "Min. Process Time": 10, "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "WATER ANALYSIS (CHEMISTRY)", "code": 244, "Department": "Biochemistry", "Price": 3000, "Result Type": "Pick List", "      Test Done On": "all", "Applicable to": "Both"}, {"Test Name": "Whole Blood Clotting Test (WBCT)", "code": 1674, "Department": "Biochemistry", "Referance Range": "Less than 20 Mins.", "Price": 100, "Result Type": "Pick List", "Primary specimen code": 9, "Primary specimen ": "BLOOD", "Specimen Code": 61, "Specimen": "WHOLE BLOOD", "      Test Done On": "all", "Applicable to": "Both"}]