/* Auth Layout Styles */

.auth-wrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--light);
  padding: 1rem;
}

.auth-card {
  width: 100%;
  max-width: 450px;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  transition: var(--transition);
}

.auth-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.3);
}

.auth-header {
  padding: 2rem;
  text-align: center;
}

.auth-logo {
  max-width: 150px;
  margin-bottom: 1.5rem;
}

.auth-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--secondary);
  margin-bottom: 0.5rem;
}

.auth-subtitle {
  font-size: 1rem;
  color: var(--medium-gray);
}

.auth-body {
  padding: 1.5rem 2rem 2rem;
}

.auth-form .form-group {
  margin-bottom: 1.5rem;
}

.auth-form .form-label {
  font-weight: 600;
  color: var(--dark-gray);
}

.auth-form .form-control {
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.auth-form .form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.auth-form .btn-primary {
  width: 100%;
  padding: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.auth-footer {
  padding: 1rem 2rem;
  text-align: center;
  border-top: 1px solid var(--border-color);
  background-color: rgba(var(--light-rgb), 0.5);
}

.auth-footer a {
  font-weight: 600;
}

.auth-alert {
  margin-bottom: 1.5rem;
  border-radius: var(--border-radius);
  padding: 1rem;
  font-weight: 600;
}

.auth-alert.alert-danger {
  background-color: rgba(var(--danger-rgb), 0.1);
  color: var(--danger);
  border: 1px solid rgba(var(--danger-rgb), 0.2);
}

.auth-alert.alert-success {
  background-color: rgba(var(--success-rgb), 0.1);
  color: var(--success);
  border: 1px solid rgba(var(--success-rgb), 0.2);
}

/* Responsive Styles */
@media (max-width: 576px) {
  .auth-card {
    max-width: 100%;
  }
  
  .auth-header {
    padding: 1.5rem 1rem;
  }
  
  .auth-body {
    padding: 1rem;
  }
  
  .auth-footer {
    padding: 1rem;
  }
}
