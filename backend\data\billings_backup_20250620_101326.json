[{"id": 1, "invoice_number": "INV00001", "patient_id": 23, "items": [{"id": 1, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 3, "price": 350, "amount": 1050}, {"id": 2, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}, {"id": 3, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 3, "price": 200, "amount": 600}, {"id": 4, "test_id": 3, "test_name": "HbA1c", "quantity": 3, "price": 450, "amount": 1350}, {"id": 5, "test_id": 4, "test_name": "Lipid Profile", "quantity": 3, "price": 600, "amount": 1800}], "subtotal": 5250, "discount": 908.08, "tax": 945.0, "total_amount": 5286.92, "paid_amount": 5286.92, "balance": 0, "payment_method": "Cash", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-03-22", "due_date": "2025-04-21", "created_at": "2025-03-22T18:01:04.178251", "updated_at": "2025-03-22T18:01:04.178251", "tenant_id": 3, "created_by": 2}, {"id": 2, "invoice_number": "INV00002", "patient_id": 30, "items": [{"id": 1, "test_id": 5, "test_name": "Liver Function Test", "quantity": 2, "price": 800, "amount": 1600}, {"id": 2, "test_id": 5, "test_name": "Liver Function Test", "quantity": 3, "price": 800, "amount": 2400}], "subtotal": 4000, "discount": 87.63, "tax": 720.0, "total_amount": 4632.37, "paid_amount": 0, "balance": 4632.37, "payment_method": "Card", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-03-24", "due_date": "2025-04-23", "created_at": "2025-03-24T18:01:04.178251", "updated_at": "2025-03-24T18:01:04.178251", "tenant_id": 3, "created_by": 3}, {"id": 3, "invoice_number": "INV00003", "patient_id": 11, "items": [{"id": 1, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 1, "price": 700, "amount": 700}, {"id": 2, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 3, "price": 200, "amount": 600}, {"id": 3, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 1, "price": 350, "amount": 350}, {"id": 4, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 2, "price": 150, "amount": 300}], "subtotal": 1950, "discount": 0.55, "tax": 351.0, "total_amount": 2300.45, "paid_amount": 2300.45, "balance": 0, "payment_method": "Cash", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-05", "due_date": "2025-05-05", "created_at": "2025-04-05T18:01:04.178251", "updated_at": "2025-04-05T18:01:04.178251", "tenant_id": 3, "created_by": 1}, {"id": 4, "invoice_number": "INV00004", "patient_id": 23, "items": [{"id": 1, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}], "subtotal": 450, "discount": 28.78, "tax": 81.0, "total_amount": 502.22, "paid_amount": 502.22, "balance": 0, "payment_method": "Card", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-03-16", "due_date": "2025-04-15", "created_at": "2025-03-16T18:01:04.178251", "updated_at": "2025-03-16T18:01:04.178251", "tenant_id": 2, "created_by": 1}, {"id": 5, "invoice_number": "INV00005", "patient_id": 24, "items": [{"id": 1, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 2, "price": 700, "amount": 1400}, {"id": 2, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 1, "price": 200, "amount": 200}, {"id": 3, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 2, "price": 700, "amount": 1400}], "subtotal": 3000, "discount": 378.97, "tax": 540.0, "total_amount": 3161.0299999999997, "paid_amount": 996.12, "balance": 2164.91, "payment_method": "Cash", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-05-13", "due_date": "2025-06-12", "created_at": "2025-05-13T18:01:04.178251", "updated_at": "2025-05-13T18:01:04.178251", "tenant_id": 3, "created_by": 1}, {"id": 6, "invoice_number": "INV00006", "patient_id": 30, "items": [{"id": 1, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 2, "price": 700, "amount": 1400}, {"id": 2, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 3, "price": 200, "amount": 600}], "subtotal": 2000, "discount": 92.82, "tax": 360.0, "total_amount": 2267.1800000000003, "paid_amount": 762.43, "balance": 1504.7500000000005, "payment_method": "Cash", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-03-20", "due_date": "2025-04-19", "created_at": "2025-03-20T18:01:04.178251", "updated_at": "2025-03-20T18:01:04.178251", "tenant_id": 1, "created_by": 1}, {"id": 7, "invoice_number": "INV00007", "patient_id": 5, "items": [{"id": 1, "test_id": 3, "test_name": "HbA1c", "quantity": 2, "price": 450, "amount": 900}, {"id": 2, "test_id": 7, "test_name": "Thyroid Profile", "quantity": 3, "price": 850, "amount": 2550}, {"id": 3, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}], "subtotal": 3900, "discount": 47.92, "tax": 702.0, "total_amount": 4554.08, "paid_amount": 4554.08, "balance": 0, "payment_method": "Card", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-09", "due_date": "2025-05-09", "created_at": "2025-04-09T18:01:04.178251", "updated_at": "2025-04-09T18:01:04.178251", "tenant_id": 2, "created_by": 3}, {"id": 8, "invoice_number": "INV00008", "patient_id": 34, "items": [{"id": 1, "test_id": 5, "test_name": "Liver Function Test", "quantity": 2, "price": 800, "amount": 1600}, {"id": 2, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 3, "price": 350, "amount": 1050}, {"id": 3, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 3, "price": 700, "amount": 2100}, {"id": 4, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}], "subtotal": 5200, "discount": 446.09, "tax": 936.0, "total_amount": 5689.91, "paid_amount": 5689.91, "balance": 0, "payment_method": "Bank Transfer", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-05-21", "due_date": "2025-06-20", "created_at": "2025-05-21T18:01:04.178251", "updated_at": "2025-05-21T18:01:04.178251", "tenant_id": 1, "created_by": 2}, {"id": 9, "invoice_number": "INV00009", "patient_id": 11, "items": [{"id": 1, "test_id": 3, "test_name": "HbA1c", "quantity": 3, "price": 450, "amount": 1350}, {"id": 2, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 1, "price": 350, "amount": 350}, {"id": 3, "test_id": 4, "test_name": "Lipid Profile", "quantity": 3, "price": 600, "amount": 1800}, {"id": 4, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 1, "price": 700, "amount": 700}, {"id": 5, "test_id": 3, "test_name": "HbA1c", "quantity": 2, "price": 450, "amount": 900}], "subtotal": 5100, "discount": 166.61, "tax": 918.0, "total_amount": 5851.39, "paid_amount": 0, "balance": 5851.39, "payment_method": "Cash", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-03-25", "due_date": "2025-04-24", "created_at": "2025-03-25T18:01:04.178251", "updated_at": "2025-03-25T18:01:04.178251", "tenant_id": 3, "created_by": 2}, {"id": 10, "invoice_number": "INV00010", "patient_id": 34, "items": [{"id": 1, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 2, "price": 200, "amount": 400}, {"id": 2, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 3, "price": 350, "amount": 1050}, {"id": 3, "test_id": 4, "test_name": "Lipid Profile", "quantity": 2, "price": 600, "amount": 1200}, {"id": 4, "test_id": 4, "test_name": "Lipid Profile", "quantity": 2, "price": 600, "amount": 1200}, {"id": 5, "test_id": 3, "test_name": "HbA1c", "quantity": 3, "price": 450, "amount": 1350}], "subtotal": 5200, "discount": 47.49, "tax": 936.0, "total_amount": 6088.51, "paid_amount": 0, "balance": 6088.51, "payment_method": "Card", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-10", "due_date": "2025-07-10", "created_at": "2025-06-10T18:01:04.178251", "updated_at": "2025-06-10T18:01:04.178251", "tenant_id": 1, "created_by": 3}, {"id": 11, "invoice_number": "INV00011", "patient_id": 46, "items": [{"id": 1, "test_id": 5, "test_name": "Liver Function Test", "quantity": 2, "price": 800, "amount": 1600}, {"id": 2, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 1, "price": 700, "amount": 700}, {"id": 3, "test_id": 3, "test_name": "HbA1c", "quantity": 3, "price": 450, "amount": 1350}, {"id": 4, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 2, "price": 350, "amount": 700}, {"id": 5, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}], "subtotal": 4800, "discount": 545.59, "tax": 864.0, "total_amount": 5118.41, "paid_amount": 1431.81, "balance": 3686.6, "payment_method": "UPI", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-04-19", "due_date": "2025-05-19", "created_at": "2025-04-19T18:01:04.178251", "updated_at": "2025-04-19T18:01:04.178251", "tenant_id": 2, "created_by": 3}, {"id": 12, "invoice_number": "INV00012", "patient_id": 19, "items": [{"id": 1, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 3, "price": 350, "amount": 1050}, {"id": 2, "test_id": 5, "test_name": "Liver Function Test", "quantity": 3, "price": 800, "amount": 2400}, {"id": 3, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 3, "price": 350, "amount": 1050}, {"id": 4, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 1, "price": 700, "amount": 700}, {"id": 5, "test_id": 5, "test_name": "Liver Function Test", "quantity": 3, "price": 800, "amount": 2400}], "subtotal": 7600, "discount": 1148.03, "tax": 1368.0, "total_amount": 7819.97, "paid_amount": 7819.97, "balance": 0, "payment_method": "Card", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-05-27", "due_date": "2025-06-26", "created_at": "2025-05-27T18:01:04.178251", "updated_at": "2025-05-27T18:01:04.178251", "tenant_id": 3, "created_by": 1}, {"id": 13, "invoice_number": "INV00013", "patient_id": 30, "items": [{"id": 1, "test_id": 3, "test_name": "HbA1c", "quantity": 3, "price": 450, "amount": 1350}, {"id": 2, "test_id": 3, "test_name": "HbA1c", "quantity": 2, "price": 450, "amount": 900}, {"id": 3, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 2, "price": 350, "amount": 700}, {"id": 4, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 1, "price": 700, "amount": 700}], "subtotal": 3650, "discount": 266.21, "tax": 657.0, "total_amount": 4040.79, "paid_amount": 3191.34, "balance": 849.*************, "payment_method": "Card", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-03-24", "due_date": "2025-04-23", "created_at": "2025-03-24T18:01:04.178251", "updated_at": "2025-03-24T18:01:04.178251", "tenant_id": 2, "created_by": 2}, {"id": 14, "invoice_number": "INV00014", "patient_id": 34, "items": [{"id": 1, "test_id": 4, "test_name": "Lipid Profile", "quantity": 2, "price": 600, "amount": 1200}, {"id": 2, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 1, "price": 200, "amount": 200}], "subtotal": 1400, "discount": 80.24, "tax": 252.0, "total_amount": 1571.76, "paid_amount": 1571.76, "balance": 0, "payment_method": "Card", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-05-03", "due_date": "2025-06-02", "created_at": "2025-05-03T18:01:04.178251", "updated_at": "2025-05-03T18:01:04.178251", "tenant_id": 1, "created_by": 3}, {"id": 15, "invoice_number": "INV00015", "patient_id": 9, "items": [{"id": 1, "test_id": 7, "test_name": "Thyroid Profile", "quantity": 1, "price": 850, "amount": 850}, {"id": 2, "test_id": 9, "test_name": "Stool Routine", "quantity": 3, "price": 250, "amount": 750}], "subtotal": 1600, "discount": 292.69, "tax": 288.0, "total_amount": 1595.31, "paid_amount": 0, "balance": 1595.31, "payment_method": "Bank Transfer", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-04-15", "due_date": "2025-05-15", "created_at": "2025-04-15T18:01:04.178251", "updated_at": "2025-04-15T18:01:04.178251", "tenant_id": 3, "created_by": 2}, {"id": 16, "invoice_number": "INV00016", "patient_id": 26, "items": [{"id": 1, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}], "subtotal": 450, "discount": 85.85, "tax": 81.0, "total_amount": 445.15, "paid_amount": 445.15, "balance": 0, "payment_method": "UPI", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-06-09", "due_date": "2025-07-09", "created_at": "2025-06-09T18:01:04.178251", "updated_at": "2025-06-09T18:01:04.178251", "tenant_id": 3, "created_by": 2}, {"id": 17, "invoice_number": "INV00017", "patient_id": 50, "items": [{"id": 1, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 3, "price": 700, "amount": 2100}, {"id": 2, "test_id": 7, "test_name": "Thyroid Profile", "quantity": 2, "price": 850, "amount": 1700}], "subtotal": 3800, "discount": 186.84, "tax": 684.0, "total_amount": 4297.16, "paid_amount": 3476.62, "balance": 820.54, "payment_method": "Card", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-04-07", "due_date": "2025-05-07", "created_at": "2025-04-07T18:01:04.178251", "updated_at": "2025-04-07T18:01:04.178251", "tenant_id": 3, "created_by": 1}, {"id": 18, "invoice_number": "INV00018", "patient_id": 46, "items": [{"id": 1, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 2, "price": 700, "amount": 1400}], "subtotal": 1400, "discount": 104.74, "tax": 252.0, "total_amount": 1547.26, "paid_amount": 1326.93, "balance": 220.32999999999993, "payment_method": "Cash", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-05-04", "due_date": "2025-06-03", "created_at": "2025-05-04T18:01:04.178251", "updated_at": "2025-05-04T18:01:04.178251", "tenant_id": 1, "created_by": 3}, {"id": 19, "invoice_number": "INV00019", "patient_id": 24, "items": [{"id": 1, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 1, "price": 700, "amount": 700}], "subtotal": 700, "discount": 30.45, "tax": 126.0, "total_amount": 795.55, "paid_amount": 795.55, "balance": 0, "payment_method": "UPI", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-03-23", "due_date": "2025-04-22", "created_at": "2025-03-23T18:01:04.178251", "updated_at": "2025-03-23T18:01:04.178251", "tenant_id": 1, "created_by": 1}, {"id": 20, "invoice_number": "INV00020", "patient_id": 11, "items": [{"id": 1, "test_id": 3, "test_name": "HbA1c", "quantity": 3, "price": 450, "amount": 1350}, {"id": 2, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 3, "price": 700, "amount": 2100}], "subtotal": 3450, "discount": 618.12, "tax": 621.0, "total_amount": 3452.88, "paid_amount": 3452.88, "balance": 0, "payment_method": "Card", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-03-16", "due_date": "2025-04-15", "created_at": "2025-03-16T18:01:04.178251", "updated_at": "2025-03-16T18:01:04.178251", "tenant_id": 3, "created_by": 1}, {"id": 21, "invoice_number": "INV00021", "patient_id": 36, "items": [{"id": 1, "test_id": 9, "test_name": "Stool Routine", "quantity": 3, "price": 250, "amount": 750}, {"id": 2, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 3, "price": 700, "amount": 2100}, {"id": 3, "test_id": 7, "test_name": "Thyroid Profile", "quantity": 3, "price": 850, "amount": 2550}, {"id": 4, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 1, "price": 350, "amount": 350}], "subtotal": 5750, "discount": 158.98, "tax": 1035.0, "total_amount": 6626.02, "paid_amount": 6626.02, "balance": 0, "payment_method": "UPI", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-30", "due_date": "2025-05-30", "created_at": "2025-04-30T18:01:04.178251", "updated_at": "2025-04-30T18:01:04.178251", "tenant_id": 1, "created_by": 2}, {"id": 22, "invoice_number": "INV00022", "patient_id": 34, "items": [{"id": 1, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}, {"id": 2, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 2, "price": 350, "amount": 700}], "subtotal": 1150, "discount": 19.7, "tax": 207.0, "total_amount": 1337.3, "paid_amount": 0, "balance": 1337.3, "payment_method": "UPI", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-04-23", "due_date": "2025-05-23", "created_at": "2025-04-23T18:01:04.178251", "updated_at": "2025-04-23T18:01:04.178251", "tenant_id": 1, "created_by": 3}, {"id": 23, "invoice_number": "INV00023", "patient_id": 33, "items": [{"id": 1, "test_id": 3, "test_name": "HbA1c", "quantity": 2, "price": 450, "amount": 900}, {"id": 2, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 1, "price": 150, "amount": 150}, {"id": 3, "test_id": 5, "test_name": "Liver Function Test", "quantity": 1, "price": 800, "amount": 800}], "subtotal": 1850, "discount": 339.35, "tax": 333.0, "total_amount": 1843.65, "paid_amount": 0, "balance": 1843.65, "payment_method": "Card", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-13", "due_date": "2025-07-13", "created_at": "2025-06-13T18:01:04.178251", "updated_at": "2025-06-13T18:01:04.178251", "tenant_id": 2, "created_by": 3}, {"id": 24, "invoice_number": "INV00024", "patient_id": 18, "items": [{"id": 1, "test_id": 4, "test_name": "Lipid Profile", "quantity": 2, "price": 600, "amount": 1200}, {"id": 2, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}, {"id": 3, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 3, "price": 200, "amount": 600}, {"id": 4, "test_id": 4, "test_name": "Lipid Profile", "quantity": 1, "price": 600, "amount": 600}], "subtotal": 2850, "discount": 347.02, "tax": 513.0, "total_amount": 3015.98, "paid_amount": 0, "balance": 3015.98, "payment_method": "Card", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-05-19", "due_date": "2025-06-18", "created_at": "2025-05-19T18:01:04.178251", "updated_at": "2025-05-19T18:01:04.178251", "tenant_id": 2, "created_by": 3}, {"id": 25, "invoice_number": "INV00025", "patient_id": 29, "items": [{"id": 1, "test_id": 4, "test_name": "Lipid Profile", "quantity": 3, "price": 600, "amount": 1800}, {"id": 2, "test_id": 3, "test_name": "HbA1c", "quantity": 3, "price": 450, "amount": 1350}, {"id": 3, "test_id": 9, "test_name": "Stool Routine", "quantity": 2, "price": 250, "amount": 500}], "subtotal": 3650, "discount": 189.14, "tax": 657.0, "total_amount": 4117.************, "paid_amount": 0, "balance": 4117.************, "payment_method": "Bank Transfer", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-05-26", "due_date": "2025-06-25", "created_at": "2025-05-26T18:01:04.178251", "updated_at": "2025-05-26T18:01:04.178251", "tenant_id": 2, "created_by": 3}, {"id": 26, "invoice_number": "INV00026", "patient_id": 26, "items": [{"id": 1, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 2, "price": 700, "amount": 1400}, {"id": 2, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 1, "price": 350, "amount": 350}, {"id": 3, "test_id": 4, "test_name": "Lipid Profile", "quantity": 3, "price": 600, "amount": 1800}], "subtotal": 3550, "discount": 500.24, "tax": 639.0, "total_amount": 3688.76, "paid_amount": 2475.08, "balance": 1213.*************, "payment_method": "UPI", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-06-08", "due_date": "2025-07-08", "created_at": "2025-06-08T18:01:04.178251", "updated_at": "2025-06-08T18:01:04.178251", "tenant_id": 1, "created_by": 2}, {"id": 27, "invoice_number": "INV00027", "patient_id": 18, "items": [{"id": 1, "test_id": 5, "test_name": "Liver Function Test", "quantity": 1, "price": 800, "amount": 800}, {"id": 2, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 1, "price": 700, "amount": 700}], "subtotal": 1500, "discount": 2.99, "tax": 270.0, "total_amount": 1767.01, "paid_amount": 0, "balance": 1767.01, "payment_method": "Cash", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-04-25", "due_date": "2025-05-25", "created_at": "2025-04-25T18:01:04.178251", "updated_at": "2025-04-25T18:01:04.178251", "tenant_id": 3, "created_by": 3}, {"id": 28, "invoice_number": "INV00028", "patient_id": 26, "items": [{"id": 1, "test_id": 9, "test_name": "Stool Routine", "quantity": 2, "price": 250, "amount": 500}], "subtotal": 500, "discount": 67.29, "tax": 90.0, "total_amount": 522.71, "paid_amount": 522.71, "balance": 0, "payment_method": "Cash", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-03-20", "due_date": "2025-04-19", "created_at": "2025-03-20T18:01:04.178251", "updated_at": "2025-03-20T18:01:04.178251", "tenant_id": 1, "created_by": 1}, {"id": 29, "invoice_number": "INV00029", "patient_id": 48, "items": [{"id": 1, "test_id": 4, "test_name": "Lipid Profile", "quantity": 2, "price": 600, "amount": 1200}, {"id": 2, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}, {"id": 3, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 1, "price": 700, "amount": 700}, {"id": 4, "test_id": 7, "test_name": "Thyroid Profile", "quantity": 3, "price": 850, "amount": 2550}], "subtotal": 4900, "discount": 842.35, "tax": 882.0, "total_amount": 4939.65, "paid_amount": 4939.65, "balance": 0, "payment_method": "Card", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-16", "due_date": "2025-05-16", "created_at": "2025-04-16T18:01:04.178251", "updated_at": "2025-04-16T18:01:04.178251", "tenant_id": 1, "created_by": 1}, {"id": 30, "invoice_number": "INV00030", "patient_id": 28, "items": [{"id": 1, "test_id": 7, "test_name": "Thyroid Profile", "quantity": 3, "price": 850, "amount": 2550}], "subtotal": 2550, "discount": 281.56, "tax": 459.0, "total_amount": 2727.44, "paid_amount": 390.81, "balance": 2336.63, "payment_method": "Cash", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-04-24", "due_date": "2025-05-24", "created_at": "2025-04-24T18:01:04.178251", "updated_at": "2025-04-24T18:01:04.178251", "tenant_id": 2, "created_by": 3}, {"id": 31, "invoice_number": "INV00031", "sid_number": "AS001", "patient_id": 28, "items": [{"amount": 250, "id": 1750325754071, "test_id": 191, "test_name": "Red Blood Cell (RBC) Count"}], "subtotal": 250, "discount": 0, "tax": 0, "total_amount": 250, "paid_amount": 0, "balance": 250, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-19", "due_date": "2025-07-19", "notes": "", "branch": "2", "created_at": "2025-06-19T15:05:57.986293", "updated_at": "2025-06-19T15:05:57.986293", "tenant_id": 2, "created_by": 2}, {"id": 32, "invoice_number": "INV00032", "sid_number": "AS001", "patient_id": 10, "items": [{"testName": "Lipid Profile", "amount": 400, "id": 1750325790455}], "subtotal": 400, "discount": 0, "tax": 0, "total_amount": 400, "paid_amount": 0, "balance": 400, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-19", "due_date": "2025-07-19", "notes": "", "branch": "2", "created_at": "2025-06-19T15:06:32.967559", "updated_at": "2025-06-19T15:06:32.967559", "tenant_id": 2, "created_by": 2}, {"id": 33, "invoice_number": "INV00033", "sid_number": "AM001", "patient_id": 1, "items": [{"testName": "Basic Health Checkup", "amount": 1500}], "subtotal": 1500, "discount": 0, "tax": 0, "total_amount": 1500, "paid_amount": 0, "balance": 1500, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-19", "due_date": "2025-07-19", "notes": "", "branch": 1, "created_at": "2025-06-19T15:31:23.854855", "updated_at": "2025-06-19T15:31:23.854855", "tenant_id": 1, "created_by": 19}, {"id": 34, "invoice_number": "INV00034", "sid_number": "AM002", "patient_id": 36, "items": [{"amount": 800, "id": 1750327415251, "test_id": 2, "test_name": "17 - HYDROXY PROGESTERONE"}], "subtotal": 800, "discount": 0, "tax": 0, "total_amount": 800, "paid_amount": 0, "balance": 800, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-19", "due_date": "2025-07-19", "notes": "", "branch": "1", "created_at": "2025-06-19T15:33:37.834956", "updated_at": "2025-06-19T15:33:37.834956", "tenant_id": 1, "created_by": 19}, {"id": 35, "invoice_number": "INV00035", "sid_number": "KTL362", "patient_id": 65, "items": [{"amount": 1200, "id": 1750327882833, "test_id": 3, "test_name": "25 Hydroxy Vitamin D3"}], "subtotal": 1200, "discount": 0, "tax": 0, "total_amount": 1200, "paid_amount": 0, "balance": 1200, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-19", "due_date": "2025-07-19", "notes": "", "branch": "4", "created_at": "2025-06-19T15:41:26.023527", "updated_at": "2025-06-19T15:41:26.023527", "tenant_id": 1, "created_by": 19}, {"id": 36, "invoice_number": "INV00036", "sid_number": "AK001", "patient_id": 64, "items": [{"amount": 800, "id": 1750328090185, "test_id": 2, "test_name": "17 - HYDROXY PROGESTERONE"}], "subtotal": 800, "discount": 0, "tax": 0, "total_amount": 800, "paid_amount": 0, "balance": 800, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-19", "due_date": "2025-07-19", "notes": "", "branch": "4", "created_at": "2025-06-19T15:44:55.998175", "updated_at": "2025-06-19T15:44:55.998175", "tenant_id": 1, "created_by": 19}, {"id": 37, "invoice_number": "INV00037", "sid_number": "AM003", "patient_id": 7, "items": [{"amount": 4000, "id": 1750329190646, "test_id": 6, "test_name": "5-HIAA (Hydroxy Indole Acetic Acid), Urine 24H"}], "bill_amount": 4000, "other_charges": 0, "discount_percent": 0, "subtotal": 4720, "discount": 0, "gst_rate": 18, "gst_amount": 720, "tax": 720, "total_amount": 4720, "paid_amount": 0, "balance": 4720, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-19", "due_date": "2025-07-19", "notes": "", "branch": "1", "created_at": "2025-06-19T16:03:15.913369", "updated_at": "2025-06-19T16:03:15.913369", "tenant_id": 1, "created_by": 19}, {"id": 38, "invoice_number": "INV00038", "sid_number": "AT001", "patient_id": 24, "items": [{"amount": 800, "id": 1750336275604, "test_id": 2, "test_name": "17 - HYDROXY PROGESTERONE"}], "bill_amount": 800, "other_charges": 0, "discount_percent": 0, "subtotal": 944, "discount": 0, "gst_rate": 18, "gst_amount": 144, "tax": 144, "total_amount": 944, "paid_amount": 0, "balance": 944, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-19", "due_date": "2025-07-19", "notes": "", "branch": "3", "created_at": "2025-06-19T18:01:27.450140", "updated_at": "2025-06-19T18:01:27.450140", "tenant_id": 1, "created_by": 19}, {"id": 39, "invoice_number": "INV00039", "sid_number": "AM004", "patient_id": 5, "items": [{"amount": 800, "id": 1750393253676, "test_id": 2, "test_name": "17 - HYDROXY PROGESTERONE"}], "bill_amount": 800, "other_charges": 0, "discount_percent": 0, "subtotal": 944, "discount": 0, "gst_rate": 18, "gst_amount": 144, "tax": 144, "total_amount": 944, "paid_amount": 0, "balance": 944, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-20", "due_date": "2025-07-20", "notes": "", "branch": "1", "created_at": "2025-06-20T09:50:56.558988", "updated_at": "2025-06-20T09:50:56.558988", "tenant_id": 1, "created_by": 19}, {"id": 40, "invoice_number": "INV00040", "sid_number": "AM005", "patient_id": 5, "items": [{"amount": 800, "id": 1750393935793, "test_id": 2, "test_name": "17 - HYDROXY PROGESTERONE"}], "bill_amount": 800, "other_charges": 0, "discount_percent": 0, "subtotal": 944, "discount": 0, "gst_rate": 18, "gst_amount": 144, "tax": 144, "total_amount": 944, "paid_amount": 0, "balance": 944, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-20", "due_date": "2025-07-20", "notes": "", "branch": "1", "created_at": "2025-06-20T10:02:18.154777", "updated_at": "2025-06-20T10:02:18.154777", "tenant_id": 1, "created_by": 19}]